/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { FirebasePerformance, PerformanceTrace } from '@firebase/performance';
import { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';
import { FirebaseApp, _FirebaseService } from '@firebase/app-compat';
export declare class PerformanceCompatImpl implements FirebasePerformanceCompat, _FirebaseService {
    app: FirebaseApp;
    readonly _delegate: FirebasePerformance;
    constructor(app: FirebaseApp, _delegate: FirebasePerformance);
    get instrumentationEnabled(): boolean;
    set instrumentationEnabled(val: boolean);
    get dataCollectionEnabled(): boolean;
    set dataCollectionEnabled(val: boolean);
    trace(traceName: string): PerformanceTrace;
}

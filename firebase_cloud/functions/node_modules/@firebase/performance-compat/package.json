{"name": "@firebase/performance-compat", "version": "0.2.21", "description": "The compatibility package of Firebase Performance", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm.js", "module": "dist/esm/index.esm.js", "exports": {".": {"types": "./dist/src/index.d.ts", "require": "./dist/index.cjs.js", "default": "./dist/esm/index.esm.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts'  --ignore-path '../../.gitignore'", "build": "rollup -c", "build:release": "rollup -c rollup.config.release.js && yarn add-compat-overloads", "build:deps": "lerna run --scope @firebase/performance-compat --include-dependencies build", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:all", "test:all": "run-p --npm-path npm test:browser", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:all", "test:browser": "karma start", "test:browser:debug": "karma start --browsers Chrome --auto-watch", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "prettier": "prettier --write '{src,test}/**/*.{js,ts}'", "add-compat-overloads": "ts-node-script ../../scripts/build/create-overloads.ts -i ../performance/dist/src/index.d.ts -o dist/src/index.d.ts -a -r FirebasePerformance:FirebasePerformanceCompat -r FirebaseApp:FirebaseAppCompat --moduleToEnhance @firebase/performance"}, "license": "Apache-2.0", "peerDependencies": {"@firebase/app-compat": "0.x"}, "dependencies": {"@firebase/performance": "0.7.8", "@firebase/performance-types": "0.2.3", "@firebase/util": "1.13.0", "@firebase/logger": "0.5.0", "@firebase/component": "0.7.0", "tslib": "^2.1.0"}, "devDependencies": {"rollup": "2.79.2", "@rollup/plugin-json": "6.1.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-typescript2": "0.36.0", "typescript": "5.5.4", "@firebase/app-compat": "0.5.0"}, "repository": {"directory": "packages/performance-compat", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "dist/src/index.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}}
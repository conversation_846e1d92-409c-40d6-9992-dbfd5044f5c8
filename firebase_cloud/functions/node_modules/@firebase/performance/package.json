{"name": "@firebase/performance", "version": "0.7.8", "description": "Firebase performance for web", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm.js", "module": "dist/esm/index.esm.js", "exports": {".": {"types": "./dist/src/index.d.ts", "require": "./dist/index.cjs.js", "default": "./dist/esm/index.esm.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c && yarn api-report", "build:deps": "lerna run --scope @firebase/performance --include-dependencies build", "build:release": "yarn build", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:browser", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:browser", "test:browser": "karma start", "test:debug": "karma start --browsers=Chrome --auto-watch", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "prettier": "prettier --write '{src,test}/**/*.{js,ts}'", "api-report": "api-extractor run --local --verbose", "doc": "api-documenter markdown --input temp --output docs", "build:doc": "yarn build && yarn doc"}, "peerDependencies": {"@firebase/app": "0.x"}, "dependencies": {"@firebase/logger": "0.5.0", "@firebase/installations": "0.6.19", "@firebase/util": "1.13.0", "@firebase/component": "0.7.0", "tslib": "^2.1.0", "web-vitals": "^4.2.4"}, "license": "Apache-2.0", "devDependencies": {"@firebase/app": "0.14.0", "rollup": "2.79.2", "@rollup/plugin-json": "6.1.0", "rollup-plugin-typescript2": "0.36.0", "typescript": "5.5.4"}, "repository": {"directory": "packages/performance", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "dist/src/index.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}}
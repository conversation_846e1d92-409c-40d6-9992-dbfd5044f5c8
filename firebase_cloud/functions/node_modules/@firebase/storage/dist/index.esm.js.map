{"version": 3, "file": "index.esm.js", "sources": ["../src/implementation/constants.ts", "../src/implementation/error.ts", "../src/implementation/location.ts", "../src/implementation/failrequest.ts", "../src/implementation/backoff.ts", "../src/implementation/type.ts", "../src/implementation/url.ts", "../src/implementation/connection.ts", "../src/implementation/utils.ts", "../src/implementation/request.ts", "../src/implementation/fs.ts", "../src/platform/browser/base64.ts", "../src/implementation/string.ts", "../src/implementation/blob.ts", "../src/implementation/json.ts", "../src/implementation/path.ts", "../src/implementation/metadata.ts", "../src/implementation/list.ts", "../src/implementation/requestinfo.ts", "../src/implementation/requests.ts", "../src/implementation/taskenums.ts", "../src/implementation/observer.ts", "../src/implementation/async.ts", "../src/platform/browser/connection.ts", "../src/task.ts", "../src/reference.ts", "../src/service.ts", "../src/constants.ts", "../src/api.ts", "../src/api.browser.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n\n/**\n * Domain name for firebase storage.\n */\nexport const DEFAULT_HOST = 'firebasestorage.googleapis.com';\n\n/**\n * The key in Firebase config json for the storage bucket.\n */\nexport const CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nexport const DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nexport const DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n\n/**\n * 1 second\n */\nexport const DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * This is the value of Number.MIN_SAFE_INTEGER, which is not well supported\n * enough for us to use it directly.\n */\nexport const MIN_SAFE_INTEGER = -9007199254740991;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport { CONFIG_STORAGE_BUCKET_KEY } from './constants';\n\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nexport class StorageError extends FirebaseError {\n  private readonly _baseMessage: string;\n  /**\n   * Stores custom error data unique to the `StorageError`.\n   */\n  customData: { serverResponse: string | null } = { serverResponse: null };\n\n  /**\n   * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n   *  added to the end of the message.\n   * @param message  - Error message.\n   * @param status_ - Corresponding HTTP Status Code\n   */\n  constructor(code: StorageErrorCode, message: string, private status_ = 0) {\n    super(\n      prependCode(code),\n      `Firebase Storage: ${message} (${prependCode(code)})`\n    );\n    this._baseMessage = this.message;\n    // Without this, `instanceof StorageError`, in tests for example,\n    // returns false.\n    Object.setPrototypeOf(this, StorageError.prototype);\n  }\n\n  get status(): number {\n    return this.status_;\n  }\n\n  set status(status: number) {\n    this.status_ = status;\n  }\n\n  /**\n   * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n   */\n  _codeEquals(code: StorageErrorCode): boolean {\n    return prependCode(code) === this.code;\n  }\n\n  /**\n   * Optional response message that was added by the server.\n   */\n  get serverResponse(): null | string {\n    return this.customData.serverResponse;\n  }\n\n  set serverResponse(serverResponse: string | null) {\n    this.customData.serverResponse = serverResponse;\n    if (this.customData.serverResponse) {\n      this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n    } else {\n      this.message = this._baseMessage;\n    }\n  }\n}\n\nexport const errors = {};\n\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nexport enum StorageErrorCode {\n  // Shared between all platforms\n  UNKNOWN = 'unknown',\n  OBJECT_NOT_FOUND = 'object-not-found',\n  BUCKET_NOT_FOUND = 'bucket-not-found',\n  PROJECT_NOT_FOUND = 'project-not-found',\n  QUOTA_EXCEEDED = 'quota-exceeded',\n  UNAUTHENTICATED = 'unauthenticated',\n  UNAUTHORIZED = 'unauthorized',\n  UNAUTHORIZED_APP = 'unauthorized-app',\n  RETRY_LIMIT_EXCEEDED = 'retry-limit-exceeded',\n  INVALID_CHECKSUM = 'invalid-checksum',\n  CANCELED = 'canceled',\n  // JS specific\n  INVALID_EVENT_NAME = 'invalid-event-name',\n  INVALID_URL = 'invalid-url',\n  INVALID_DEFAULT_BUCKET = 'invalid-default-bucket',\n  NO_DEFAULT_BUCKET = 'no-default-bucket',\n  CANNOT_SLICE_BLOB = 'cannot-slice-blob',\n  SERVER_FILE_WRONG_SIZE = 'server-file-wrong-size',\n  NO_DOWNLOAD_URL = 'no-download-url',\n  INVALID_ARGUMENT = 'invalid-argument',\n  INVALID_ARGUMENT_COUNT = 'invalid-argument-count',\n  APP_DELETED = 'app-deleted',\n  INVALID_ROOT_OPERATION = 'invalid-root-operation',\n  INVALID_FORMAT = 'invalid-format',\n  INTERNAL_ERROR = 'internal-error',\n  UNSUPPORTED_ENVIRONMENT = 'unsupported-environment'\n}\n\nexport function prependCode(code: StorageErrorCode): string {\n  return 'storage/' + code;\n}\n\nexport function unknown(): StorageError {\n  const message =\n    'An unknown error occurred, please check the error payload for ' +\n    'server response.';\n  return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\n\nexport function objectNotFound(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.OBJECT_NOT_FOUND,\n    \"Object '\" + path + \"' does not exist.\"\n  );\n}\n\nexport function bucketNotFound(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.BUCKET_NOT_FOUND,\n    \"Bucket '\" + bucket + \"' does not exist.\"\n  );\n}\n\nexport function projectNotFound(project: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.PROJECT_NOT_FOUND,\n    \"Project '\" + project + \"' does not exist.\"\n  );\n}\n\nexport function quotaExceeded(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.QUOTA_EXCEEDED,\n    \"Quota for bucket '\" +\n      bucket +\n      \"' exceeded, please view quota on \" +\n      'https://firebase.google.com/pricing/.'\n  );\n}\n\nexport function unauthenticated(): StorageError {\n  const message =\n    'User is not authenticated, please authenticate using Firebase ' +\n    'Authentication and try again.';\n  return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\n\nexport function unauthorizedApp(): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED_APP,\n    'This app does not have permission to access Firebase Storage on this project.'\n  );\n}\n\nexport function unauthorized(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED,\n    \"User does not have permission to access '\" + path + \"'.\"\n  );\n}\n\nexport function retryLimitExceeded(): StorageError {\n  return new StorageError(\n    StorageErrorCode.RETRY_LIMIT_EXCEEDED,\n    'Max retry time for operation exceeded, please try again.'\n  );\n}\n\nexport function invalidChecksum(\n  path: string,\n  checksum: string,\n  calculated: string\n): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_CHECKSUM,\n    \"Uploaded/downloaded object '\" +\n      path +\n      \"' has checksum '\" +\n      checksum +\n      \"' which does not match '\" +\n      calculated +\n      \"'. Please retry the upload/download.\"\n  );\n}\n\nexport function canceled(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANCELED,\n    'User canceled the upload/download.'\n  );\n}\n\nexport function invalidEventName(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_EVENT_NAME,\n    \"Invalid event name '\" + name + \"'.\"\n  );\n}\n\nexport function invalidUrl(url: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_URL,\n    \"Invalid URL '\" + url + \"'.\"\n  );\n}\n\nexport function invalidDefaultBucket(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_DEFAULT_BUCKET,\n    \"Invalid default bucket '\" + bucket + \"'.\"\n  );\n}\n\nexport function noDefaultBucket(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DEFAULT_BUCKET,\n    'No default bucket ' +\n      \"found. Did you set the '\" +\n      CONFIG_STORAGE_BUCKET_KEY +\n      \"' property when initializing the app?\"\n  );\n}\n\nexport function cannotSliceBlob(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANNOT_SLICE_BLOB,\n    'Cannot slice blob for upload. Please retry the upload.'\n  );\n}\n\nexport function serverFileWrongSize(): StorageError {\n  return new StorageError(\n    StorageErrorCode.SERVER_FILE_WRONG_SIZE,\n    'Server recorded incorrect upload file size, please retry the upload.'\n  );\n}\n\nexport function noDownloadURL(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DOWNLOAD_URL,\n    'The given file does not have any download URLs.'\n  );\n}\n\nexport function missingPolyFill(polyFill: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n    `${polyFill} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`\n  );\n}\n\n/**\n * @internal\n */\nexport function invalidArgument(message: string): StorageError {\n  return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\n\nexport function invalidArgumentCount(\n  argMin: number,\n  argMax: number,\n  fnName: string,\n  real: number\n): StorageError {\n  let countPart;\n  let plural;\n  if (argMin === argMax) {\n    countPart = argMin;\n    plural = argMin === 1 ? 'argument' : 'arguments';\n  } else {\n    countPart = 'between ' + argMin + ' and ' + argMax;\n    plural = 'arguments';\n  }\n  return new StorageError(\n    StorageErrorCode.INVALID_ARGUMENT_COUNT,\n    'Invalid argument count in `' +\n      fnName +\n      '`: Expected ' +\n      countPart +\n      ' ' +\n      plural +\n      ', received ' +\n      real +\n      '.'\n  );\n}\n\nexport function appDeleted(): StorageError {\n  return new StorageError(\n    StorageErrorCode.APP_DELETED,\n    'The Firebase app was deleted.'\n  );\n}\n\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nexport function invalidRootOperation(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_ROOT_OPERATION,\n    \"The operation '\" +\n      name +\n      \"' cannot be performed on a root reference, create a non-root \" +\n      \"reference using child, such as .child('file.png').\"\n  );\n}\n\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nexport function invalidFormat(format: string, message: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_FORMAT,\n    \"String does not match format '\" + format + \"': \" + message\n  );\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function unsupportedEnvironment(message: string): StorageError {\n  throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, message);\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function internalError(message: string): StorageError {\n  throw new StorageError(\n    StorageErrorCode.INTERNAL_ERROR,\n    'Internal error: ' + message\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functionality related to the parsing/composition of bucket/\n * object location.\n */\n\nimport { invalidDefaultBucket, invalidUrl } from './error';\nimport { DEFAULT_HOST } from './constants';\n\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nexport class Location {\n  private path_: string;\n\n  constructor(public readonly bucket: string, path: string) {\n    this.path_ = path;\n  }\n\n  get path(): string {\n    return this.path_;\n  }\n\n  get isRoot(): boolean {\n    return this.path.length === 0;\n  }\n\n  fullServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n  }\n\n  bucketOnlyServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o';\n  }\n\n  static makeFromBucketSpec(bucketString: string, host: string): Location {\n    let bucketLocation;\n    try {\n      bucketLocation = Location.makeFromUrl(bucketString, host);\n    } catch (e) {\n      // Not valid URL, use as-is. This lets you put bare bucket names in\n      // config.\n      return new Location(bucketString, '');\n    }\n    if (bucketLocation.path === '') {\n      return bucketLocation;\n    } else {\n      throw invalidDefaultBucket(bucketString);\n    }\n  }\n\n  static makeFromUrl(url: string, host: string): Location {\n    let location: Location | null = null;\n    const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n\n    function gsModify(loc: Location): void {\n      if (loc.path.charAt(loc.path.length - 1) === '/') {\n        loc.path_ = loc.path_.slice(0, -1);\n      }\n    }\n    const gsPath = '(/(.*))?$';\n    const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n    const gsIndices = { bucket: 1, path: 3 };\n\n    function httpModify(loc: Location): void {\n      loc.path_ = decodeURIComponent(loc.path);\n    }\n    const version = 'v[A-Za-z0-9_]+';\n    const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n    const firebaseStoragePath = '(/([^?#]*).*)?$';\n    const firebaseStorageRegExp = new RegExp(\n      `^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`,\n      'i'\n    );\n    const firebaseStorageIndices = { bucket: 1, path: 3 };\n\n    const cloudStorageHost =\n      host === DEFAULT_HOST\n        ? '(?:storage.googleapis.com|storage.cloud.google.com)'\n        : host;\n    const cloudStoragePath = '([^?#]*)';\n    const cloudStorageRegExp = new RegExp(\n      `^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`,\n      'i'\n    );\n    const cloudStorageIndices = { bucket: 1, path: 2 };\n\n    const groups = [\n      { regex: gsRegex, indices: gsIndices, postModify: gsModify },\n      {\n        regex: firebaseStorageRegExp,\n        indices: firebaseStorageIndices,\n        postModify: httpModify\n      },\n      {\n        regex: cloudStorageRegExp,\n        indices: cloudStorageIndices,\n        postModify: httpModify\n      }\n    ];\n    for (let i = 0; i < groups.length; i++) {\n      const group = groups[i];\n      const captures = group.regex.exec(url);\n      if (captures) {\n        const bucketValue = captures[group.indices.bucket];\n        let pathValue = captures[group.indices.path];\n        if (!pathValue) {\n          pathValue = '';\n        }\n        location = new Location(bucketValue, pathValue);\n        group.postModify(location);\n        break;\n      }\n    }\n    if (location == null) {\n      throw invalidUrl(url);\n    }\n    return location;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Request } from './request';\n\n/**\n * A request whose promise always fails.\n */\nexport class FailRequest<T> implements Request<T> {\n  promise_: Promise<T>;\n\n  constructor(error: StorageError) {\n    this.promise_ = Promise.reject<T>(error);\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<T> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(_appDelete = false): void {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Provides a method for running a function with exponential\n * backoff.\n */\ntype id = (p1: boolean) => void;\n\nexport { id };\n\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nexport function start(\n  doRequest: (\n    onRequestComplete: (success: boolean) => void,\n    canceled: boolean\n  ) => void,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  backoffCompleteCb: (...args: any[]) => unknown,\n  timeout: number\n): id {\n  // TODO(andysoto): make this code cleaner (probably refactor into an actual\n  // type instead of a bunch of functions with state shared in the closure)\n  let waitSeconds = 1;\n  // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n  // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let retryTimeoutId: any = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let globalTimeoutId: any = null;\n  let hitTimeout = false;\n  let cancelState = 0;\n\n  function canceled(): boolean {\n    return cancelState === 2;\n  }\n  let triggeredCallback = false;\n\n  function triggerCallback(...args: any[]): void {\n    if (!triggeredCallback) {\n      triggeredCallback = true;\n      backoffCompleteCb.apply(null, args);\n    }\n  }\n\n  function callWithDelay(millis: number): void {\n    retryTimeoutId = setTimeout(() => {\n      retryTimeoutId = null;\n      doRequest(responseHandler, canceled());\n    }, millis);\n  }\n\n  function clearGlobalTimeout(): void {\n    if (globalTimeoutId) {\n      clearTimeout(globalTimeoutId);\n    }\n  }\n\n  function responseHandler(success: boolean, ...args: any[]): void {\n    if (triggeredCallback) {\n      clearGlobalTimeout();\n      return;\n    }\n    if (success) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    const mustStop = canceled() || hitTimeout;\n    if (mustStop) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    if (waitSeconds < 64) {\n      /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n      waitSeconds *= 2;\n    }\n    let waitMillis;\n    if (cancelState === 1) {\n      cancelState = 2;\n      waitMillis = 0;\n    } else {\n      waitMillis = (waitSeconds + Math.random()) * 1000;\n    }\n    callWithDelay(waitMillis);\n  }\n  let stopped = false;\n\n  function stop(wasTimeout: boolean): void {\n    if (stopped) {\n      return;\n    }\n    stopped = true;\n    clearGlobalTimeout();\n    if (triggeredCallback) {\n      return;\n    }\n    if (retryTimeoutId !== null) {\n      if (!wasTimeout) {\n        cancelState = 2;\n      }\n      clearTimeout(retryTimeoutId);\n      callWithDelay(0);\n    } else {\n      if (!wasTimeout) {\n        cancelState = 1;\n      }\n    }\n  }\n  callWithDelay(0);\n  globalTimeoutId = setTimeout(() => {\n    hitTimeout = true;\n    stop(true);\n  }, timeout);\n  return stop;\n}\n\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nexport function stop(id: id): void {\n  id(false);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { invalidArgument } from './error';\n\nexport function isJustDef<T>(p: T | null | undefined): p is T | null {\n  return p !== void 0;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function isFunction(p: unknown): p is Function {\n  return typeof p === 'function';\n}\n\nexport function isNonArrayObject(p: unknown): boolean {\n  return typeof p === 'object' && !Array.isArray(p);\n}\n\nexport function isString(p: unknown): p is string {\n  return typeof p === 'string' || p instanceof String;\n}\n\nexport function isNativeBlob(p: unknown): p is Blob {\n  return isNativeBlobDefined() && p instanceof Blob;\n}\n\nexport function isNativeBlobDefined(): boolean {\n  return typeof Blob !== 'undefined';\n}\n\nexport function validateNumber(\n  argument: string,\n  minValue: number,\n  maxValue: number,\n  value: number\n): void {\n  if (value < minValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${minValue} or greater.`\n    );\n  }\n  if (value > maxValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${maxValue} or less.`\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functions to create and manipulate URLs for the server API.\n */\nimport { UrlParams } from './requestinfo';\n\nexport function makeUrl(\n  urlPart: string,\n  host: string,\n  protocol: string\n): string {\n  let origin = host;\n  if (protocol == null) {\n    origin = `https://${host}`;\n  }\n  return `${protocol}://${origin}/v0${urlPart}`;\n}\n\nexport function makeQueryString(params: UrlParams): string {\n  const encode = encodeURIComponent;\n  let queryPart = '?';\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      const nextPart = encode(key) + '=' + encode(params[key]);\n      queryPart = queryPart + nextPart + '&';\n    }\n  }\n\n  // Chop off the extra '&' or '?' on the end\n  queryPart = queryPart.slice(0, -1);\n  return queryPart;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Network headers */\nexport type Headers = Record<string, string>;\n\n/** Response type exposed by the networking APIs. */\nexport type ConnectionType =\n  | string\n  | ArrayBuffer\n  | Blob\n  | ReadableStream<Uint8Array>;\n\n/**\n * A lightweight wrapper around XMLHttpRequest with a\n * goog.net.XhrIo-like interface.\n *\n * You can create a new connection by invoking `newTextConnection()`,\n * `newBytesConnection()` or `newStreamConnection()`.\n */\nexport interface Connection<T extends ConnectionType> {\n  /**\n   * Sends a request to the provided URL.\n   *\n   * This method never rejects its promise. In case of encountering an error,\n   * it sets an error code internally which can be accessed by calling\n   * getErrorCode() by callers.\n   */\n  send(\n    url: string,\n    method: string,\n    isUsingEmulator: boolean,\n    body?: ArrayBufferView | Blob | string | null,\n    headers?: Headers\n  ): Promise<void>;\n\n  getErrorCode(): ErrorCode;\n\n  getStatus(): number;\n\n  getResponse(): T;\n\n  getErrorText(): string;\n\n  /**\n   * Abort the request.\n   */\n  abort(): void;\n\n  getResponseHeader(header: string): string | null;\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nexport enum ErrorCode {\n  NO_ERROR = 0,\n  NETWORK_ERROR = 1,\n  ABORT = 2\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nexport function isRetryStatusCode(\n  status: number,\n  additionalRetryCodes: number[]\n): boolean {\n  // The codes for which to retry came from this page:\n  // https://cloud.google.com/storage/docs/exponential-backoff\n  const isFiveHundredCode = status >= 500 && status < 600;\n  const extraRetryCodes = [\n    // Request Timeout: web server didn't receive full request in time.\n    408,\n    // Too Many Requests: you're getting rate-limited, basically.\n    429\n  ];\n  const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n  const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n  return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods used to actually send HTTP requests from\n * abstract representations.\n */\n\nimport { id as backoffId, start, stop } from './backoff';\nimport { appDeleted, canceled, retryLimitExceeded, unknown } from './error';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RequestHandler, RequestInfo } from './requestinfo';\nimport { isJustDef } from './type';\nimport { makeQueryString } from './url';\nimport { Connection, ErrorCode, Headers, ConnectionType } from './connection';\nimport { isRetryStatusCode } from './utils';\n\nexport interface Request<T> {\n  getPromise(): Promise<T>;\n\n  /**\n   * Cancels the request. IMPORTANT: the promise may still be resolved with an\n   * appropriate value (if the request is finished before you call this method,\n   * but the promise has not yet been resolved), so don't just assume it will be\n   * rejected if you call this function.\n   * @param appDelete - True if the cancelation came from the app being deleted.\n   */\n  cancel(appDelete?: boolean): void;\n}\n\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest<I extends ConnectionType, O> implements Request<O> {\n  private pendingConnection_: Connection<I> | null = null;\n  private backoffId_: backoffId | null = null;\n  private resolve_!: (value?: O | PromiseLike<O>) => void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private reject_!: (reason?: any) => void;\n  private canceled_: boolean = false;\n  private appDelete_: boolean = false;\n  private promise_: Promise<O>;\n\n  constructor(\n    private url_: string,\n    private method_: string,\n    private headers_: Headers,\n    private body_: string | Blob | Uint8Array | null,\n    private successCodes_: number[],\n    private additionalRetryCodes_: number[],\n    private callback_: RequestHandler<I, O>,\n    private errorCallback_: ErrorHandler | null,\n    private timeout_: number,\n    private progressCallback_: ((p1: number, p2: number) => void) | null,\n    private connectionFactory_: () => Connection<I>,\n    private retry = true,\n    private isUsingEmulator = false\n  ) {\n    this.promise_ = new Promise((resolve, reject) => {\n      this.resolve_ = resolve as (value?: O | PromiseLike<O>) => void;\n      this.reject_ = reject;\n      this.start_();\n    });\n  }\n\n  /**\n   * Actually starts the retry loop.\n   */\n  private start_(): void {\n    const doTheRequest: (\n      backoffCallback: (success: boolean, ...p2: unknown[]) => void,\n      canceled: boolean\n    ) => void = (backoffCallback, canceled) => {\n      if (canceled) {\n        backoffCallback(false, new RequestEndStatus(false, null, true));\n        return;\n      }\n      const connection = this.connectionFactory_();\n      this.pendingConnection_ = connection;\n\n      const progressListener: (\n        progressEvent: ProgressEvent\n      ) => void = progressEvent => {\n        const loaded = progressEvent.loaded;\n        const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n        if (this.progressCallback_ !== null) {\n          this.progressCallback_(loaded, total);\n        }\n      };\n      if (this.progressCallback_ !== null) {\n        connection.addUploadProgressListener(progressListener);\n      }\n\n      // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      connection\n        .send(\n          this.url_,\n          this.method_,\n          this.isUsingEmulator,\n          this.body_,\n          this.headers_\n        )\n        .then(() => {\n          if (this.progressCallback_ !== null) {\n            connection.removeUploadProgressListener(progressListener);\n          }\n          this.pendingConnection_ = null;\n          const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n          const status = connection.getStatus();\n          if (\n            !hitServer ||\n            (isRetryStatusCode(status, this.additionalRetryCodes_) &&\n              this.retry)\n          ) {\n            const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n            backoffCallback(\n              false,\n              new RequestEndStatus(false, null, wasCanceled)\n            );\n            return;\n          }\n          const successCode = this.successCodes_.indexOf(status) !== -1;\n          backoffCallback(true, new RequestEndStatus(successCode, connection));\n        });\n    };\n\n    /**\n     * @param requestWentThrough - True if the request eventually went\n     *     through, false if it hit the retry limit or was canceled.\n     */\n    const backoffDone: (\n      requestWentThrough: boolean,\n      status: RequestEndStatus<I>\n    ) => void = (requestWentThrough, status) => {\n      const resolve = this.resolve_;\n      const reject = this.reject_;\n      const connection = status.connection as Connection<I>;\n      if (status.wasSuccessCode) {\n        try {\n          const result = this.callback_(connection, connection.getResponse());\n          if (isJustDef(result)) {\n            resolve(result);\n          } else {\n            resolve();\n          }\n        } catch (e) {\n          reject(e);\n        }\n      } else {\n        if (connection !== null) {\n          const err = unknown();\n          err.serverResponse = connection.getErrorText();\n          if (this.errorCallback_) {\n            reject(this.errorCallback_(connection, err));\n          } else {\n            reject(err);\n          }\n        } else {\n          if (status.canceled) {\n            const err = this.appDelete_ ? appDeleted() : canceled();\n            reject(err);\n          } else {\n            const err = retryLimitExceeded();\n            reject(err);\n          }\n        }\n      }\n    };\n    if (this.canceled_) {\n      backoffDone(false, new RequestEndStatus(false, null, true));\n    } else {\n      this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n    }\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<O> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(appDelete?: boolean): void {\n    this.canceled_ = true;\n    this.appDelete_ = appDelete || false;\n    if (this.backoffId_ !== null) {\n      stop(this.backoffId_);\n    }\n    if (this.pendingConnection_ !== null) {\n      this.pendingConnection_.abort();\n    }\n  }\n}\n\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nexport class RequestEndStatus<I extends ConnectionType> {\n  /**\n   * True if the request was canceled.\n   */\n  canceled: boolean;\n\n  constructor(\n    public wasSuccessCode: boolean,\n    public connection: Connection<I> | null,\n    canceled?: boolean\n  ) {\n    this.canceled = !!canceled;\n  }\n}\n\nexport function addAuthHeader_(\n  headers: Headers,\n  authToken: string | null\n): void {\n  if (authToken !== null && authToken.length > 0) {\n    headers['Authorization'] = 'Firebase ' + authToken;\n  }\n}\n\nexport function addVersionHeader_(\n  headers: Headers,\n  firebaseVersion?: string\n): void {\n  headers['X-Firebase-Storage-Version'] =\n    'webjs/' + (firebaseVersion ?? 'AppManager');\n}\n\nexport function addGmpidHeader_(headers: Headers, appId: string | null): void {\n  if (appId) {\n    headers['X-Firebase-GMPID'] = appId;\n  }\n}\n\nexport function addAppCheckHeader_(\n  headers: Headers,\n  appCheckToken: string | null\n): void {\n  if (appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n}\n\nexport function makeRequest<I extends ConnectionType, O>(\n  requestInfo: RequestInfo<I, O>,\n  appId: string | null,\n  authToken: string | null,\n  appCheckToken: string | null,\n  requestFactory: () => Connection<I>,\n  firebaseVersion?: string,\n  retry = true,\n  isUsingEmulator = false\n): Request<O> {\n  const queryPart = makeQueryString(requestInfo.urlParams);\n  const url = requestInfo.url + queryPart;\n  const headers = Object.assign({}, requestInfo.headers);\n  addGmpidHeader_(headers, appId);\n  addAuthHeader_(headers, authToken);\n  addVersionHeader_(headers, firebaseVersion);\n  addAppCheckHeader_(headers, appCheckToken);\n  return new NetworkRequest<I, O>(\n    url,\n    requestInfo.method,\n    headers,\n    requestInfo.body,\n    requestInfo.successCodes,\n    requestInfo.additionalRetryCodes,\n    requestInfo.handler,\n    requestInfo.errorHandler,\n    requestInfo.timeout,\n    requestInfo.progressCallback,\n    requestFactory,\n    retry,\n    isUsingEmulator\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Some methods copied from goog.fs.\n * We don't include goog.fs because it pulls in a bunch of Deferred code that\n * bloats the size of the released binary.\n */\nimport { isNativeBlobDefined } from './type';\nimport { StorageErrorCode, StorageError } from './error';\n\nfunction getBlobBuilder(): typeof IBlobBuilder | undefined {\n  if (typeof BlobBuilder !== 'undefined') {\n    return BlobBuilder;\n  } else if (typeof WebKitBlobBuilder !== 'undefined') {\n    return WebKitBlobBuilder;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nexport function getBlob(...args: Array<string | Blob | ArrayBuffer>): Blob {\n  const BlobBuilder = getBlobBuilder();\n  if (BlobBuilder !== undefined) {\n    const bb = new BlobBuilder();\n    for (let i = 0; i < args.length; i++) {\n      bb.append(args[i]);\n    }\n    return bb.getBlob();\n  } else {\n    if (isNativeBlobDefined()) {\n      return new Blob(args);\n    } else {\n      throw new StorageError(\n        StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n        \"This browser doesn't seem to support creating Blobs\"\n      );\n    }\n  }\n}\n\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nexport function sliceBlob(blob: Blob, start: number, end: number): Blob | null {\n  if (blob.webkitSlice) {\n    return blob.webkitSlice(start, end);\n  } else if (blob.mozSlice) {\n    return blob.mozSlice(start, end);\n  } else if (blob.slice) {\n    return blob.slice(start, end);\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { missingPolyFill } from '../../implementation/error';\n\n/** Converts a Base64 encoded string to a binary string. */\nexport function decodeBase64(encoded: string): string {\n  if (typeof atob === 'undefined') {\n    throw missingPolyFill('base-64');\n  }\n  return atob(encoded);\n}\n\nexport function decodeUint8Array(data: Uint8Array): string {\n  return new TextDecoder().decode(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { unknown, invalidFormat } from './error';\nimport { decodeBase64 } from '../platform/base64';\n\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport type StringFormat = (typeof StringFormat)[keyof typeof StringFormat];\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport const StringFormat = {\n  /**\n   * Indicates the string should be interpreted \"raw\", that is, as normal text.\n   * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n   * sequence.\n   * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n   * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n   */\n  RAW: 'raw',\n  /**\n   * Indicates the string should be interpreted as base64-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64: 'base64',\n  /**\n   * Indicates the string should be interpreted as base64url-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64URL: 'base64url',\n  /**\n   * Indicates the string is a data URL, such as one obtained from\n   * canvas.toDataURL().\n   * Example: the string 'data:application/octet-stream;base64,aaaa'\n   * becomes the byte sequence\n   * 69 a6 9a\n   * (the content-type \"application/octet-stream\" is also applied, but can\n   * be overridden in the metadata object).\n   */\n  DATA_URL: 'data_url'\n} as const;\n\nexport class StringData {\n  contentType: string | null;\n\n  constructor(public data: Uint8Array, contentType?: string | null) {\n    this.contentType = contentType || null;\n  }\n}\n\n/**\n * @internal\n */\nexport function dataFromString(\n  format: StringFormat,\n  stringData: string\n): StringData {\n  switch (format) {\n    case StringFormat.RAW:\n      return new StringData(utf8Bytes_(stringData));\n    case StringFormat.BASE64:\n    case StringFormat.BASE64URL:\n      return new StringData(base64Bytes_(format, stringData));\n    case StringFormat.DATA_URL:\n      return new StringData(\n        dataURLBytes_(stringData),\n        dataURLContentType_(stringData)\n      );\n    default:\n    // do nothing\n  }\n\n  // assert(false);\n  throw unknown();\n}\n\nexport function utf8Bytes_(value: string): Uint8Array {\n  const b: number[] = [];\n  for (let i = 0; i < value.length; i++) {\n    let c = value.charCodeAt(i);\n    if (c <= 127) {\n      b.push(c);\n    } else {\n      if (c <= 2047) {\n        b.push(192 | (c >> 6), 128 | (c & 63));\n      } else {\n        if ((c & 64512) === 55296) {\n          // The start of a surrogate pair.\n          const valid =\n            i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n          if (!valid) {\n            // The second surrogate wasn't there.\n            b.push(239, 191, 189);\n          } else {\n            const hi = c;\n            const lo = value.charCodeAt(++i);\n            c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\n            b.push(\n              240 | (c >> 18),\n              128 | ((c >> 12) & 63),\n              128 | ((c >> 6) & 63),\n              128 | (c & 63)\n            );\n          }\n        } else {\n          if ((c & 64512) === 56320) {\n            // Invalid low surrogate.\n            b.push(239, 191, 189);\n          } else {\n            b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\n          }\n        }\n      }\n    }\n  }\n  return new Uint8Array(b);\n}\n\nexport function percentEncodedBytes_(value: string): Uint8Array {\n  let decoded;\n  try {\n    decoded = decodeURIComponent(value);\n  } catch (e) {\n    throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n  }\n  return utf8Bytes_(decoded);\n}\n\nexport function base64Bytes_(format: StringFormat, value: string): Uint8Array {\n  switch (format) {\n    case StringFormat.BASE64: {\n      const hasMinus = value.indexOf('-') !== -1;\n      const hasUnder = value.indexOf('_') !== -1;\n      if (hasMinus || hasUnder) {\n        const invalidChar = hasMinus ? '-' : '_';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" +\n            invalidChar +\n            \"' found: is it base64url encoded?\"\n        );\n      }\n      break;\n    }\n    case StringFormat.BASE64URL: {\n      const hasPlus = value.indexOf('+') !== -1;\n      const hasSlash = value.indexOf('/') !== -1;\n      if (hasPlus || hasSlash) {\n        const invalidChar = hasPlus ? '+' : '/';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\"\n        );\n      }\n      value = value.replace(/-/g, '+').replace(/_/g, '/');\n      break;\n    }\n    default:\n    // do nothing\n  }\n  let bytes;\n  try {\n    bytes = decodeBase64(value);\n  } catch (e) {\n    if ((e as Error).message.includes('polyfill')) {\n      throw e;\n    }\n    throw invalidFormat(format, 'Invalid character found');\n  }\n  const array = new Uint8Array(bytes.length);\n  for (let i = 0; i < bytes.length; i++) {\n    array[i] = bytes.charCodeAt(i);\n  }\n  return array;\n}\n\nclass DataURLParts {\n  base64: boolean = false;\n  contentType: string | null = null;\n  rest: string;\n\n  constructor(dataURL: string) {\n    const matches = dataURL.match(/^data:([^,]+)?,/);\n    if (matches === null) {\n      throw invalidFormat(\n        StringFormat.DATA_URL,\n        \"Must be formatted 'data:[<mediatype>][;base64],<data>\"\n      );\n    }\n    const middle = matches[1] || null;\n    if (middle != null) {\n      this.base64 = endsWith(middle, ';base64');\n      this.contentType = this.base64\n        ? middle.substring(0, middle.length - ';base64'.length)\n        : middle;\n    }\n    this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n  }\n}\n\nexport function dataURLBytes_(dataUrl: string): Uint8Array {\n  const parts = new DataURLParts(dataUrl);\n  if (parts.base64) {\n    return base64Bytes_(StringFormat.BASE64, parts.rest);\n  } else {\n    return percentEncodedBytes_(parts.rest);\n  }\n}\n\nexport function dataURLContentType_(dataUrl: string): string | null {\n  const parts = new DataURLParts(dataUrl);\n  return parts.contentType;\n}\n\nfunction endsWith(s: string, end: string): boolean {\n  const longEnough = s.length >= end.length;\n  if (!longEnough) {\n    return false;\n  }\n\n  return s.substring(s.length - end.length) === end;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @file Provides a Blob-like wrapper for various binary types (including the\n * native Blob type). This makes it possible to upload types like ArrayBuffers,\n * making uploads possible in environments without the native Blob type.\n */\nimport { sliceBlob, getBlob } from './fs';\nimport { StringFormat, dataFromString } from './string';\nimport { isNativeBlob, isNativeBlobDefined, isString } from './type';\n\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nexport class FbsBlob {\n  private data_!: Blob | Uint8Array;\n  private size_: number;\n  private type_: string;\n\n  constructor(data: Blob | Uint8Array | ArrayBuffer, elideCopy?: boolean) {\n    let size: number = 0;\n    let blobType: string = '';\n    if (isNativeBlob(data)) {\n      this.data_ = data as Blob;\n      size = (data as Blob).size;\n      blobType = (data as Blob).type;\n    } else if (data instanceof ArrayBuffer) {\n      if (elideCopy) {\n        this.data_ = new Uint8Array(data);\n      } else {\n        this.data_ = new Uint8Array(data.byteLength);\n        this.data_.set(new Uint8Array(data));\n      }\n      size = this.data_.length;\n    } else if (data instanceof Uint8Array) {\n      if (elideCopy) {\n        this.data_ = data as Uint8Array;\n      } else {\n        this.data_ = new Uint8Array(data.length);\n        this.data_.set(data as Uint8Array);\n      }\n      size = data.length;\n    }\n    this.size_ = size;\n    this.type_ = blobType;\n  }\n\n  size(): number {\n    return this.size_;\n  }\n\n  type(): string {\n    return this.type_;\n  }\n\n  slice(startByte: number, endByte: number): FbsBlob | null {\n    if (isNativeBlob(this.data_)) {\n      const realBlob = this.data_ as Blob;\n      const sliced = sliceBlob(realBlob, startByte, endByte);\n      if (sliced === null) {\n        return null;\n      }\n      return new FbsBlob(sliced);\n    } else {\n      const slice = new Uint8Array(\n        (this.data_ as Uint8Array).buffer,\n        startByte,\n        endByte - startByte\n      );\n      return new FbsBlob(slice, true);\n    }\n  }\n\n  static getBlob(...args: Array<string | FbsBlob>): FbsBlob | null {\n    if (isNativeBlobDefined()) {\n      const blobby: Array<Blob | Uint8Array | string> = args.map(\n        (val: string | FbsBlob): Blob | Uint8Array | string => {\n          if (val instanceof FbsBlob) {\n            return val.data_;\n          } else {\n            return val;\n          }\n        }\n      );\n      return new FbsBlob(getBlob.apply(null, blobby));\n    } else {\n      const uint8Arrays: Uint8Array[] = args.map(\n        (val: string | FbsBlob): Uint8Array => {\n          if (isString(val)) {\n            return dataFromString(StringFormat.RAW, val as string).data;\n          } else {\n            // Blobs don't exist, so this has to be a Uint8Array.\n            return (val as FbsBlob).data_ as Uint8Array;\n          }\n        }\n      );\n      let finalLength = 0;\n      uint8Arrays.forEach((array: Uint8Array): void => {\n        finalLength += array.byteLength;\n      });\n      const merged = new Uint8Array(finalLength);\n      let index = 0;\n      uint8Arrays.forEach((array: Uint8Array) => {\n        for (let i = 0; i < array.length; i++) {\n          merged[index++] = array[i];\n        }\n      });\n      return new FbsBlob(merged, true);\n    }\n  }\n\n  uploadData(): Blob | Uint8Array {\n    return this.data_;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isNonArrayObject } from './type';\n\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nexport function jsonObjectOrNull(\n  s: string\n): { [name: string]: unknown } | null {\n  let obj;\n  try {\n    obj = JSON.parse(s);\n  } catch (e) {\n    return null;\n  }\n  if (isNonArrayObject(obj)) {\n    return obj;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n\n/**\n * @return Null if the path is already at the root.\n */\nexport function parent(path: string): string | null {\n  if (path.length === 0) {\n    return null;\n  }\n  const index = path.lastIndexOf('/');\n  if (index === -1) {\n    return '';\n  }\n  const newPath = path.slice(0, index);\n  return newPath;\n}\n\nexport function child(path: string, childPath: string): string {\n  const canonicalChildPath = childPath\n    .split('/')\n    .filter(component => component.length > 0)\n    .join('/');\n  if (path.length === 0) {\n    return canonicalChildPath;\n  } else {\n    return path + '/' + canonicalChildPath;\n  }\n}\n\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nexport function lastComponent(path: string): string {\n  const index = path.lastIndexOf('/', path.length - 2);\n  if (index === -1) {\n    return path;\n  } else {\n    return path.slice(index + 1);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the metadata format\n */\nimport { Metadata } from '../metadata';\n\nimport { jsonObjectOrNull } from './json';\nimport { Location } from './location';\nimport { lastComponent } from './path';\nimport { isString } from './type';\nimport { makeUrl, makeQueryString } from './url';\nimport { Reference } from '../reference';\nimport { FirebaseStorageImpl } from '../service';\n\nexport function noXform_<T>(metadata: Metadata, value: T): T {\n  return value;\n}\n\nclass Mapping<T> {\n  local: string;\n  writable: boolean;\n  xform: (p1: Metadata, p2?: T) => T | undefined;\n\n  constructor(\n    public server: string,\n    local?: string | null,\n    writable?: boolean,\n    xform?: ((p1: Metadata, p2?: T) => T | undefined) | null\n  ) {\n    this.local = local || server;\n    this.writable = !!writable;\n    this.xform = xform || noXform_;\n  }\n}\ntype Mappings = Array<Mapping<string> | Mapping<number>>;\n\nexport { Mappings };\n\nlet mappings_: Mappings | null = null;\n\nexport function xformPath(fullPath: string | undefined): string | undefined {\n  if (!isString(fullPath) || fullPath.length < 2) {\n    return fullPath;\n  } else {\n    return lastComponent(fullPath);\n  }\n}\n\nexport function getMappings(): Mappings {\n  if (mappings_) {\n    return mappings_;\n  }\n  const mappings: Mappings = [];\n  mappings.push(new Mapping<string>('bucket'));\n  mappings.push(new Mapping<string>('generation'));\n  mappings.push(new Mapping<string>('metageneration'));\n  mappings.push(new Mapping<string>('name', 'fullPath', true));\n\n  function mappingsXformPath(\n    _metadata: Metadata,\n    fullPath: string | undefined\n  ): string | undefined {\n    return xformPath(fullPath);\n  }\n  const nameMapping = new Mapping<string>('name');\n  nameMapping.xform = mappingsXformPath;\n  mappings.push(nameMapping);\n\n  /**\n   * Coerces the second param to a number, if it is defined.\n   */\n  function xformSize(\n    _metadata: Metadata,\n    size?: number | string\n  ): number | undefined {\n    if (size !== undefined) {\n      return Number(size);\n    } else {\n      return size;\n    }\n  }\n  const sizeMapping = new Mapping<number>('size');\n  sizeMapping.xform = xformSize;\n  mappings.push(sizeMapping);\n  mappings.push(new Mapping<number>('timeCreated'));\n  mappings.push(new Mapping<string>('updated'));\n  mappings.push(new Mapping<string>('md5Hash', null, true));\n  mappings.push(new Mapping<string>('cacheControl', null, true));\n  mappings.push(new Mapping<string>('contentDisposition', null, true));\n  mappings.push(new Mapping<string>('contentEncoding', null, true));\n  mappings.push(new Mapping<string>('contentLanguage', null, true));\n  mappings.push(new Mapping<string>('contentType', null, true));\n  mappings.push(new Mapping<string>('metadata', 'customMetadata', true));\n  mappings_ = mappings;\n  return mappings_;\n}\n\nexport function addRef(metadata: Metadata, service: FirebaseStorageImpl): void {\n  function generateRef(): Reference {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const loc = new Location(bucket, path);\n    return service._makeStorageReference(loc);\n  }\n  Object.defineProperty(metadata, 'ref', { get: generateRef });\n}\n\nexport function fromResource(\n  service: FirebaseStorageImpl,\n  resource: { [name: string]: unknown },\n  mappings: Mappings\n): Metadata {\n  const metadata: Metadata = {} as Metadata;\n  metadata['type'] = 'file';\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    metadata[mapping.local] = (mapping as Mapping<unknown>).xform(\n      metadata,\n      resource[mapping.server]\n    );\n  }\n  addRef(metadata, service);\n  return metadata;\n}\n\nexport function fromResourceString(\n  service: FirebaseStorageImpl,\n  resourceString: string,\n  mappings: Mappings\n): Metadata | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as Metadata;\n  return fromResource(service, resource, mappings);\n}\n\nexport function downloadUrlFromResourceString(\n  metadata: Metadata,\n  resourceString: string,\n  host: string,\n  protocol: string\n): string | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  if (!isString(obj['downloadTokens'])) {\n    // This can happen if objects are uploaded through GCS and retrieved\n    // through list, so we don't want to throw an Error.\n    return null;\n  }\n  const tokens: string = obj['downloadTokens'] as string;\n  if (tokens.length === 0) {\n    return null;\n  }\n  const encode = encodeURIComponent;\n  const tokensList = tokens.split(',');\n  const urls = tokensList.map((token: string): string => {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n    const base = makeUrl(urlPart, host, protocol);\n    const queryString = makeQueryString({\n      alt: 'media',\n      token\n    });\n    return base + queryString;\n  });\n  return urls[0];\n}\n\nexport function toResourceString(\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): string {\n  const resource: {\n    [prop: string]: unknown;\n  } = {};\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    if (mapping.writable) {\n      resource[mapping.server] = metadata[mapping.local];\n    }\n  }\n  return JSON.stringify(resource);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the listOptions and listResult format\n */\nimport { Location } from './location';\nimport { jsonObjectOrNull } from './json';\nimport { ListResult } from '../list';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Represents the simplified object metadata returned by List API.\n * Other fields are filtered because list in Firebase Rules does not grant\n * the permission to read the metadata.\n */\ninterface ListMetadataResponse {\n  name: string;\n  bucket: string;\n}\n\n/**\n * Represents the JSON response of List API.\n */\ninterface ListResultResponse {\n  prefixes: string[];\n  items: ListMetadataResponse[];\n  nextPageToken?: string;\n}\n\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\n\nfunction fromBackendResponse(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resource: ListResultResponse\n): ListResult {\n  const listResult: ListResult = {\n    prefixes: [],\n    items: [],\n    nextPageToken: resource['nextPageToken']\n  };\n  if (resource[PREFIXES_KEY]) {\n    for (const path of resource[PREFIXES_KEY]) {\n      const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n      const reference = service._makeStorageReference(\n        new Location(bucket, pathWithoutTrailingSlash)\n      );\n      listResult.prefixes.push(reference);\n    }\n  }\n\n  if (resource[ITEMS_KEY]) {\n    for (const item of resource[ITEMS_KEY]) {\n      const reference = service._makeStorageReference(\n        new Location(bucket, item['name'])\n      );\n      listResult.items.push(reference);\n    }\n  }\n  return listResult;\n}\n\nexport function fromResponseString(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resourceString: string\n): ListResult | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as unknown as ListResultResponse;\n  return fromBackendResponse(service, bucket, resource);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Headers, Connection, ConnectionType } from './connection';\n\n/**\n * Type for url params stored in RequestInfo.\n */\nexport interface UrlParams {\n  [name: string]: string | number;\n}\n\n/**\n * A function that converts a server response to the API type expected by the\n * SDK.\n *\n * @param I - the type of the backend's network response\n * @param O - the output response type used by the rest of the SDK.\n */\nexport type RequestHandler<I extends ConnectionType, O> = (\n  connection: Connection<I>,\n  response: I\n) => O;\n\n/** A function to handle an error. */\nexport type ErrorHandler = (\n  connection: Connection<ConnectionType>,\n  response: StorageError\n) => StorageError;\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nexport class RequestInfo<I extends ConnectionType, O> {\n  urlParams: UrlParams = {};\n  headers: Headers = {};\n  body: Blob | string | Uint8Array | null = null;\n  errorHandler: ErrorHandler | null = null;\n\n  /**\n   * Called with the current number of bytes uploaded and total size (-1 if not\n   * computable) of the request body (i.e. used to report upload progress).\n   */\n  progressCallback: ((p1: number, p2: number) => void) | null = null;\n  successCodes: number[] = [200];\n  additionalRetryCodes: number[] = [];\n\n  constructor(\n    public url: string,\n    public method: string,\n    /**\n     * Returns the value with which to resolve the request's promise. Only called\n     * if the request is successful. Throw from this function to reject the\n     * returned Request's promise with the thrown error.\n     * Note: The XhrIo passed to this function may be reused after this callback\n     * returns. Do not keep a reference to it in any way.\n     */\n    public handler: RequestHandler<I, O>,\n    public timeout: number\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods for interacting with the network.\n */\n\nimport { Metadata } from '../metadata';\nimport { ListResult } from '../list';\nimport { FbsBlob } from './blob';\nimport {\n  StorageError,\n  cannotSliceBlob,\n  unauthenticated,\n  quotaExceeded,\n  unauthorized,\n  objectNotFound,\n  serverFileWrongSize,\n  unknown,\n  unauthorizedApp\n} from './error';\nimport { Location } from './location';\nimport {\n  Mappings,\n  fromResourceString,\n  downloadUrlFromResourceString,\n  toResourceString\n} from './metadata';\nimport { fromResponseString } from './list';\nimport { RequestInfo, UrlParams } from './requestinfo';\nimport { isString } from './type';\nimport { makeUrl } from './url';\nimport { Connection, ConnectionType } from './connection';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nexport function handlerCheck(cndn: boolean): void {\n  if (!cndn) {\n    throw unknown();\n  }\n}\n\nexport function metadataHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => Metadata {\n  function handler(xhr: Connection<string>, text: string): Metadata {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return metadata as Metadata;\n  }\n  return handler;\n}\n\nexport function listHandler(\n  service: FirebaseStorageImpl,\n  bucket: string\n): (p1: Connection<string>, p2: string) => ListResult {\n  function handler(xhr: Connection<string>, text: string): ListResult {\n    const listResult = fromResponseString(service, bucket, text);\n    handlerCheck(listResult !== null);\n    return listResult as ListResult;\n  }\n  return handler;\n}\n\nexport function downloadUrlHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => string | null {\n  function handler(xhr: Connection<string>, text: string): string | null {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return downloadUrlFromResourceString(\n      metadata as Metadata,\n      text,\n      service.host,\n      service._protocol\n    );\n  }\n  return handler;\n}\n\nexport function sharedErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr: StorageError;\n    if (xhr.getStatus() === 401) {\n      if (\n        // This exact message string is the only consistent part of the\n        // server's error response that identifies it as an App Check error.\n        xhr.getErrorText().includes('Firebase App Check token is invalid')\n      ) {\n        newErr = unauthorizedApp();\n      } else {\n        newErr = unauthenticated();\n      }\n    } else {\n      if (xhr.getStatus() === 402) {\n        newErr = quotaExceeded(location.bucket);\n      } else {\n        if (xhr.getStatus() === 403) {\n          newErr = unauthorized(location.path);\n        } else {\n          newErr = err;\n        }\n      }\n    }\n    newErr.status = xhr.getStatus();\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function objectErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  const shared = sharedErrorHandler(location);\n\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr = shared(xhr, err);\n    if (xhr.getStatus() === 404) {\n      newErr = objectNotFound(location.path);\n    }\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function getMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function list(\n  service: FirebaseStorageImpl,\n  location: Location,\n  delimiter?: string,\n  pageToken?: string | null,\n  maxResults?: number | null\n): RequestInfo<string, ListResult> {\n  const urlParams: UrlParams = {};\n  if (location.isRoot) {\n    urlParams['prefix'] = '';\n  } else {\n    urlParams['prefix'] = location.path + '/';\n  }\n  if (delimiter && delimiter.length > 0) {\n    urlParams['delimiter'] = delimiter;\n  }\n  if (pageToken) {\n    urlParams['pageToken'] = pageToken;\n  }\n  if (maxResults) {\n    urlParams['maxResults'] = maxResults;\n  }\n  const urlPart = location.bucketOnlyServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    listHandler(service, location.bucket),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\nexport function getBytes<I extends ConnectionType>(\n  service: FirebaseStorageImpl,\n  location: Location,\n  maxDownloadSizeBytes?: number\n): RequestInfo<I, I> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    (_: Connection<I>, data: I) => data,\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  if (maxDownloadSizeBytes !== undefined) {\n    requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n    requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n  }\n  return requestInfo;\n}\n\nexport function getDownloadUrl(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, string | null> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    downloadUrlHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function updateMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'PATCH';\n  const body = toResourceString(metadata, mappings);\n  const headers = { 'Content-Type': 'application/json; charset=utf-8' };\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function deleteObject(\n  service: FirebaseStorageImpl,\n  location: Location\n): RequestInfo<string, void> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'DELETE';\n  const timeout = service.maxOperationRetryTime;\n\n  function handler(_xhr: Connection<string>, _text: string): void {}\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.successCodes = [200, 204];\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function determineContentType_(\n  metadata: Metadata | null,\n  blob: FbsBlob | null\n): string {\n  return (\n    (metadata && metadata['contentType']) ||\n    (blob && blob.type()) ||\n    'application/octet-stream'\n  );\n}\n\nexport function metadataForUpload_(\n  location: Location,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): Metadata {\n  const metadataClone = Object.assign({}, metadata);\n  metadataClone['fullPath'] = location.path;\n  metadataClone['size'] = blob.size();\n  if (!metadataClone['contentType']) {\n    metadataClone['contentType'] = determineContentType_(null, blob);\n  }\n  return metadataClone;\n}\n\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nexport function multipartUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, Metadata> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const headers: { [prop: string]: string } = {\n    'X-Goog-Upload-Protocol': 'multipart'\n  };\n\n  function genBoundary(): string {\n    let str = '';\n    for (let i = 0; i < 2; i++) {\n      str = str + Math.random().toString().slice(2);\n    }\n    return str;\n  }\n  const boundary = genBoundary();\n  headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n  const metadata_ = metadataForUpload_(location, blob, metadata);\n  const metadataString = toResourceString(metadata_, mappings);\n  const preBlobPart =\n    '--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\n    metadataString +\n    '\\r\\n--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: ' +\n    metadata_['contentType'] +\n    '\\r\\n\\r\\n';\n  const postBlobPart = '\\r\\n--' + boundary + '--';\n  const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  const urlParams: UrlParams = { name: metadata_['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nexport class ResumableUploadStatus {\n  finalized: boolean;\n  metadata: Metadata | null;\n\n  constructor(\n    public current: number,\n    public total: number,\n    finalized?: boolean,\n    metadata?: Metadata | null\n  ) {\n    this.finalized = !!finalized;\n    this.metadata = metadata || null;\n  }\n}\n\nexport function checkResumeHeader_(\n  xhr: Connection<string>,\n  allowed?: string[]\n): string {\n  let status: string | null = null;\n  try {\n    status = xhr.getResponseHeader('X-Goog-Upload-Status');\n  } catch (e) {\n    handlerCheck(false);\n  }\n  const allowedStatus = allowed || ['active'];\n  handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n  return status as string;\n}\n\nexport function createResumableUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, string> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const metadataForUpload = metadataForUpload_(location, blob, metadata);\n  const urlParams: UrlParams = { name: metadataForUpload['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const headers = {\n    'X-Goog-Upload-Protocol': 'resumable',\n    'X-Goog-Upload-Command': 'start',\n    'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n    'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType']!,\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const body = toResourceString(metadataForUpload, mappings);\n  const timeout = service.maxUploadRetryTime;\n\n  function handler(xhr: Connection<string>): string {\n    checkResumeHeader_(xhr);\n    let url;\n    try {\n      url = xhr.getResponseHeader('X-Goog-Upload-URL');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    handlerCheck(isString(url));\n    return url as string;\n  }\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nexport function getResumableUploadStatus(\n  service: FirebaseStorageImpl,\n  location: Location,\n  url: string,\n  blob: FbsBlob\n): RequestInfo<string, ResumableUploadStatus> {\n  const headers = { 'X-Goog-Upload-Command': 'query' };\n\n  function handler(xhr: Connection<string>): ResumableUploadStatus {\n    const status = checkResumeHeader_(xhr, ['active', 'final']);\n    let sizeString: string | null = null;\n    try {\n      sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n    } catch (e) {\n      handlerCheck(false);\n    }\n\n    if (!sizeString) {\n      // null or empty string\n      handlerCheck(false);\n    }\n\n    const size = Number(sizeString);\n    handlerCheck(!isNaN(size));\n    return new ResumableUploadStatus(size, blob.size(), status === 'final');\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nexport const RESUMABLE_UPLOAD_CHUNK_SIZE: number = 256 * 1024;\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nexport function continueResumableUpload(\n  location: Location,\n  service: FirebaseStorageImpl,\n  url: string,\n  blob: FbsBlob,\n  chunkSize: number,\n  mappings: Mappings,\n  status?: ResumableUploadStatus | null,\n  progressCallback?: ((p1: number, p2: number) => void) | null\n): RequestInfo<string, ResumableUploadStatus> {\n  // TODO(andysoto): standardize on internal asserts\n  // assert(!(opt_status && opt_status.finalized));\n  const status_ = new ResumableUploadStatus(0, 0);\n  if (status) {\n    status_.current = status.current;\n    status_.total = status.total;\n  } else {\n    status_.current = 0;\n    status_.total = blob.size();\n  }\n  if (blob.size() !== status_.total) {\n    throw serverFileWrongSize();\n  }\n  const bytesLeft = status_.total - status_.current;\n  let bytesToUpload = bytesLeft;\n  if (chunkSize > 0) {\n    bytesToUpload = Math.min(bytesToUpload, chunkSize);\n  }\n  const startByte = status_.current;\n  const endByte = startByte + bytesToUpload;\n  let uploadCommand = '';\n  if (bytesToUpload === 0) {\n    uploadCommand = 'finalize';\n  } else if (bytesLeft === bytesToUpload) {\n    uploadCommand = 'upload, finalize';\n  } else {\n    uploadCommand = 'upload';\n  }\n  const headers = {\n    'X-Goog-Upload-Command': uploadCommand,\n    'X-Goog-Upload-Offset': `${status_.current}`\n  };\n  const body = blob.slice(startByte, endByte);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n\n  function handler(\n    xhr: Connection<string>,\n    text: string\n  ): ResumableUploadStatus {\n    // TODO(andysoto): Verify the MD5 of each uploaded range:\n    // the 'x-range-md5' header comes back with status code 308 responses.\n    // We'll only be able to bail out though, because you can't re-upload a\n    // range that you previously uploaded.\n    const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n    const newCurrent = status_.current + bytesToUpload;\n    const size = blob.size();\n    let metadata;\n    if (uploadStatus === 'final') {\n      metadata = metadataHandler(service, mappings)(xhr, text);\n    } else {\n      metadata = null;\n    }\n    return new ResumableUploadStatus(\n      newCurrent,\n      size,\n      uploadStatus === 'final',\n      metadata\n    );\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.progressCallback = progressCallback || null;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Enumerations used for upload tasks.\n */\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport type TaskEvent = string;\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport const TaskEvent = {\n  /**\n   * For this event,\n   * <ul>\n   *   <li>The `next` function is triggered on progress updates and when the\n   *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n   *       argument.</li>\n   *   <li>The `error` function is triggered if the upload is canceled or fails\n   *       for another reason.</li>\n   *   <li>The `complete` function is triggered if the upload completes\n   *       successfully.</li>\n   * </ul>\n   */\n  STATE_CHANGED: 'state_changed'\n};\n\n/**\n * Internal enum for task state.\n */\nexport const enum InternalTaskState {\n  RUNNING = 'running',\n  PAUSING = 'pausing',\n  PAUSED = 'paused',\n  SUCCESS = 'success',\n  CANCELING = 'canceling',\n  CANCELED = 'canceled',\n  ERROR = 'error'\n}\n\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport type TaskState = (typeof TaskState)[keyof typeof TaskState];\n\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport const TaskState = {\n  /** The task is currently transferring data. */\n  RUNNING: 'running',\n\n  /** The task was paused by the user. */\n  PAUSED: 'paused',\n\n  /** The task completed successfully. */\n  SUCCESS: 'success',\n\n  /** The task was canceled. */\n  CANCELED: 'canceled',\n\n  /** The task failed with an error. */\n  ERROR: 'error'\n} as const;\n\nexport function taskStateFromInternalTaskState(\n  state: InternalTaskState\n): TaskState {\n  switch (state) {\n    case InternalTaskState.RUNNING:\n    case InternalTaskState.PAUSING:\n    case InternalTaskState.CANCELING:\n      return TaskState.RUNNING;\n    case InternalTaskState.PAUSED:\n      return TaskState.PAUSED;\n    case InternalTaskState.SUCCESS:\n      return TaskState.SUCCESS;\n    case InternalTaskState.CANCELED:\n      return TaskState.CANCELED;\n    case InternalTaskState.ERROR:\n      return TaskState.ERROR;\n    default:\n      // TODO(andysoto): assert(false);\n      return TaskState.ERROR;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isFunction } from './type';\nimport { StorageError } from './error';\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `StorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * An observer identical to the `Observer` defined in packages/util except the\n * error passed into the ErrorFn is specifically a `StorageError`.\n */\nexport interface StorageObserver<T> {\n  /**\n   * Function that is called once for each value in the event stream.\n   */\n  next?: NextFn<T>;\n  /**\n   * A function that is called with a `StorageError`\n   * if the event stream ends due to an error.\n   */\n  error?: ErrorFn;\n  /**\n   * A function that is called if the event stream ends normally.\n   */\n  complete?: CompleteFn;\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\nexport class Observer<T> implements StorageObserver<T> {\n  next?: NextFn<T>;\n  error?: ErrorFn;\n  complete?: CompleteFn;\n\n  constructor(\n    nextOrObserver?: NextFn<T> | StorageObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ) {\n    const asFunctions =\n      isFunction(nextOrObserver) || error != null || complete != null;\n    if (asFunctions) {\n      this.next = nextOrObserver as NextFn<T>;\n      this.error = error ?? undefined;\n      this.complete = complete ?? undefined;\n    } else {\n      const observer = nextOrObserver as {\n        next?: NextFn<T>;\n        error?: ErrorFn;\n        complete?: CompleteFn;\n      };\n      this.next = observer.next;\n      this.error = observer.error;\n      this.complete = observer.complete;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(f: Function): Function {\n  return (...argsToForward: unknown[]) => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.resolve().then(() => f(...argsToForward));\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { isCloudWorkstation } from '@firebase/util';\nimport {\n  Connection,\n  ConnectionType,\n  ErrorCode,\n  Headers\n} from '../../implementation/connection';\nimport { internalError } from '../../implementation/error';\n\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride: (() => Connection<string>) | null = null;\n\n/**\n * Network layer for browsers. We use this instead of goog.net.XhrIo because\n * goog.net.XhrIo is hyuuuuge and doesn't work in React Native on Android.\n */\nabstract class XhrConnection<T extends ConnectionType>\n  implements Connection<T>\n{\n  protected xhr_: XMLHttpRequest;\n  private errorCode_: ErrorCode;\n  private sendPromise_: Promise<void>;\n  protected sent_: boolean = false;\n\n  constructor() {\n    this.xhr_ = new XMLHttpRequest();\n    this.initXhr();\n    this.errorCode_ = ErrorCode.NO_ERROR;\n    this.sendPromise_ = new Promise(resolve => {\n      this.xhr_.addEventListener('abort', () => {\n        this.errorCode_ = ErrorCode.ABORT;\n        resolve();\n      });\n      this.xhr_.addEventListener('error', () => {\n        this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        resolve();\n      });\n      this.xhr_.addEventListener('load', () => {\n        resolve();\n      });\n    });\n  }\n\n  abstract initXhr(): void;\n\n  send(\n    url: string,\n    method: string,\n    isUsingEmulator: boolean,\n    body?: ArrayBufferView | Blob | string,\n    headers?: Headers\n  ): Promise<void> {\n    if (this.sent_) {\n      throw internalError('cannot .send() more than once');\n    }\n    if (isCloudWorkstation(url) && isUsingEmulator) {\n      this.xhr_.withCredentials = true;\n    }\n    this.sent_ = true;\n    this.xhr_.open(method, url, true);\n    if (headers !== undefined) {\n      for (const key in headers) {\n        if (headers.hasOwnProperty(key)) {\n          this.xhr_.setRequestHeader(key, headers[key].toString());\n        }\n      }\n    }\n    if (body !== undefined) {\n      this.xhr_.send(body);\n    } else {\n      this.xhr_.send();\n    }\n    return this.sendPromise_;\n  }\n\n  getErrorCode(): ErrorCode {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorCode() before sending');\n    }\n    return this.errorCode_;\n  }\n\n  getStatus(): number {\n    if (!this.sent_) {\n      throw internalError('cannot .getStatus() before sending');\n    }\n    try {\n      return this.xhr_.status;\n    } catch (e) {\n      return -1;\n    }\n  }\n\n  getResponse(): T {\n    if (!this.sent_) {\n      throw internalError('cannot .getResponse() before sending');\n    }\n    return this.xhr_.response;\n  }\n\n  getErrorText(): string {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorText() before sending');\n    }\n    return this.xhr_.statusText;\n  }\n\n  /** Aborts the request. */\n  abort(): void {\n    this.xhr_.abort();\n  }\n\n  getResponseHeader(header: string): string | null {\n    return this.xhr_.getResponseHeader(header);\n  }\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.addEventListener('progress', listener);\n    }\n  }\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.removeEventListener('progress', listener);\n    }\n  }\n}\n\nexport class XhrTextConnection extends XhrConnection<string> {\n  initXhr(): void {\n    this.xhr_.responseType = 'text';\n  }\n}\n\nexport function newTextConnection(): Connection<string> {\n  return textFactoryOverride ? textFactoryOverride() : new XhrTextConnection();\n}\n\nexport class XhrBytesConnection extends XhrConnection<ArrayBuffer> {\n  private data_?: ArrayBuffer;\n\n  initXhr(): void {\n    this.xhr_.responseType = 'arraybuffer';\n  }\n}\n\nexport function newBytesConnection(): Connection<ArrayBuffer> {\n  return new XhrBytesConnection();\n}\n\nexport class XhrBlobConnection extends XhrConnection<Blob> {\n  initXhr(): void {\n    this.xhr_.responseType = 'blob';\n  }\n}\n\nexport function newBlobConnection(): Connection<Blob> {\n  return new XhrBlobConnection();\n}\n\nexport function newStreamConnection(): Connection<ReadableStream> {\n  throw new Error('Streams are only supported on Node');\n}\n\nexport function injectTestConnection(\n  factory: (() => Connection<string>) | null\n): void {\n  textFactoryOverride = factory;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Defines types for interacting with blob transfer tasks.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport {\n  canceled,\n  StorageErrorCode,\n  StorageError,\n  retryLimitExceeded\n} from './implementation/error';\nimport {\n  InternalTaskState,\n  TaskEvent,\n  TaskState,\n  taskStateFromInternalTaskState\n} from './implementation/taskenums';\nimport { Metadata } from './metadata';\nimport {\n  Observer,\n  Subscribe,\n  Unsubscribe,\n  StorageObserver as StorageObserverInternal,\n  NextFn\n} from './implementation/observer';\nimport { Request } from './implementation/request';\nimport { UploadTaskSnapshot, StorageObserver } from './public-types';\nimport { async as fbsAsync } from './implementation/async';\nimport { Mappings, getMappings } from './implementation/metadata';\nimport {\n  createResumableUpload,\n  getResumableUploadStatus,\n  RESUMABLE_UPLOAD_CHUNK_SIZE,\n  ResumableUploadStatus,\n  continueResumableUpload,\n  getMetadata,\n  multipartUpload\n} from './implementation/requests';\nimport { Reference } from './reference';\nimport { newTextConnection } from './platform/connection';\nimport { isRetryStatusCode } from './implementation/utils';\nimport { CompleteFn } from '@firebase/util';\nimport { DEFAULT_MIN_SLEEP_TIME_MILLIS } from './implementation/constants';\n\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nexport class UploadTask {\n  private _ref: Reference;\n  /**\n   * The data to be uploaded.\n   */\n  _blob: FbsBlob;\n  /**\n   * Metadata related to the upload.\n   */\n  _metadata: Metadata | null;\n  private _mappings: Mappings;\n  /**\n   * Number of bytes transferred so far.\n   */\n  _transferred: number = 0;\n  private _needToFetchStatus: boolean = false;\n  private _needToFetchMetadata: boolean = false;\n  private _observers: Array<StorageObserverInternal<UploadTaskSnapshot>> = [];\n  private _resumable: boolean;\n  /**\n   * Upload state.\n   */\n  _state: InternalTaskState;\n  private _error?: StorageError = undefined;\n  private _uploadUrl?: string = undefined;\n  private _request?: Request<unknown> = undefined;\n  private _chunkMultiplier: number = 1;\n  private _errorHandler: (p1: StorageError) => void;\n  private _metadataErrorHandler: (p1: StorageError) => void;\n  private _resolve?: (p1: UploadTaskSnapshot) => void = undefined;\n  private _reject?: (p1: StorageError) => void = undefined;\n  private pendingTimeout?: ReturnType<typeof setTimeout>;\n  private _promise: Promise<UploadTaskSnapshot>;\n\n  private sleepTime: number;\n\n  private maxSleepTime: number;\n\n  isExponentialBackoffExpired(): boolean {\n    return this.sleepTime > this.maxSleepTime;\n  }\n\n  /**\n   * @param ref - The firebaseStorage.Reference object this task came\n   *     from, untyped to avoid cyclic dependencies.\n   * @param blob - The blob to upload.\n   */\n  constructor(ref: Reference, blob: FbsBlob, metadata: Metadata | null = null) {\n    this._ref = ref;\n    this._blob = blob;\n    this._metadata = metadata;\n    this._mappings = getMappings();\n    this._resumable = this._shouldDoResumable(this._blob);\n    this._state = InternalTaskState.RUNNING;\n    this._errorHandler = error => {\n      this._request = undefined;\n      this._chunkMultiplier = 1;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this._needToFetchStatus = true;\n        this.completeTransitions_();\n      } else {\n        const backoffExpired = this.isExponentialBackoffExpired();\n        if (isRetryStatusCode(error.status, [])) {\n          if (backoffExpired) {\n            error = retryLimitExceeded();\n          } else {\n            this.sleepTime = Math.max(\n              this.sleepTime * 2,\n              DEFAULT_MIN_SLEEP_TIME_MILLIS\n            );\n            this._needToFetchStatus = true;\n            this.completeTransitions_();\n            return;\n          }\n        }\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this._metadataErrorHandler = error => {\n      this._request = undefined;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this.completeTransitions_();\n      } else {\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this.sleepTime = 0;\n    this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n      this._start();\n    });\n\n    // Prevent uncaught rejections on the internal promise from bubbling out\n    // to the top level with a dummy handler.\n    this._promise.then(null, () => {});\n  }\n\n  private _makeProgressCallback(): (p1: number, p2: number) => void {\n    const sizeBefore = this._transferred;\n    return loaded => this._updateProgress(sizeBefore + loaded);\n  }\n\n  private _shouldDoResumable(blob: FbsBlob): boolean {\n    return blob.size() > 256 * 1024;\n  }\n\n  private _start(): void {\n    if (this._state !== InternalTaskState.RUNNING) {\n      // This can happen if someone pauses us in a resume callback, for example.\n      return;\n    }\n    if (this._request !== undefined) {\n      return;\n    }\n    if (this._resumable) {\n      if (this._uploadUrl === undefined) {\n        this._createResumable();\n      } else {\n        if (this._needToFetchStatus) {\n          this._fetchStatus();\n        } else {\n          if (this._needToFetchMetadata) {\n            // Happens if we miss the metadata on upload completion.\n            this._fetchMetadata();\n          } else {\n            this.pendingTimeout = setTimeout(() => {\n              this.pendingTimeout = undefined;\n              this._continueUpload();\n            }, this.sleepTime);\n          }\n        }\n      }\n    } else {\n      this._oneShotUpload();\n    }\n  }\n\n  private _resolveToken(\n    callback: (authToken: string | null, appCheckToken: string | null) => void\n  ): void {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.all([\n      this._ref.storage._getAuthToken(),\n      this._ref.storage._getAppCheckToken()\n    ]).then(([authToken, appCheckToken]) => {\n      switch (this._state) {\n        case InternalTaskState.RUNNING:\n          callback(authToken, appCheckToken);\n          break;\n        case InternalTaskState.CANCELING:\n          this._transition(InternalTaskState.CANCELED);\n          break;\n        case InternalTaskState.PAUSING:\n          this._transition(InternalTaskState.PAUSED);\n          break;\n        default:\n      }\n    });\n  }\n\n  // TODO(andysoto): assert false\n\n  private _createResumable(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = createResumableUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const createRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = createRequest;\n      createRequest.getPromise().then((url: string) => {\n        this._request = undefined;\n        this._uploadUrl = url;\n        this._needToFetchStatus = false;\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _fetchStatus(): void {\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getResumableUploadStatus(\n        this._ref.storage,\n        this._ref._location,\n        url,\n        this._blob\n      );\n      const statusRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = statusRequest;\n      statusRequest.getPromise().then(status => {\n        status = status as ResumableUploadStatus;\n        this._request = undefined;\n        this._updateProgress(status.current);\n        this._needToFetchStatus = false;\n        if (status.finalized) {\n          this._needToFetchMetadata = true;\n        }\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _continueUpload(): void {\n    const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    const status = new ResumableUploadStatus(\n      this._transferred,\n      this._blob.size()\n    );\n\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      let requestInfo;\n      try {\n        requestInfo = continueResumableUpload(\n          this._ref._location,\n          this._ref.storage,\n          url,\n          this._blob,\n          chunkSize,\n          this._mappings,\n          status,\n          this._makeProgressCallback()\n        );\n      } catch (e) {\n        this._error = e as StorageError;\n        this._transition(InternalTaskState.ERROR);\n        return;\n      }\n      const uploadRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken,\n        /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n      );\n      this._request = uploadRequest;\n      uploadRequest.getPromise().then((newStatus: ResumableUploadStatus) => {\n        this._increaseMultiplier();\n        this._request = undefined;\n        this._updateProgress(newStatus.current);\n        if (newStatus.finalized) {\n          this._metadata = newStatus.metadata;\n          this._transition(InternalTaskState.SUCCESS);\n        } else {\n          this.completeTransitions_();\n        }\n      }, this._errorHandler);\n    });\n  }\n\n  private _increaseMultiplier(): void {\n    const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n\n    // Max chunk size is 32M.\n    if (currentSize * 2 < 32 * 1024 * 1024) {\n      this._chunkMultiplier *= 2;\n    }\n  }\n\n  private _fetchMetadata(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getMetadata(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings\n      );\n      const metadataRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = metadataRequest;\n      metadataRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._metadataErrorHandler);\n    });\n  }\n\n  private _oneShotUpload(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = multipartUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const multipartRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = multipartRequest;\n      multipartRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._updateProgress(this._blob.size());\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._errorHandler);\n    });\n  }\n\n  private _updateProgress(transferred: number): void {\n    const old = this._transferred;\n    this._transferred = transferred;\n\n    // A progress update can make the \"transferred\" value smaller (e.g. a\n    // partial upload not completed by server, after which the \"transferred\"\n    // value may reset to the value at the beginning of the request).\n    if (this._transferred !== old) {\n      this._notifyObservers();\n    }\n  }\n\n  private _transition(state: InternalTaskState): void {\n    if (this._state === state) {\n      return;\n    }\n    switch (state) {\n      case InternalTaskState.CANCELING:\n      case InternalTaskState.PAUSING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        if (this._request !== undefined) {\n          this._request.cancel();\n        } else if (this.pendingTimeout) {\n          clearTimeout(this.pendingTimeout);\n          this.pendingTimeout = undefined;\n          this.completeTransitions_();\n        }\n        break;\n      case InternalTaskState.RUNNING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        const wasPaused = this._state === InternalTaskState.PAUSED;\n        this._state = state;\n        if (wasPaused) {\n          this._notifyObservers();\n          this._start();\n        }\n        break;\n      case InternalTaskState.PAUSED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.CANCELED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._error = canceled();\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.ERROR:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.SUCCESS:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      default: // Ignore\n    }\n  }\n\n  private completeTransitions_(): void {\n    switch (this._state) {\n      case InternalTaskState.PAUSING:\n        this._transition(InternalTaskState.PAUSED);\n        break;\n      case InternalTaskState.CANCELING:\n        this._transition(InternalTaskState.CANCELED);\n        break;\n      case InternalTaskState.RUNNING:\n        this._start();\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        break;\n    }\n  }\n\n  /**\n   * A snapshot of the current task state.\n   */\n  get snapshot(): UploadTaskSnapshot {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    return {\n      bytesTransferred: this._transferred,\n      totalBytes: this._blob.size(),\n      state: externalState,\n      metadata: this._metadata!,\n      task: this,\n      ref: this._ref\n    };\n  }\n\n  /**\n   * Adds a callback for an event.\n   * @param type - The type of event to listen for.\n   * @param nextOrObserver -\n   *     The `next` function, which gets called for each item in\n   *     the event stream, or an observer object with some or all of these three\n   *     properties (`next`, `error`, `complete`).\n   * @param error - A function that gets called with a `StorageError`\n   *     if the event stream ends due to an error.\n   * @param completed - A function that gets called if the\n   *     event stream ends normally.\n   * @returns\n   *     If only the event argument is passed, returns a function you can use to\n   *     add callbacks (see the examples above). If more than just the event\n   *     argument is passed, returns a function you can call to unregister the\n   *     callbacks.\n   */\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | StorageObserver<UploadTaskSnapshot>\n      | null\n      | ((snapshot: UploadTaskSnapshot) => unknown),\n    error?: ((a: StorageError) => unknown) | null,\n    completed?: CompleteFn | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshot> {\n    // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n    const observer = new Observer(\n      (nextOrObserver as\n        | StorageObserverInternal<UploadTaskSnapshot>\n        | NextFn<UploadTaskSnapshot>) || undefined,\n      error || undefined,\n      completed || undefined\n    );\n    this._addObserver(observer);\n    return () => {\n      this._removeObserver(observer);\n    };\n  }\n\n  /**\n   * This object behaves like a Promise, and resolves with its snapshot data\n   * when the upload completes.\n   * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n   * @param onRejected - The rejection callback.\n   */\n  then<U>(\n    onFulfilled?: ((value: UploadTaskSnapshot) => U | Promise<U>) | null,\n    onRejected?: ((error: StorageError) => U | Promise<U>) | null\n  ): Promise<U> {\n    // These casts are needed so that TypeScript can infer the types of the\n    // resulting Promise.\n    return this._promise.then<U>(\n      onFulfilled as (value: UploadTaskSnapshot) => U | Promise<U>,\n      onRejected as ((error: unknown) => Promise<never>) | null\n    );\n  }\n\n  /**\n   * Equivalent to calling `then(null, onRejected)`.\n   */\n  catch<T>(onRejected: (p1: StorageError) => T | Promise<T>): Promise<T> {\n    return this.then(null, onRejected);\n  }\n\n  /**\n   * Adds the given observer.\n   */\n  private _addObserver(observer: Observer<UploadTaskSnapshot>): void {\n    this._observers.push(observer);\n    this._notifyObserver(observer);\n  }\n\n  /**\n   * Removes the given observer.\n   */\n  private _removeObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const i = this._observers.indexOf(observer);\n    if (i !== -1) {\n      this._observers.splice(i, 1);\n    }\n  }\n\n  private _notifyObservers(): void {\n    this._finishPromise();\n    const observers = this._observers.slice();\n    observers.forEach(observer => {\n      this._notifyObserver(observer);\n    });\n  }\n\n  private _finishPromise(): void {\n    if (this._resolve !== undefined) {\n      let triggered = true;\n      switch (taskStateFromInternalTaskState(this._state)) {\n        case TaskState.SUCCESS:\n          fbsAsync(this._resolve.bind(null, this.snapshot))();\n          break;\n        case TaskState.CANCELED:\n        case TaskState.ERROR:\n          const toCall = this._reject as (p1: StorageError) => void;\n          fbsAsync(toCall.bind(null, this._error as StorageError))();\n          break;\n        default:\n          triggered = false;\n          break;\n      }\n      if (triggered) {\n        this._resolve = undefined;\n        this._reject = undefined;\n      }\n    }\n  }\n\n  private _notifyObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    switch (externalState) {\n      case TaskState.RUNNING:\n      case TaskState.PAUSED:\n        if (observer.next) {\n          fbsAsync(observer.next.bind(observer, this.snapshot))();\n        }\n        break;\n      case TaskState.SUCCESS:\n        if (observer.complete) {\n          fbsAsync(observer.complete.bind(observer))();\n        }\n        break;\n      case TaskState.CANCELED:\n      case TaskState.ERROR:\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n    }\n  }\n\n  /**\n   * Resumes a paused task. Has no effect on a currently running or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  resume(): boolean {\n    const valid =\n      this._state === InternalTaskState.PAUSED ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.RUNNING);\n    }\n    return valid;\n  }\n\n  /**\n   * Pauses a currently running task. Has no effect on a paused or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  pause(): boolean {\n    const valid = this._state === InternalTaskState.RUNNING;\n    if (valid) {\n      this._transition(InternalTaskState.PAUSING);\n    }\n    return valid;\n  }\n\n  /**\n   * Cancels a currently running or paused task. Has no effect on a complete or\n   * failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  cancel(): boolean {\n    const valid =\n      this._state === InternalTaskState.RUNNING ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.CANCELING);\n    }\n    return valid;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines the Firebase StorageReference class.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport { Location } from './implementation/location';\nimport { getMappings } from './implementation/metadata';\nimport { child, lastComponent, parent } from './implementation/path';\nimport {\n  deleteObject as requestsDeleteObject,\n  getBytes,\n  getDownloadUrl as requestsGetDownloadUrl,\n  getMetadata as requestsGetMetadata,\n  list as requestsList,\n  multipartUpload,\n  updateMetadata as requestsUpdateMetadata\n} from './implementation/requests';\nimport { ListOptions, UploadResult } from './public-types';\nimport { dataFromString, StringFormat } from './implementation/string';\nimport { Metadata } from './metadata';\nimport { FirebaseStorageImpl } from './service';\nimport { ListResult } from './list';\nimport { UploadTask } from './task';\nimport { invalidRootOperation, noDownloadURL } from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport {\n  newBlobConnection,\n  newBytesConnection,\n  newStreamConnection,\n  newTextConnection\n} from './platform/connection';\nimport { RequestInfo } from './implementation/requestinfo';\n\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nexport class Reference {\n  _location: Location;\n\n  constructor(\n    private _service: FirebaseStorageImpl,\n    location: string | Location\n  ) {\n    if (location instanceof Location) {\n      this._location = location;\n    } else {\n      this._location = Location.makeFromUrl(location, _service.host);\n    }\n  }\n\n  /**\n   * Returns the URL for the bucket and path this object references,\n   *     in the form gs://<bucket>/<object-path>\n   * @override\n   */\n  toString(): string {\n    return 'gs://' + this._location.bucket + '/' + this._location.path;\n  }\n\n  protected _newRef(\n    service: FirebaseStorageImpl,\n    location: Location\n  ): Reference {\n    return new Reference(service, location);\n  }\n\n  /**\n   * A reference to the root of this object's bucket.\n   */\n  get root(): Reference {\n    const location = new Location(this._location.bucket, '');\n    return this._newRef(this._service, location);\n  }\n\n  /**\n   * The name of the bucket containing this reference's object.\n   */\n  get bucket(): string {\n    return this._location.bucket;\n  }\n\n  /**\n   * The full path of this object.\n   */\n  get fullPath(): string {\n    return this._location.path;\n  }\n\n  /**\n   * The short name of this object, which is the last component of the full path.\n   * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n   */\n  get name(): string {\n    return lastComponent(this._location.path);\n  }\n\n  /**\n   * The `StorageService` instance this `StorageReference` is associated with.\n   */\n  get storage(): FirebaseStorageImpl {\n    return this._service;\n  }\n\n  /**\n   * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n   * this reference is the root.\n   */\n  get parent(): Reference | null {\n    const newPath = parent(this._location.path);\n    if (newPath === null) {\n      return null;\n    }\n    const location = new Location(this._location.bucket, newPath);\n    return new Reference(this._service, location);\n  }\n\n  /**\n   * Utility function to throw an error in methods that do not accept a root reference.\n   */\n  _throwIfRoot(name: string): void {\n    if (this._location.path === '') {\n      throw invalidRootOperation(name);\n    }\n  }\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nexport function getBytesInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref._throwIfRoot('getBytes');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBytesConnection)\n    .then(bytes =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (bytes as ArrayBuffer).slice(0, maxDownloadSizeBytes)\n        : (bytes as ArrayBuffer)\n    );\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded blob.\n */\nexport function getBlobInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref._throwIfRoot('getBlob');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBlobConnection)\n    .then(blob =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (blob as Blob).slice(0, maxDownloadSizeBytes)\n        : (blob as Blob)\n    );\n}\n\n/** Stream the bytes at the object's location. */\nexport function getStreamInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  ref._throwIfRoot('getStream');\n  const requestInfo: RequestInfo<ReadableStream, ReadableStream> = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n\n  // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\n  const newMaxSizeTransform = (n: number): Transformer => {\n    let missingBytes = n;\n    return {\n      transform(chunk, controller: TransformStreamDefaultController) {\n        // GCS may not honor the Range header for small files\n        if (chunk.length < missingBytes) {\n          controller.enqueue(chunk);\n          missingBytes -= chunk.length;\n        } else {\n          controller.enqueue(chunk.slice(0, missingBytes));\n          controller.terminate();\n        }\n      }\n    };\n  };\n\n  const result =\n    maxDownloadSizeBytes !== undefined\n      ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\n      : new TransformStream(); // The default transformer forwards all chunks to its readable side\n\n  ref.storage\n    .makeRequestWithTokens(requestInfo, newStreamConnection)\n    .then(readableStream => readableStream.pipeThrough(result))\n    .catch(err => result.writable.abort(err));\n\n  return result.readable;\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadBytes');\n  const requestInfo = multipartUpload(\n    ref.storage,\n    ref._location,\n    getMappings(),\n    new FbsBlob(data, true),\n    metadata\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(finalMetadata => {\n      return {\n        metadata: finalMetadata,\n        ref\n      };\n    });\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): UploadTask {\n  ref._throwIfRoot('uploadBytesResumable');\n  return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: Reference,\n  value: string,\n  format: StringFormat = StringFormat.RAW,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadString');\n  const data = dataFromString(format, value);\n  const metadataClone = { ...metadata } as Metadata;\n  if (metadataClone['contentType'] == null && data.contentType != null) {\n    metadataClone['contentType'] = data.contentType!;\n  }\n  return uploadBytes(ref, data.data, metadataClone);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: Reference): Promise<ListResult> {\n  const accumulator: ListResult = {\n    prefixes: [],\n    items: []\n  };\n  return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(\n  ref: Reference,\n  accumulator: ListResult,\n  pageToken?: string\n): Promise<void> {\n  const opt: ListOptions = {\n    // maxResults is 1000 by default.\n    pageToken\n  };\n  const nextPage = await list(ref, opt);\n  accumulator.prefixes.push(...nextPage.prefixes);\n  accumulator.items.push(...nextPage.items);\n  if (nextPage.nextPageToken != null) {\n    await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n  }\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: Reference,\n  options?: ListOptions | null\n): Promise<ListResult> {\n  if (options != null) {\n    if (typeof options.maxResults === 'number') {\n      validateNumber(\n        'options.maxResults',\n        /* minValue= */ 1,\n        /* maxValue= */ 1000,\n        options.maxResults\n      );\n    }\n  }\n  const op = options || {};\n  const requestInfo = requestsList(\n    ref.storage,\n    ref._location,\n    /*delimiter= */ '/',\n    op.pageToken,\n    op.maxResults\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nexport function getMetadata(ref: Reference): Promise<Metadata> {\n  ref._throwIfRoot('getMetadata');\n  const requestInfo = requestsGetMetadata(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nexport function updateMetadata(\n  ref: Reference,\n  metadata: Partial<Metadata>\n): Promise<Metadata> {\n  ref._throwIfRoot('updateMetadata');\n  const requestInfo = requestsUpdateMetadata(\n    ref.storage,\n    ref._location,\n    metadata,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: Reference): Promise<string> {\n  ref._throwIfRoot('getDownloadURL');\n  const requestInfo = requestsGetDownloadUrl(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(url => {\n      if (url === null) {\n        throw noDownloadURL();\n      }\n      return url;\n    });\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: Reference): Promise<void> {\n  ref._throwIfRoot('deleteObject');\n  const requestInfo = requestsDeleteObject(ref.storage, ref._location);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nexport function _getChild(ref: Reference, childPath: string): Reference {\n  const newPath = child(ref._location.path, childPath);\n  const location = new Location(ref._location.bucket, newPath);\n  return new Reference(ref.storage, location);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Location } from './implementation/location';\nimport { FailRequest } from './implementation/failrequest';\nimport { Request, makeRequest } from './implementation/request';\nimport { RequestInfo } from './implementation/requestinfo';\nimport { Reference, _getChild } from './reference';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  _isFirebaseServerApp\n} from '@firebase/app';\nimport {\n  CONFIG_STORAGE_BUCKET_KEY,\n  DEFAULT_HOST,\n  DEFAULT_MAX_OPERATION_RETRY_TIME,\n  DEFAULT_MAX_UPLOAD_RETRY_TIME\n} from './implementation/constants';\nimport {\n  invalidArgument,\n  appDeleted,\n  noDefaultBucket\n} from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport { FirebaseStorage } from './public-types';\nimport {\n  createMockUserToken,\n  EmulatorMockTokenOptions,\n  isCloudWorkstation,\n  pingServer,\n  updateEmulatorBanner\n} from '@firebase/util';\nimport { Connection, ConnectionType } from './implementation/connection';\n\nexport function isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service: FirebaseStorageImpl, url: string): Reference {\n  return new Reference(service, url);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(\n  ref: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference {\n  if (ref instanceof FirebaseStorageImpl) {\n    const service = ref;\n    if (service._bucket == null) {\n      throw noDefaultBucket();\n    }\n    const reference = new Reference(service, service._bucket!);\n    if (path != null) {\n      return refFromPath(reference, path);\n    } else {\n      return reference;\n    }\n  } else {\n    // ref is a Reference\n    if (path !== undefined) {\n      return _getChild(ref, path);\n    } else {\n      return ref;\n    }\n  }\n}\n\n/**\n * Returns a storage Reference for the given url.\n * @param storage - `Storage` instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorageImpl, url?: string): Reference;\n/**\n * Returns a storage Reference for the given path in the\n * default bucket.\n * @param storageOrRef - `Storage` service or storage `Reference`.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if Storage\n * instance provided) or returns same reference (if Reference provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference;\nexport function ref(\n  serviceOrRef: FirebaseStorageImpl | Reference,\n  pathOrUrl?: string\n): Reference | null {\n  if (pathOrUrl && isUrl(pathOrUrl)) {\n    if (serviceOrRef instanceof FirebaseStorageImpl) {\n      return refFromURL(serviceOrRef, pathOrUrl);\n    } else {\n      throw invalidArgument(\n        'To use ref(service, url), the first argument must be a Storage instance.'\n      );\n    }\n  } else {\n    return refFromPath(serviceOrRef, pathOrUrl);\n  }\n}\n\nfunction extractBucket(\n  host: string,\n  config?: FirebaseOptions\n): Location | null {\n  const bucketString = config?.[CONFIG_STORAGE_BUCKET_KEY];\n  if (bucketString == null) {\n    return null;\n  }\n  return Location.makeFromBucketSpec(bucketString, host);\n}\n\nexport function connectStorageEmulator(\n  storage: FirebaseStorageImpl,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  storage.host = `${host}:${port}`;\n  const useSsl = isCloudWorkstation(host);\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(`https://${storage.host}/b`);\n    updateEmulatorBanner('Storage', true);\n  }\n  storage._isUsingEmulator = true;\n  storage._protocol = useSsl ? 'https' : 'http';\n  const { mockUserToken } = options;\n  if (mockUserToken) {\n    storage._overrideAuthToken =\n      typeof mockUserToken === 'string'\n        ? mockUserToken\n        : createMockUserToken(mockUserToken, storage.app.options.projectId);\n  }\n}\n\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nexport class FirebaseStorageImpl implements FirebaseStorage {\n  _bucket: Location | null = null;\n  /**\n   * This string can be in the formats:\n   * - host\n   * - host:port\n   */\n  private _host: string = DEFAULT_HOST;\n  _protocol: string = 'https';\n  protected readonly _appId: string | null = null;\n  private readonly _requests: Set<Request<unknown>>;\n  private _deleted: boolean = false;\n  private _maxOperationRetryTime: number;\n  private _maxUploadRetryTime: number;\n  _overrideAuthToken?: string;\n\n  constructor(\n    /**\n     * FirebaseApp associated with this StorageService instance.\n     */\n    readonly app: FirebaseApp,\n    readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    /**\n     * @internal\n     */\n    readonly _appCheckProvider: Provider<AppCheckInternalComponentName>,\n    /**\n     * @internal\n     */\n    readonly _url?: string,\n    readonly _firebaseVersion?: string,\n    public _isUsingEmulator = false\n  ) {\n    this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n    this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n    this._requests = new Set();\n    if (_url != null) {\n      this._bucket = Location.makeFromBucketSpec(_url, this._host);\n    } else {\n      this._bucket = extractBucket(this._host, this.app.options);\n    }\n  }\n\n  /**\n   * The host string for this service, in the form of `host` or\n   * `host:port`.\n   */\n  get host(): string {\n    return this._host;\n  }\n\n  set host(host: string) {\n    this._host = host;\n    if (this._url != null) {\n      this._bucket = Location.makeFromBucketSpec(this._url, host);\n    } else {\n      this._bucket = extractBucket(host, this.app.options);\n    }\n  }\n\n  /**\n   * The maximum time to retry uploads in milliseconds.\n   */\n  get maxUploadRetryTime(): number {\n    return this._maxUploadRetryTime;\n  }\n\n  set maxUploadRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxUploadRetryTime = time;\n  }\n\n  /**\n   * The maximum time to retry operations other than uploads or downloads in\n   * milliseconds.\n   */\n  get maxOperationRetryTime(): number {\n    return this._maxOperationRetryTime;\n  }\n\n  set maxOperationRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxOperationRetryTime = time;\n  }\n\n  async _getAuthToken(): Promise<string | null> {\n    if (this._overrideAuthToken) {\n      return this._overrideAuthToken;\n    }\n    const auth = this._authProvider.getImmediate({ optional: true });\n    if (auth) {\n      const tokenData = await auth.getToken();\n      if (tokenData !== null) {\n        return tokenData.accessToken;\n      }\n    }\n    return null;\n  }\n\n  async _getAppCheckToken(): Promise<string | null> {\n    if (_isFirebaseServerApp(this.app) && this.app.settings.appCheckToken) {\n      return this.app.settings.appCheckToken;\n    }\n    const appCheck = this._appCheckProvider.getImmediate({ optional: true });\n    if (appCheck) {\n      const result = await appCheck.getToken();\n      // TODO: What do we want to do if there is an error getting the token?\n      // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n      // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n      // the token (actual or dummy) to send requests.\n      return result.token;\n    }\n    return null;\n  }\n\n  /**\n   * Stop running requests and prevent more from being created.\n   */\n  _delete(): Promise<void> {\n    if (!this._deleted) {\n      this._deleted = true;\n      this._requests.forEach(request => request.cancel());\n      this._requests.clear();\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Returns a new firebaseStorage.Reference object referencing this StorageService\n   * at the given Location.\n   */\n  _makeStorageReference(loc: Location): Reference {\n    return new Reference(this, loc);\n  }\n\n  /**\n   * @param requestInfo - HTTP RequestInfo object\n   * @param authToken - Firebase auth token\n   */\n  _makeRequest<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>,\n    authToken: string | null,\n    appCheckToken: string | null,\n    retry = true\n  ): Request<O> {\n    if (!this._deleted) {\n      const request = makeRequest(\n        requestInfo,\n        this._appId,\n        authToken,\n        appCheckToken,\n        requestFactory,\n        this._firebaseVersion,\n        retry,\n        this._isUsingEmulator\n      );\n      this._requests.add(request);\n      // Request removes itself from set when complete.\n      request.getPromise().then(\n        () => this._requests.delete(request),\n        () => this._requests.delete(request)\n      );\n      return request;\n    } else {\n      return new FailRequest(appDeleted());\n    }\n  }\n\n  async makeRequestWithTokens<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>\n  ): Promise<O> {\n    const [authToken, appCheckToken] = await Promise.all([\n      this._getAuthToken(),\n      this._getAppCheckToken()\n    ]);\n\n    return this._makeRequest(\n      requestInfo,\n      requestFactory,\n      authToken,\n      appCheckToken\n    ).getPromise();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Storage.\n */\nexport const STORAGE_TYPE = 'storage';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\n\nimport {\n  ref as refInternal,\n  FirebaseStorageImpl,\n  connectStorageEmulator as connectEmulatorInternal\n} from './service';\nimport { Provider } from '@firebase/component';\n\nimport {\n  StorageReference,\n  FirebaseStorage,\n  UploadResult,\n  ListOptions,\n  ListResult,\n  UploadTask,\n  SettableMetadata,\n  UploadMetadata,\n  FullMetadata\n} from './public-types';\nimport { Metadata as MetadataInternal } from './metadata';\nimport {\n  uploadBytes as uploadBytesInternal,\n  uploadBytesResumable as uploadBytesResumableInternal,\n  uploadString as uploadStringInternal,\n  getMetadata as getMetadataInternal,\n  updateMetadata as updateMetadataInternal,\n  list as listInternal,\n  listAll as listAllInternal,\n  getDownloadURL as getDownloadURLInternal,\n  deleteObject as deleteObjectInternal,\n  Reference,\n  _getChild as _getChildInternal,\n  getBytesInternal\n} from './reference';\nimport { STORAGE_TYPE } from './constants';\nimport {\n  EmulatorMockTokenOptions,\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\nimport { StringFormat } from './implementation/string';\n\nexport { EmulatorMockTokenOptions } from '@firebase/util';\n\nexport { StorageError, StorageErrorCode } from './implementation/error';\n\n/**\n * Public types.\n */\nexport * from './public-types';\n\nexport { Location as _Location } from './implementation/location';\nexport { UploadTask as _UploadTask } from './task';\nexport type { Reference as _Reference } from './reference';\nexport type { FirebaseStorageImpl as _FirebaseStorageImpl } from './service';\nexport { FbsBlob as _FbsBlob } from './implementation/blob';\nexport { dataFromString as _dataFromString } from './implementation/string';\nexport {\n  invalidRootOperation as _invalidRootOperation,\n  invalidArgument as _invalidArgument\n} from './implementation/error';\nexport {\n  TaskEvent as _TaskEvent,\n  TaskState as _TaskState\n} from './implementation/taskenums';\nexport { StringFormat };\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nexport function getBytes(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref = getModularInstance(ref);\n  return getBytesInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadBytesInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: StorageReference,\n  value: string,\n  format?: StringFormat,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadStringInternal(\n    ref as Reference,\n    value,\n    format,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): UploadTask {\n  ref = getModularInstance(ref);\n  return uploadBytesResumableInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  ) as UploadTask;\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nexport function getMetadata(ref: StorageReference): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return getMetadataInternal(ref as Reference) as Promise<FullMetadata>;\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nexport function updateMetadata(\n  ref: StorageReference,\n  metadata: SettableMetadata\n): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return updateMetadataInternal(\n    ref as Reference,\n    metadata as Partial<MetadataInternal>\n  ) as Promise<FullMetadata>;\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: StorageReference,\n  options?: ListOptions\n): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listInternal(ref as Reference, options);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: StorageReference): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listAllInternal(ref as Reference);\n}\n\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: StorageReference): Promise<string> {\n  ref = getModularInstance(ref);\n  return getDownloadURLInternal(ref as Reference);\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: StorageReference): Promise<void> {\n  ref = getModularInstance(ref);\n  return deleteObjectInternal(ref as Reference);\n}\n\n/**\n * Returns a {@link StorageReference} for the given url.\n * @param storage - {@link FirebaseStorage} instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorage, url?: string): StorageReference;\n/**\n * Returns a {@link StorageReference} for the given path in the\n * default bucket.\n * @param storageOrRef - {@link FirebaseStorage} or {@link StorageReference}.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if {@link FirebaseStorage}\n * instance provided) or returns same reference (if {@link StorageReference} provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorage | StorageReference,\n  path?: string\n): StorageReference;\nexport function ref(\n  serviceOrRef: FirebaseStorage | StorageReference,\n  pathOrUrl?: string\n): StorageReference | null {\n  serviceOrRef = getModularInstance(serviceOrRef);\n  return refInternal(\n    serviceOrRef as FirebaseStorageImpl | Reference,\n    pathOrUrl\n  );\n}\n\n/**\n * @internal\n */\nexport function _getChild(ref: StorageReference, childPath: string): Reference {\n  return _getChildInternal(ref as Reference, childPath);\n}\n\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nexport function getStorage(\n  app: FirebaseApp = getApp(),\n  bucketUrl?: string\n): FirebaseStorage {\n  app = getModularInstance(app);\n  const storageProvider: Provider<'storage'> = _getProvider(app, STORAGE_TYPE);\n  const storageInstance = storageProvider.getImmediate({\n    identifier: bucketUrl\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('storage');\n  if (emulator) {\n    connectStorageEmulator(storageInstance, ...emulator);\n  }\n  return storageInstance;\n}\n\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nexport function connectStorageEmulator(\n  storage: FirebaseStorage,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  connectEmulatorInternal(storage as FirebaseStorageImpl, host, port, options);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { StorageReference } from './public-types';\nimport { Reference, getBlobInternal } from './reference';\nimport { getModularInstance } from '@firebase/util';\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * This API is not available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise that resolves with a Blob containing the object's bytes\n */\nexport function getBlob(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref = getModularInstance(ref);\n  return getBlobInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Downloads the data at the object's location. Raises an error event if the\n * object is not found.\n *\n * This API is only available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A stream with the object's data as bytes\n */\nexport function getStream(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  throw new Error('getStream() is only supported by NodeJS builds');\n}\n", "/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\n\nimport { FirebaseStorageImpl } from '../src/service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\nimport { FirebaseStorage } from './public-types';\nimport { STORAGE_TYPE } from './constants';\n\nexport * from './api';\nexport * from './api.browser';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): FirebaseStorage {\n  const app = container.getProvider('app').getImmediate();\n  const authProvider = container.getProvider('auth-internal');\n  const appCheckProvider = container.getProvider('app-check-internal');\n\n  return new FirebaseStorageImpl(\n    app,\n    authProvider,\n    appCheckProvider,\n    url,\n    SDK_VERSION\n  );\n}\n\nfunction registerStorage(): void {\n  _registerComponent(\n    new Component(\n      STORAGE_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  //RUNTIME_ENV will be replaced during the compilation to \"node\" for nodejs and an empty string for browser\n  registerVersion(name, version, '__RUNTIME_ENV__');\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterStorage();\n"], "names": ["getBlob", "getMetadata", "list", "getBytes", "updateMetadata", "deleteObject", "fbsAsync", "uploadBytes", "uploadBytesResumable", "uploadString", "listAll", "requestsList", "requestsGetMetadata", "requestsUpdateMetadata", "getDownloadURL", "requestsGetDownloadUrl", "requestsDeleteObject", "_get<PERSON><PERSON>d", "ref", "connectStorageEmulator", "uploadBytesInternal", "uploadStringInternal", "uploadBytesResumableInternal", "getMetadataInternal", "updateMetadataInternal", "listInternal", "listAllInternal", "getDownloadURLInternal", "deleteObjectInternal", "refInternal", "_getChildInternal", "connectEmulatorInternal"], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;AAeG;AACH;;AAEG;AAEH;;AAEG;AACI,MAAM,YAAY,GAAG,gCAAgC,CAAC;AAE7D;;AAEG;AACI,MAAM,yBAAyB,GAAG,eAAe,CAAC;AAEzD;;;;AAIG;AACI,MAAM,gCAAgC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAE9D;;;;AAIG;AACI,MAAM,6BAA6B,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAE5D;;AAEG;AACI,MAAM,6BAA6B,GAAG,IAAI;;AC/CjD;;;;;;;;;;;;;;;AAeG;AAMH;;;AAGG;AACG,MAAO,YAAa,SAAQ,aAAa,CAAA;AAO7C;;;;;AAKG;AACH,IAAA,WAAA,CAAY,IAAsB,EAAE,OAAe,EAAU,UAAU,CAAC,EAAA;AACtE,QAAA,KAAK,CACH,WAAW,CAAC,IAAI,CAAC,EACjB,CAAA,kBAAA,EAAqB,OAAO,CAAA,EAAA,EAAK,WAAW,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,CACtD,CAAC;QAJyD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAI;AAXxE;;AAEG;AACH,QAAA,IAAA,CAAA,UAAU,GAAsC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC;AAavE,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC;;;QAGjC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;KACrD;AAED,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IAED,IAAI,MAAM,CAAC,MAAc,EAAA;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;KACvB;AAED;;AAEG;AACH,IAAA,WAAW,CAAC,IAAsB,EAAA;QAChC,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC;KACxC;AAED;;AAEG;AACH,IAAA,IAAI,cAAc,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;KACvC;IAED,IAAI,cAAc,CAAC,cAA6B,EAAA;AAC9C,QAAA,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,cAAc,CAAC;AAChD,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,GAAG,CAAA,EAAG,IAAI,CAAC,YAAY,CAAK,EAAA,EAAA,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;SAC1E;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;SAClC;KACF;AACF,CAAA;AAID;;;AAGG;IACS,iBA4BX;AA5BD,CAAA,UAAY,gBAAgB,EAAA;;AAE1B,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,gBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,gBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,gBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC,IAAA,gBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;AAC7B,IAAA,gBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,gBAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAC7C,IAAA,gBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,gBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;;AAErB,IAAA,gBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;AACzC,IAAA,gBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,gBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AACjD,IAAA,gBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC,IAAA,gBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC,IAAA,gBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AACjD,IAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC,IAAA,gBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,gBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AACjD,IAAA,gBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,gBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AACjD,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,gBAAA,CAAA,yBAAA,CAAA,GAAA,yBAAmD,CAAA;AACrD,CAAC,EA5BW,gBAAgB,KAAhB,gBAAgB,GA4B3B,EAAA,CAAA,CAAA,CAAA;AAEK,SAAU,WAAW,CAAC,IAAsB,EAAA;IAChD,OAAO,UAAU,GAAG,IAAI,CAAC;AAC3B,CAAC;SAEe,OAAO,GAAA;IACrB,MAAM,OAAO,GACX,gEAAgE;AAChE,QAAA,kBAAkB,CAAC;IACrB,OAAO,IAAI,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC7D,CAAC;AAEK,SAAU,cAAc,CAAC,IAAY,EAAA;AACzC,IAAA,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,gBAAgB,EACjC,UAAU,GAAG,IAAI,GAAG,mBAAmB,CACxC,CAAC;AACJ,CAAC;AAgBK,SAAU,aAAa,CAAC,MAAc,EAAA;AAC1C,IAAA,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,cAAc,EAC/B,oBAAoB;QAClB,MAAM;QACN,mCAAmC;AACnC,QAAA,uCAAuC,CAC1C,CAAC;AACJ,CAAC;SAEe,eAAe,GAAA;IAC7B,MAAM,OAAO,GACX,gEAAgE;AAChE,QAAA,+BAA+B,CAAC;IAClC,OAAO,IAAI,YAAY,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC;SAEe,eAAe,GAAA;IAC7B,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,gBAAgB,EACjC,+EAA+E,CAChF,CAAC;AACJ,CAAC;AAEK,SAAU,YAAY,CAAC,IAAY,EAAA;AACvC,IAAA,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,YAAY,EAC7B,2CAA2C,GAAG,IAAI,GAAG,IAAI,CAC1D,CAAC;AACJ,CAAC;SAEe,kBAAkB,GAAA;IAChC,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,oBAAoB,EACrC,0DAA0D,CAC3D,CAAC;AACJ,CAAC;SAmBe,QAAQ,GAAA;IACtB,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,QAAQ,EACzB,oCAAoC,CACrC,CAAC;AACJ,CAAC;AASK,SAAU,UAAU,CAAC,GAAW,EAAA;AACpC,IAAA,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,WAAW,EAC5B,eAAe,GAAG,GAAG,GAAG,IAAI,CAC7B,CAAC;AACJ,CAAC;AAEK,SAAU,oBAAoB,CAAC,MAAc,EAAA;AACjD,IAAA,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,sBAAsB,EACvC,0BAA0B,GAAG,MAAM,GAAG,IAAI,CAC3C,CAAC;AACJ,CAAC;SAEe,eAAe,GAAA;AAC7B,IAAA,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,iBAAiB,EAClC,oBAAoB;QAClB,0BAA0B;QAC1B,yBAAyB;AACzB,QAAA,uCAAuC,CAC1C,CAAC;AACJ,CAAC;SAEe,eAAe,GAAA;IAC7B,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,iBAAiB,EAClC,wDAAwD,CACzD,CAAC;AACJ,CAAC;SAEe,mBAAmB,GAAA;IACjC,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,sBAAsB,EACvC,sEAAsE,CACvE,CAAC;AACJ,CAAC;SAEe,aAAa,GAAA;IAC3B,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,eAAe,EAChC,iDAAiD,CAClD,CAAC;AACJ,CAAC;AAEK,SAAU,eAAe,CAAC,QAAgB,EAAA;IAC9C,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,uBAAuB,EACxC,CAAG,EAAA,QAAQ,CAAwJ,sJAAA,CAAA,CACpK,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,eAAe,CAAC,OAAe,EAAA;IAC7C,OAAO,IAAI,YAAY,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;SA+Be,UAAU,GAAA;IACxB,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,WAAW,EAC5B,+BAA+B,CAChC,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,oBAAoB,CAAC,IAAY,EAAA;AAC/C,IAAA,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,sBAAsB,EACvC,iBAAiB;QACf,IAAI;QACJ,+DAA+D;AAC/D,QAAA,oDAAoD,CACvD,CAAC;AACJ,CAAC;AAED;;;AAGG;AACa,SAAA,aAAa,CAAC,MAAc,EAAE,OAAe,EAAA;AAC3D,IAAA,OAAO,IAAI,YAAY,CACrB,gBAAgB,CAAC,cAAc,EAC/B,gCAAgC,GAAG,MAAM,GAAG,KAAK,GAAG,OAAO,CAC5D,CAAC;AACJ,CAAC;AASD;;AAEG;AACG,SAAU,aAAa,CAAC,OAAe,EAAA;IAC3C,MAAM,IAAI,YAAY,CACpB,gBAAgB,CAAC,cAAc,EAC/B,kBAAkB,GAAG,OAAO,CAC7B,CAAC;AACJ;;AClWA;;;;;;;;;;;;;;;AAeG;AAUH;;;;AAIG;MACU,QAAQ,CAAA;IAGnB,WAA4B,CAAA,MAAc,EAAE,IAAY,EAAA;QAA5B,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;AACxC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;KACnB;AAED,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAED,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;KAC/B;IAED,aAAa,GAAA;QACX,MAAM,MAAM,GAAG,kBAAkB,CAAC;AAClC,QAAA,OAAO,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAChE;IAED,mBAAmB,GAAA;QACjB,MAAM,MAAM,GAAG,kBAAkB,CAAC;QAClC,OAAO,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;KAC3C;AAED,IAAA,OAAO,kBAAkB,CAAC,YAAoB,EAAE,IAAY,EAAA;AAC1D,QAAA,IAAI,cAAc,CAAC;AACnB,QAAA,IAAI;YACF,cAAc,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SAC3D;QAAC,OAAO,CAAC,EAAE;;;AAGV,YAAA,OAAO,IAAI,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;SACvC;AACD,QAAA,IAAI,cAAc,CAAC,IAAI,KAAK,EAAE,EAAE;AAC9B,YAAA,OAAO,cAAc,CAAC;SACvB;aAAM;AACL,YAAA,MAAM,oBAAoB,CAAC,YAAY,CAAC,CAAC;SAC1C;KACF;AAED,IAAA,OAAO,WAAW,CAAC,GAAW,EAAE,IAAY,EAAA;QAC1C,IAAI,QAAQ,GAAoB,IAAI,CAAC;QACrC,MAAM,YAAY,GAAG,qBAAqB,CAAC;QAE3C,SAAS,QAAQ,CAAC,GAAa,EAAA;AAC7B,YAAA,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;AAChD,gBAAA,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACpC;SACF;QACD,MAAM,MAAM,GAAG,WAAW,CAAC;AAC3B,QAAA,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,QAAQ,GAAG,YAAY,GAAG,MAAM,EAAE,GAAG,CAAC,CAAC;QAClE,MAAM,SAAS,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;QAEzC,SAAS,UAAU,CAAC,GAAa,EAAA;YAC/B,GAAG,CAAC,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC1C;QACD,MAAM,OAAO,GAAG,gBAAgB,CAAC;QACjC,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,QAAA,MAAM,qBAAqB,GAAG,IAAI,MAAM,CACtC,aAAa,mBAAmB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,EAAM,YAAY,CAAK,EAAA,EAAA,mBAAmB,EAAE,EACvF,GAAG,CACJ,CAAC;QACF,MAAM,sBAAsB,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;AAEtD,QAAA,MAAM,gBAAgB,GACpB,IAAI,KAAK,YAAY;AACnB,cAAE,qDAAqD;cACrD,IAAI,CAAC;QACX,MAAM,gBAAgB,GAAG,UAAU,CAAC;AACpC,QAAA,MAAM,kBAAkB,GAAG,IAAI,MAAM,CACnC,CAAa,UAAA,EAAA,gBAAgB,CAAI,CAAA,EAAA,YAAY,IAAI,gBAAgB,CAAA,CAAE,EACnE,GAAG,CACJ,CAAC;QACF,MAAM,mBAAmB,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;AAEnD,QAAA,MAAM,MAAM,GAAG;YACb,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE;AAC5D,YAAA;AACE,gBAAA,KAAK,EAAE,qBAAqB;AAC5B,gBAAA,OAAO,EAAE,sBAAsB;AAC/B,gBAAA,UAAU,EAAE,UAAU;AACvB,aAAA;AACD,YAAA;AACE,gBAAA,KAAK,EAAE,kBAAkB;AACzB,gBAAA,OAAO,EAAE,mBAAmB;AAC5B,gBAAA,UAAU,EAAE,UAAU;AACvB,aAAA;SACF,CAAC;AACF,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,QAAQ,EAAE;gBACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACnD,IAAI,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC7C,IAAI,CAAC,SAAS,EAAE;oBACd,SAAS,GAAG,EAAE,CAAC;iBAChB;gBACD,QAAQ,GAAG,IAAI,QAAQ,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAChD,gBAAA,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC3B,MAAM;aACP;SACF;AACD,QAAA,IAAI,QAAQ,IAAI,IAAI,EAAE;AACpB,YAAA,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC;SACvB;AACD,QAAA,OAAO,QAAQ,CAAC;KACjB;AACF;;ACxHD;;AAEG;MACU,WAAW,CAAA;AAGtB,IAAA,WAAA,CAAY,KAAmB,EAAA;QAC7B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAI,KAAK,CAAC,CAAC;KAC1C;;IAGD,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;;AAGD,IAAA,MAAM,CAAC,UAAU,GAAG,KAAK,KAAU;AACpC;;ACpCD;;;;;;;;;;;;;;;AAeG;AAUH;;;;;;;;;;;AAWG;AACG,SAAU,KAAK,CACnB,SAGS;AACT;AACA,iBAA8C,EAC9C,OAAe,EAAA;;;IAIf,IAAI,WAAW,GAAG,CAAC,CAAC;;;;IAIpB,IAAI,cAAc,GAAQ,IAAI,CAAC;;IAE/B,IAAI,eAAe,GAAQ,IAAI,CAAC;IAChC,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI,WAAW,GAAG,CAAC,CAAC;AAEpB,IAAA,SAAS,QAAQ,GAAA;QACf,OAAO,WAAW,KAAK,CAAC,CAAC;KAC1B;IACD,IAAI,iBAAiB,GAAG,KAAK,CAAC;IAE9B,SAAS,eAAe,CAAC,GAAG,IAAW,EAAA;QACrC,IAAI,CAAC,iBAAiB,EAAE;YACtB,iBAAiB,GAAG,IAAI,CAAC;AACzB,YAAA,iBAAiB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACrC;KACF;IAED,SAAS,aAAa,CAAC,MAAc,EAAA;AACnC,QAAA,cAAc,GAAG,UAAU,CAAC,MAAK;YAC/B,cAAc,GAAG,IAAI,CAAC;AACtB,YAAA,SAAS,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC,CAAC;SACxC,EAAE,MAAM,CAAC,CAAC;KACZ;AAED,IAAA,SAAS,kBAAkB,GAAA;QACzB,IAAI,eAAe,EAAE;YACnB,YAAY,CAAC,eAAe,CAAC,CAAC;SAC/B;KACF;AAED,IAAA,SAAS,eAAe,CAAC,OAAgB,EAAE,GAAG,IAAW,EAAA;QACvD,IAAI,iBAAiB,EAAE;AACrB,YAAA,kBAAkB,EAAE,CAAC;YACrB,OAAO;SACR;QACD,IAAI,OAAO,EAAE;AACX,YAAA,kBAAkB,EAAE,CAAC;YACrB,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YAC7C,OAAO;SACR;AACD,QAAA,MAAM,QAAQ,GAAG,QAAQ,EAAE,IAAI,UAAU,CAAC;QAC1C,IAAI,QAAQ,EAAE;AACZ,YAAA,kBAAkB,EAAE,CAAC;YACrB,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YAC7C,OAAO;SACR;AACD,QAAA,IAAI,WAAW,GAAG,EAAE,EAAE;;YAEpB,WAAW,IAAI,CAAC,CAAC;SAClB;AACD,QAAA,IAAI,UAAU,CAAC;AACf,QAAA,IAAI,WAAW,KAAK,CAAC,EAAE;YACrB,WAAW,GAAG,CAAC,CAAC;YAChB,UAAU,GAAG,CAAC,CAAC;SAChB;aAAM;YACL,UAAU,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC;SACnD;QACD,aAAa,CAAC,UAAU,CAAC,CAAC;KAC3B;IACD,IAAI,OAAO,GAAG,KAAK,CAAC;IAEpB,SAAS,IAAI,CAAC,UAAmB,EAAA;QAC/B,IAAI,OAAO,EAAE;YACX,OAAO;SACR;QACD,OAAO,GAAG,IAAI,CAAC;AACf,QAAA,kBAAkB,EAAE,CAAC;QACrB,IAAI,iBAAiB,EAAE;YACrB,OAAO;SACR;AACD,QAAA,IAAI,cAAc,KAAK,IAAI,EAAE;YAC3B,IAAI,CAAC,UAAU,EAAE;gBACf,WAAW,GAAG,CAAC,CAAC;aACjB;YACD,YAAY,CAAC,cAAc,CAAC,CAAC;YAC7B,aAAa,CAAC,CAAC,CAAC,CAAC;SAClB;aAAM;YACL,IAAI,CAAC,UAAU,EAAE;gBACf,WAAW,GAAG,CAAC,CAAC;aACjB;SACF;KACF;IACD,aAAa,CAAC,CAAC,CAAC,CAAC;AACjB,IAAA,eAAe,GAAG,UAAU,CAAC,MAAK;QAChC,UAAU,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,CAAC;KACZ,EAAE,OAAO,CAAC,CAAC;AACZ,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;AAMG;AACG,SAAU,IAAI,CAAC,EAAM,EAAA;IACzB,EAAE,CAAC,KAAK,CAAC,CAAC;AACZ;;ACxJA;;;;;;;;;;;;;;;AAeG;AAIG,SAAU,SAAS,CAAI,CAAuB,EAAA;AAClD,IAAA,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC;AACtB,CAAC;AAED;AACM,SAAU,UAAU,CAAC,CAAU,EAAA;AACnC,IAAA,OAAO,OAAO,CAAC,KAAK,UAAU,CAAC;AACjC,CAAC;AAEK,SAAU,gBAAgB,CAAC,CAAU,EAAA;AACzC,IAAA,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAEK,SAAU,QAAQ,CAAC,CAAU,EAAA;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,YAAY,MAAM,CAAC;AACtD,CAAC;AAEK,SAAU,YAAY,CAAC,CAAU,EAAA;AACrC,IAAA,OAAO,mBAAmB,EAAE,IAAI,CAAC,YAAY,IAAI,CAAC;AACpD,CAAC;SAEe,mBAAmB,GAAA;AACjC,IAAA,OAAO,OAAO,IAAI,KAAK,WAAW,CAAC;AACrC,CAAC;AAEK,SAAU,cAAc,CAC5B,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAChB,KAAa,EAAA;AAEb,IAAA,IAAI,KAAK,GAAG,QAAQ,EAAE;QACpB,MAAM,eAAe,CACnB,CAAsB,mBAAA,EAAA,QAAQ,eAAe,QAAQ,CAAA,YAAA,CAAc,CACpE,CAAC;KACH;AACD,IAAA,IAAI,KAAK,GAAG,QAAQ,EAAE;QACpB,MAAM,eAAe,CACnB,CAAsB,mBAAA,EAAA,QAAQ,eAAe,QAAQ,CAAA,SAAA,CAAW,CACjE,CAAC;KACH;AACH;;AC5DA;;;;;;;;;;;;;;;AAeG;SAOa,OAAO,CACrB,OAAe,EACf,IAAY,EACZ,QAAgB,EAAA;IAEhB,IAAI,MAAM,GAAG,IAAI,CAAC;AAClB,IAAA,IAAI,QAAQ,IAAI,IAAI,EAAE;AACpB,QAAA,MAAM,GAAG,CAAA,QAAA,EAAW,IAAI,CAAA,CAAE,CAAC;KAC5B;AACD,IAAA,OAAO,GAAG,QAAQ,CAAA,GAAA,EAAM,MAAM,CAAM,GAAA,EAAA,OAAO,EAAE,CAAC;AAChD,CAAC;AAEK,SAAU,eAAe,CAAC,MAAiB,EAAA;IAC/C,MAAM,MAAM,GAAG,kBAAkB,CAAC;IAClC,IAAI,SAAS,GAAG,GAAG,CAAC;AACpB,IAAA,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AACxB,QAAA,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC9B,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,YAAA,SAAS,GAAG,SAAS,GAAG,QAAQ,GAAG,GAAG,CAAC;SACxC;KACF;;IAGD,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnC,IAAA,OAAO,SAAS,CAAC;AACnB;;ACsBA;;AAEG;AACH,IAAY,SAIX,CAAA;AAJD,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY,CAAA;AACZ,IAAA,SAAA,CAAA,SAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;AACjB,IAAA,SAAA,CAAA,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;AACX,CAAC,EAJW,SAAS,KAAT,SAAS,GAIpB,EAAA,CAAA,CAAA;;AC5ED;;;;;;;;;;;;;;;AAeG;AAEH;;;;;AAKG;AACa,SAAA,iBAAiB,CAC/B,MAAc,EACd,oBAA8B,EAAA;;;IAI9B,MAAM,iBAAiB,GAAG,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,CAAC;AACxD,IAAA,MAAM,eAAe,GAAG;;QAEtB,GAAG;;QAEH,GAAG;KACJ,CAAC;IACF,MAAM,gBAAgB,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAChE,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E,IAAA,OAAO,iBAAiB,IAAI,gBAAgB,IAAI,qBAAqB,CAAC;AACxE;;ACvCA;;;;;;;;;;;;;;;AAeG;AA4BH;;;;;;;AAOG;AACH,MAAM,cAAc,CAAA;IAUlB,WACU,CAAA,IAAY,EACZ,OAAe,EACf,QAAiB,EACjB,KAAwC,EACxC,aAAuB,EACvB,qBAA+B,EAC/B,SAA+B,EAC/B,cAAmC,EACnC,QAAgB,EAChB,iBAA4D,EAC5D,kBAAuC,EACvC,KAAQ,GAAA,IAAI,EACZ,eAAA,GAAkB,KAAK,EAAA;QAZvB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACZ,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QACjB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAmC;QACxC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAU;QACvB,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAAU;QAC/B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAsB;QAC/B,IAAc,CAAA,cAAA,GAAd,cAAc,CAAqB;QACnC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;QAChB,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAA2C;QAC5D,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAqB;QACvC,IAAK,CAAA,KAAA,GAAL,KAAK,CAAO;QACZ,IAAe,CAAA,eAAA,GAAf,eAAe,CAAQ;QAtBzB,IAAkB,CAAA,kBAAA,GAAyB,IAAI,CAAC;QAChD,IAAU,CAAA,UAAA,GAAqB,IAAI,CAAC;QAIpC,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAC3B,IAAU,CAAA,UAAA,GAAY,KAAK,CAAC;QAkBlC,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AAC9C,YAAA,IAAI,CAAC,QAAQ,GAAG,OAA+C,CAAC;AAChE,YAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB,SAAC,CAAC,CAAC;KACJ;AAED;;AAEG;IACK,MAAM,GAAA;AACZ,QAAA,MAAM,YAAY,GAGN,CAAC,eAAe,EAAE,QAAQ,KAAI;YACxC,IAAI,QAAQ,EAAE;AACZ,gBAAA,eAAe,CAAC,KAAK,EAAE,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAChE,OAAO;aACR;AACD,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7C,YAAA,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;AAErC,YAAA,MAAM,gBAAgB,GAEV,aAAa,IAAG;AAC1B,gBAAA,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;AACpC,gBAAA,MAAM,KAAK,GAAG,aAAa,CAAC,gBAAgB,GAAG,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACxE,gBAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;AACnC,oBAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;iBACvC;AACH,aAAC,CAAC;AACF,YAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;AACnC,gBAAA,UAAU,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;aACxD;;;YAID,UAAU;iBACP,IAAI,CACH,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,CACd;iBACA,IAAI,CAAC,MAAK;AACT,gBAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;AACnC,oBAAA,UAAU,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAC;iBAC3D;AACD,gBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,EAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;AACnE,gBAAA,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;AACtC,gBAAA,IACE,CAAC,SAAS;AACV,qBAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC;AACpD,wBAAA,IAAI,CAAC,KAAK,CAAC,EACb;oBACA,MAAM,WAAW,GAAG,UAAU,CAAC,YAAY,EAAE,KAAK,SAAS,CAAC,KAAK,CAAC;AAClE,oBAAA,eAAe,CACb,KAAK,EACL,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC,CAC/C,CAAC;oBACF,OAAO;iBACR;AACD,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC9D,eAAe,CAAC,IAAI,EAAE,IAAI,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;AACvE,aAAC,CAAC,CAAC;AACP,SAAC,CAAC;AAEF;;;AAGG;AACH,QAAA,MAAM,WAAW,GAGL,CAAC,kBAAkB,EAAE,MAAM,KAAI;AACzC,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9B,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,YAAA,MAAM,UAAU,GAAG,MAAM,CAAC,UAA2B,CAAC;AACtD,YAAA,IAAI,MAAM,CAAC,cAAc,EAAE;AACzB,gBAAA,IAAI;AACF,oBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;AACpE,oBAAA,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;wBACrB,OAAO,CAAC,MAAM,CAAC,CAAC;qBACjB;yBAAM;AACL,wBAAA,OAAO,EAAE,CAAC;qBACX;iBACF;gBAAC,OAAO,CAAC,EAAE;oBACV,MAAM,CAAC,CAAC,CAAC,CAAC;iBACX;aACF;iBAAM;AACL,gBAAA,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,oBAAA,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;AACtB,oBAAA,GAAG,CAAC,cAAc,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;AAC/C,oBAAA,IAAI,IAAI,CAAC,cAAc,EAAE;wBACvB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;qBAC9C;yBAAM;wBACL,MAAM,CAAC,GAAG,CAAC,CAAC;qBACb;iBACF;qBAAM;AACL,oBAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;AACnB,wBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,EAAE,GAAG,QAAQ,EAAE,CAAC;wBACxD,MAAM,CAAC,GAAG,CAAC,CAAC;qBACb;yBAAM;AACL,wBAAA,MAAM,GAAG,GAAG,kBAAkB,EAAE,CAAC;wBACjC,MAAM,CAAC,GAAG,CAAC,CAAC;qBACb;iBACF;aACF;AACH,SAAC,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,WAAW,CAAC,KAAK,EAAE,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SAC7D;aAAM;AACL,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnE;KACF;;IAGD,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;;AAGD,IAAA,MAAM,CAAC,SAAmB,EAAA;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,IAAI,KAAK,CAAC;AACrC,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AAC5B,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACvB;AACD,QAAA,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,EAAE;AACpC,YAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;SACjC;KACF;AACF,CAAA;AAED;;;AAGG;MACU,gBAAgB,CAAA;AAM3B,IAAA,WAAA,CACS,cAAuB,EACvB,UAAgC,EACvC,QAAkB,EAAA;QAFX,IAAc,CAAA,cAAA,GAAd,cAAc,CAAS;QACvB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAsB;AAGvC,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;KAC5B;AACF,CAAA;AAEe,SAAA,cAAc,CAC5B,OAAgB,EAChB,SAAwB,EAAA;IAExB,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9C,QAAA,OAAO,CAAC,eAAe,CAAC,GAAG,WAAW,GAAG,SAAS,CAAC;KACpD;AACH,CAAC;AAEe,SAAA,iBAAiB,CAC/B,OAAgB,EAChB,eAAwB,EAAA;IAExB,OAAO,CAAC,4BAA4B,CAAC;AACnC,QAAA,QAAQ,IAAI,eAAe,IAAI,YAAY,CAAC,CAAC;AACjD,CAAC;AAEe,SAAA,eAAe,CAAC,OAAgB,EAAE,KAAoB,EAAA;IACpE,IAAI,KAAK,EAAE;AACT,QAAA,OAAO,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;KACrC;AACH,CAAC;AAEe,SAAA,kBAAkB,CAChC,OAAgB,EAChB,aAA4B,EAAA;AAE5B,IAAA,IAAI,aAAa,KAAK,IAAI,EAAE;AAC1B,QAAA,OAAO,CAAC,qBAAqB,CAAC,GAAG,aAAa,CAAC;KAChD;AACH,CAAC;AAEK,SAAU,WAAW,CACzB,WAA8B,EAC9B,KAAoB,EACpB,SAAwB,EACxB,aAA4B,EAC5B,cAAmC,EACnC,eAAwB,EACxB,KAAK,GAAG,IAAI,EACZ,eAAe,GAAG,KAAK,EAAA;IAEvB,MAAM,SAAS,GAAG,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACzD,IAAA,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,GAAG,SAAS,CAAC;AACxC,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;AACvD,IAAA,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAChC,IAAA,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACnC,IAAA,iBAAiB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AAC5C,IAAA,kBAAkB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IAC3C,OAAO,IAAI,cAAc,CACvB,GAAG,EACH,WAAW,CAAC,MAAM,EAClB,OAAO,EACP,WAAW,CAAC,IAAI,EAChB,WAAW,CAAC,YAAY,EACxB,WAAW,CAAC,oBAAoB,EAChC,WAAW,CAAC,OAAO,EACnB,WAAW,CAAC,YAAY,EACxB,WAAW,CAAC,OAAO,EACnB,WAAW,CAAC,gBAAgB,EAC5B,cAAc,EACd,KAAK,EACL,eAAe,CAChB,CAAC;AACJ;;ACvSA;;;;;;;;;;;;;;;AAeG;AASH,SAAS,cAAc,GAAA;AACrB,IAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;AACtC,QAAA,OAAO,WAAW,CAAC;KACpB;AAAM,SAAA,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE;AACnD,QAAA,OAAO,iBAAiB,CAAC;KAC1B;SAAM;AACL,QAAA,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED;;;;;AAKG;AACa,SAAAA,SAAO,CAAC,GAAG,IAAwC,EAAA;AACjE,IAAA,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AACrC,IAAA,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,QAAA,MAAM,EAAE,GAAG,IAAI,WAAW,EAAE,CAAC;AAC7B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACpB;AACD,QAAA,OAAO,EAAE,CAAC,OAAO,EAAE,CAAC;KACrB;SAAM;QACL,IAAI,mBAAmB,EAAE,EAAE;AACzB,YAAA,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;SACvB;aAAM;YACL,MAAM,IAAI,YAAY,CACpB,gBAAgB,CAAC,uBAAuB,EACxC,qDAAqD,CACtD,CAAC;SACH;KACF;AACH,CAAC;AAED;;;;;;;;AAQG;SACa,SAAS,CAAC,IAAU,EAAE,KAAa,EAAE,GAAW,EAAA;AAC9D,IAAA,IAAI,IAAI,CAAC,WAAW,EAAE;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACrC;AAAM,SAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAClC;AAAM,SAAA,IAAI,IAAI,CAAC,KAAK,EAAE;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAC/B;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AC9EA;;;;;;;;;;;;;;;AAeG;AAIH;AACM,SAAU,YAAY,CAAC,OAAe,EAAA;AAC1C,IAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC/B,QAAA,MAAM,eAAe,CAAC,SAAS,CAAC,CAAC;KAClC;AACD,IAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB;;ACzBA;;;;;;;;;;;;;;;AAeG;AAUH;;;AAGG;AACU,MAAA,YAAY,GAAG;AAC1B;;;;;;AAMG;AACH,IAAA,GAAG,EAAE,KAAK;AACV;;;;;AAKG;AACH,IAAA,MAAM,EAAE,QAAQ;AAChB;;;;;AAKG;AACH,IAAA,SAAS,EAAE,WAAW;AACtB;;;;;;;;AAQG;AACH,IAAA,QAAQ,EAAE,UAAU;EACX;MAEE,UAAU,CAAA;IAGrB,WAAmB,CAAA,IAAgB,EAAE,WAA2B,EAAA;QAA7C,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAY;AACjC,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC;KACxC;AACF,CAAA;AAED;;AAEG;AACa,SAAA,cAAc,CAC5B,MAAoB,EACpB,UAAkB,EAAA;IAElB,QAAQ,MAAM;QACZ,KAAK,YAAY,CAAC,GAAG;YACnB,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;QAChD,KAAK,YAAY,CAAC,MAAM,CAAC;QACzB,KAAK,YAAY,CAAC,SAAS;YACzB,OAAO,IAAI,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;QAC1D,KAAK,YAAY,CAAC,QAAQ;AACxB,YAAA,OAAO,IAAI,UAAU,CACnB,aAAa,CAAC,UAAU,CAAC,EACzB,mBAAmB,CAAC,UAAU,CAAC,CAChC,CAAC;;KAGL;;IAGD,MAAM,OAAO,EAAE,CAAC;AAClB,CAAC;AAEK,SAAU,UAAU,CAAC,KAAa,EAAA;IACtC,MAAM,CAAC,GAAa,EAAE,CAAC;AACvB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,IAAI,GAAG,EAAE;AACZ,YAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACX;aAAM;AACL,YAAA,IAAI,CAAC,IAAI,IAAI,EAAE;AACb,gBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;aACxC;iBAAM;gBACL,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,EAAE;;oBAEzB,MAAM,KAAK,GACT,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,CAAC;oBACtE,IAAI,CAAC,KAAK,EAAE;;wBAEV,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;qBACvB;yBAAM;wBACL,MAAM,EAAE,GAAG,CAAC,CAAC;wBACb,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,wBAAA,CAAC,GAAG,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAC9C,wBAAA,CAAC,CAAC,IAAI,CACJ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EACf,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EACtB,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EACrB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CACf,CAAC;qBACH;iBACF;qBAAM;oBACL,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,EAAE;;wBAEzB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;qBACvB;yBAAM;AACL,wBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;qBAChE;iBACF;aACF;SACF;KACF;AACD,IAAA,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC;AAEK,SAAU,oBAAoB,CAAC,KAAa,EAAA;AAChD,IAAA,IAAI,OAAO,CAAC;AACZ,IAAA,IAAI;AACF,QAAA,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;KACrC;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;KACnE;AACD,IAAA,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAEe,SAAA,YAAY,CAAC,MAAoB,EAAE,KAAa,EAAA;IAC9D,QAAQ,MAAM;AACZ,QAAA,KAAK,YAAY,CAAC,MAAM,EAAE;YACxB,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3C,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3C,YAAA,IAAI,QAAQ,IAAI,QAAQ,EAAE;gBACxB,MAAM,WAAW,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,gBAAA,MAAM,aAAa,CACjB,MAAM,EACN,qBAAqB;oBACnB,WAAW;AACX,oBAAA,mCAAmC,CACtC,CAAC;aACH;YACD,MAAM;SACP;AACD,QAAA,KAAK,YAAY,CAAC,SAAS,EAAE;YAC3B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1C,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3C,YAAA,IAAI,OAAO,IAAI,QAAQ,EAAE;gBACvB,MAAM,WAAW,GAAG,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC;gBACxC,MAAM,aAAa,CACjB,MAAM,EACN,qBAAqB,GAAG,WAAW,GAAG,gCAAgC,CACvE,CAAC;aACH;AACD,YAAA,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACpD,MAAM;SACP;;KAGF;AACD,IAAA,IAAI,KAAK,CAAC;AACV,IAAA,IAAI;AACF,QAAA,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;KAC7B;IAAC,OAAO,CAAC,EAAE;QACV,IAAK,CAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC7C,YAAA,MAAM,CAAC,CAAC;SACT;AACD,QAAA,MAAM,aAAa,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC;KACxD;IACD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC3C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KAChC;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,YAAY,CAAA;AAKhB,IAAA,WAAA,CAAY,OAAe,EAAA;QAJ3B,IAAM,CAAA,MAAA,GAAY,KAAK,CAAC;QACxB,IAAW,CAAA,WAAA,GAAkB,IAAI,CAAC;QAIhC,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACjD,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,MAAM,aAAa,CACjB,YAAY,CAAC,QAAQ,EACrB,uDAAuD,CACxD,CAAC;SACH;QACD,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAClC,QAAA,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM;AAC5B,kBAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;kBACrD,MAAM,CAAC;SACZ;AACD,QAAA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KACzD;AACF,CAAA;AAEK,SAAU,aAAa,CAAC,OAAe,EAAA;AAC3C,IAAA,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;AACxC,IAAA,IAAI,KAAK,CAAC,MAAM,EAAE;QAChB,OAAO,YAAY,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;KACtD;SAAM;AACL,QAAA,OAAO,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACzC;AACH,CAAC;AAEK,SAAU,mBAAmB,CAAC,OAAe,EAAA;AACjD,IAAA,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;IACxC,OAAO,KAAK,CAAC,WAAW,CAAC;AAC3B,CAAC;AAED,SAAS,QAAQ,CAAC,CAAS,EAAE,GAAW,EAAA;IACtC,MAAM,UAAU,GAAG,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC;IAC1C,IAAI,CAAC,UAAU,EAAE;AACf,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC;AACpD;;ACnPA;;;;;;;;;;;;;;;AAeG;AAWH;;;;;;AAMG;MACU,OAAO,CAAA;IAKlB,WAAY,CAAA,IAAqC,EAAE,SAAmB,EAAA;QACpE,IAAI,IAAI,GAAW,CAAC,CAAC;QACrB,IAAI,QAAQ,GAAW,EAAE,CAAC;AAC1B,QAAA,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACtB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAY,CAAC;AAC1B,YAAA,IAAI,GAAI,IAAa,CAAC,IAAI,CAAC;AAC3B,YAAA,QAAQ,GAAI,IAAa,CAAC,IAAI,CAAC;SAChC;AAAM,aAAA,IAAI,IAAI,YAAY,WAAW,EAAE;YACtC,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;aACnC;iBAAM;gBACL,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC7C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;aACtC;AACD,YAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;SAC1B;AAAM,aAAA,IAAI,IAAI,YAAY,UAAU,EAAE;YACrC,IAAI,SAAS,EAAE;AACb,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAkB,CAAC;aACjC;iBAAM;gBACL,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzC,gBAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAkB,CAAC,CAAC;aACpC;AACD,YAAA,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;SACpB;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;KACvB;IAED,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,KAAK,CAAC,SAAiB,EAAE,OAAe,EAAA;AACtC,QAAA,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAa,CAAC;YACpC,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AACvD,YAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,gBAAA,OAAO,IAAI,CAAC;aACb;AACD,YAAA,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;SAC5B;aAAM;AACL,YAAA,MAAM,KAAK,GAAG,IAAI,UAAU,CACzB,IAAI,CAAC,KAAoB,CAAC,MAAM,EACjC,SAAS,EACT,OAAO,GAAG,SAAS,CACpB,CAAC;AACF,YAAA,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SACjC;KACF;AAED,IAAA,OAAO,OAAO,CAAC,GAAG,IAA6B,EAAA;QAC7C,IAAI,mBAAmB,EAAE,EAAE;YACzB,MAAM,MAAM,GAAsC,IAAI,CAAC,GAAG,CACxD,CAAC,GAAqB,KAAgC;AACpD,gBAAA,IAAI,GAAG,YAAY,OAAO,EAAE;oBAC1B,OAAO,GAAG,CAAC,KAAK,CAAC;iBAClB;qBAAM;AACL,oBAAA,OAAO,GAAG,CAAC;iBACZ;AACH,aAAC,CACF,CAAC;AACF,YAAA,OAAO,IAAI,OAAO,CAACA,SAAO,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;SACjD;aAAM;YACL,MAAM,WAAW,GAAiB,IAAI,CAAC,GAAG,CACxC,CAAC,GAAqB,KAAgB;AACpC,gBAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;oBACjB,OAAO,cAAc,CAAC,YAAY,CAAC,GAAG,EAAE,GAAa,CAAC,CAAC,IAAI,CAAC;iBAC7D;qBAAM;;oBAEL,OAAQ,GAAe,CAAC,KAAmB,CAAC;iBAC7C;AACH,aAAC,CACF,CAAC;YACF,IAAI,WAAW,GAAG,CAAC,CAAC;AACpB,YAAA,WAAW,CAAC,OAAO,CAAC,CAAC,KAAiB,KAAU;AAC9C,gBAAA,WAAW,IAAI,KAAK,CAAC,UAAU,CAAC;AAClC,aAAC,CAAC,CAAC;AACH,YAAA,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;YAC3C,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,YAAA,WAAW,CAAC,OAAO,CAAC,CAAC,KAAiB,KAAI;AACxC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;iBAC5B;AACH,aAAC,CAAC,CAAC;AACH,YAAA,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAClC;KACF;IAED,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AACF;;ACrID;;;;;;;;;;;;;;;AAeG;AAGH;;;AAGG;AACG,SAAU,gBAAgB,CAC9B,CAAS,EAAA;AAET,IAAA,IAAI,GAAG,CAAC;AACR,IAAA,IAAI;AACF,QAAA,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACrB;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;AACzB,QAAA,OAAO,GAAG,CAAC;KACZ;SAAM;AACL,QAAA,OAAO,IAAI,CAAC;KACb;AACH;;ACpCA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AAEH;;AAEG;AACG,SAAU,MAAM,CAAC,IAAY,EAAA;AACjC,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACrB,QAAA,OAAO,IAAI,CAAC;KACb;IACD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACpC,IAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,QAAA,OAAO,EAAE,CAAC;KACX;IACD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACrC,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAEe,SAAA,KAAK,CAAC,IAAY,EAAE,SAAiB,EAAA;IACnD,MAAM,kBAAkB,GAAG,SAAS;SACjC,KAAK,CAAC,GAAG,CAAC;SACV,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;SACzC,IAAI,CAAC,GAAG,CAAC,CAAC;AACb,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACrB,QAAA,OAAO,kBAAkB,CAAC;KAC3B;SAAM;AACL,QAAA,OAAO,IAAI,GAAG,GAAG,GAAG,kBAAkB,CAAC;KACxC;AACH,CAAC;AAED;;;;;AAKG;AACG,SAAU,aAAa,CAAC,IAAY,EAAA;AACxC,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrD,IAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;KACb;SAAM;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;KAC9B;AACH;;AC7DA;;;;;;;;;;;;;;;AAeG;AAea,SAAA,QAAQ,CAAI,QAAkB,EAAE,KAAQ,EAAA;AACtD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,OAAO,CAAA;AAKX,IAAA,WAAA,CACS,MAAc,EACrB,KAAqB,EACrB,QAAkB,EAClB,KAAwD,EAAA;QAHjD,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;AAKrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,MAAM,CAAC;AAC7B,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;AAC3B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,QAAQ,CAAC;KAChC;AACF,CAAA;AAKD,IAAI,SAAS,GAAoB,IAAI,CAAC;AAEhC,SAAU,SAAS,CAAC,QAA4B,EAAA;AACpD,IAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9C,QAAA,OAAO,QAAQ,CAAC;KACjB;SAAM;AACL,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC;KAChC;AACH,CAAC;SAEe,WAAW,GAAA;IACzB,IAAI,SAAS,EAAE;AACb,QAAA,OAAO,SAAS,CAAC;KAClB;IACD,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,QAAQ,CAAC,CAAC,CAAC;IAC7C,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,YAAY,CAAC,CAAC,CAAC;IACjD,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,gBAAgB,CAAC,CAAC,CAAC;AACrD,IAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;AAE7D,IAAA,SAAS,iBAAiB,CACxB,SAAmB,EACnB,QAA4B,EAAA;AAE5B,QAAA,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC;KAC5B;AACD,IAAA,MAAM,WAAW,GAAG,IAAI,OAAO,CAAS,MAAM,CAAC,CAAC;AAChD,IAAA,WAAW,CAAC,KAAK,GAAG,iBAAiB,CAAC;AACtC,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAE3B;;AAEG;AACH,IAAA,SAAS,SAAS,CAChB,SAAmB,EACnB,IAAsB,EAAA;AAEtB,QAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,YAAA,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM;AACL,YAAA,OAAO,IAAI,CAAC;SACb;KACF;AACD,IAAA,MAAM,WAAW,GAAG,IAAI,OAAO,CAAS,MAAM,CAAC,CAAC;AAChD,IAAA,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;AAC9B,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3B,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,aAAa,CAAC,CAAC,CAAC;IAClD,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,SAAS,CAAC,CAAC,CAAC;AAC9C,IAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1D,IAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/D,IAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,oBAAoB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACrE,IAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAClE,IAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAClE,IAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9D,IAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAS,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC;IACvE,SAAS,GAAG,QAAQ,CAAC;AACrB,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEe,SAAA,MAAM,CAAC,QAAkB,EAAE,OAA4B,EAAA;AACrE,IAAA,SAAS,WAAW,GAAA;AAClB,QAAA,MAAM,MAAM,GAAW,QAAQ,CAAC,QAAQ,CAAW,CAAC;AACpD,QAAA,MAAM,IAAI,GAAW,QAAQ,CAAC,UAAU,CAAW,CAAC;QACpD,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACvC,QAAA,OAAO,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;KAC3C;AACD,IAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;AAC/D,CAAC;SAEe,YAAY,CAC1B,OAA4B,EAC5B,QAAqC,EACrC,QAAkB,EAAA;IAElB,MAAM,QAAQ,GAAa,EAAc,CAAC;AAC1C,IAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAC1B,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC5B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC5B,QAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAA,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAI,OAA4B,CAAC,KAAK,CAC3D,QAAQ,EACR,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CACzB,CAAC;KACH;AACD,IAAA,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC1B,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;SAEe,kBAAkB,CAChC,OAA4B,EAC5B,cAAsB,EACtB,QAAkB,EAAA;AAElB,IAAA,MAAM,GAAG,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAC7C,IAAA,IAAI,GAAG,KAAK,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;KACb;IACD,MAAM,QAAQ,GAAG,GAAe,CAAC;IACjC,OAAO,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACnD,CAAC;AAEK,SAAU,6BAA6B,CAC3C,QAAkB,EAClB,cAAsB,EACtB,IAAY,EACZ,QAAgB,EAAA;AAEhB,IAAA,MAAM,GAAG,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAC7C,IAAA,IAAI,GAAG,KAAK,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;KACb;IACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE;;;AAGpC,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,MAAM,MAAM,GAAW,GAAG,CAAC,gBAAgB,CAAW,CAAC;AACvD,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AACvB,QAAA,OAAO,IAAI,CAAC;KACb;IACD,MAAM,MAAM,GAAG,kBAAkB,CAAC;IAClC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrC,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,KAAa,KAAY;AACpD,QAAA,MAAM,MAAM,GAAW,QAAQ,CAAC,QAAQ,CAAW,CAAC;AACpD,QAAA,MAAM,IAAI,GAAW,QAAQ,CAAC,UAAU,CAAW,CAAC;AACpD,QAAA,MAAM,OAAO,GAAG,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9D,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC9C,MAAM,WAAW,GAAG,eAAe,CAAC;AAClC,YAAA,GAAG,EAAE,OAAO;YACZ,KAAK;AACN,SAAA,CAAC,CAAC;QACH,OAAO,IAAI,GAAG,WAAW,CAAC;AAC5B,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC;AAEe,SAAA,gBAAgB,CAC9B,QAA2B,EAC3B,QAAkB,EAAA;IAElB,MAAM,QAAQ,GAEV,EAAE,CAAC;AACP,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC5B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC5B,QAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAA,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpB,YAAA,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACpD;KACF;AACD,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAClC;;AC7MA;;;;;;;;;;;;;;;AAeG;AA6BH,MAAM,YAAY,GAAG,UAAU,CAAC;AAChC,MAAM,SAAS,GAAG,OAAO,CAAC;AAE1B,SAAS,mBAAmB,CAC1B,OAA4B,EAC5B,MAAc,EACd,QAA4B,EAAA;AAE5B,IAAA,MAAM,UAAU,GAAe;AAC7B,QAAA,QAAQ,EAAE,EAAE;AACZ,QAAA,KAAK,EAAE,EAAE;AACT,QAAA,aAAa,EAAE,QAAQ,CAAC,eAAe,CAAC;KACzC,CAAC;AACF,IAAA,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC1B,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE;YACzC,MAAM,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACzD,YAAA,MAAM,SAAS,GAAG,OAAO,CAAC,qBAAqB,CAC7C,IAAI,QAAQ,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAC/C,CAAC;AACF,YAAA,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACrC;KACF;AAED,IAAA,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;QACvB,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;AACtC,YAAA,MAAM,SAAS,GAAG,OAAO,CAAC,qBAAqB,CAC7C,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CACnC,CAAC;AACF,YAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAClC;KACF;AACD,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;SAEe,kBAAkB,CAChC,OAA4B,EAC5B,MAAc,EACd,cAAsB,EAAA;AAEtB,IAAA,MAAM,GAAG,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAC7C,IAAA,IAAI,GAAG,KAAK,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;KACb;IACD,MAAM,QAAQ,GAAG,GAAoC,CAAC;IACtD,OAAO,mBAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AACxD;;AC7CA;;;;;AAKG;MACU,WAAW,CAAA;IActB,WACS,CAAA,GAAW,EACX,MAAc;AACrB;;;;;;AAMG;AACI,IAAA,OAA6B,EAC7B,OAAe,EAAA;QAVf,IAAG,CAAA,GAAA,GAAH,GAAG,CAAQ;QACX,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QAQd,IAAO,CAAA,OAAA,GAAP,OAAO,CAAsB;QAC7B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QAxBxB,IAAS,CAAA,SAAA,GAAc,EAAE,CAAC;QAC1B,IAAO,CAAA,OAAA,GAAY,EAAE,CAAC;QACtB,IAAI,CAAA,IAAA,GAAsC,IAAI,CAAC;QAC/C,IAAY,CAAA,YAAA,GAAwB,IAAI,CAAC;AAEzC;;;AAGG;QACH,IAAgB,CAAA,gBAAA,GAA8C,IAAI,CAAC;AACnE,QAAA,IAAA,CAAA,YAAY,GAAa,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAoB,CAAA,oBAAA,GAAa,EAAE,CAAC;KAchC;AACL;;AC7ED;;;;;;;;;;;;;;;AAeG;AAkCH;;AAEG;AACG,SAAU,YAAY,CAAC,IAAa,EAAA;IACxC,IAAI,CAAC,IAAI,EAAE;QACT,MAAM,OAAO,EAAE,CAAC;KACjB;AACH,CAAC;AAEe,SAAA,eAAe,CAC7B,OAA4B,EAC5B,QAAkB,EAAA;AAElB,IAAA,SAAS,OAAO,CAAC,GAAuB,EAAE,IAAY,EAAA;QACpD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC7D,QAAA,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;AAChC,QAAA,OAAO,QAAoB,CAAC;KAC7B;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAEe,SAAA,WAAW,CACzB,OAA4B,EAC5B,MAAc,EAAA;AAEd,IAAA,SAAS,OAAO,CAAC,GAAuB,EAAE,IAAY,EAAA;QACpD,MAAM,UAAU,GAAG,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC7D,QAAA,YAAY,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC;AAClC,QAAA,OAAO,UAAwB,CAAC;KACjC;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAEe,SAAA,kBAAkB,CAChC,OAA4B,EAC5B,QAAkB,EAAA;AAElB,IAAA,SAAS,OAAO,CAAC,GAAuB,EAAE,IAAY,EAAA;QACpD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC7D,QAAA,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;AAChC,QAAA,OAAO,6BAA6B,CAClC,QAAoB,EACpB,IAAI,EACJ,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,SAAS,CAClB,CAAC;KACH;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAEK,SAAU,kBAAkB,CAChC,QAAkB,EAAA;AAElB,IAAA,SAAS,YAAY,CACnB,GAA+B,EAC/B,GAAiB,EAAA;AAEjB,QAAA,IAAI,MAAoB,CAAC;AACzB,QAAA,IAAI,GAAG,CAAC,SAAS,EAAE,KAAK,GAAG,EAAE;AAC3B,YAAA;;;YAGE,GAAG,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC,EAClE;gBACA,MAAM,GAAG,eAAe,EAAE,CAAC;aAC5B;iBAAM;gBACL,MAAM,GAAG,eAAe,EAAE,CAAC;aAC5B;SACF;aAAM;AACL,YAAA,IAAI,GAAG,CAAC,SAAS,EAAE,KAAK,GAAG,EAAE;AAC3B,gBAAA,MAAM,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aACzC;iBAAM;AACL,gBAAA,IAAI,GAAG,CAAC,SAAS,EAAE,KAAK,GAAG,EAAE;AAC3B,oBAAA,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;iBACtC;qBAAM;oBACL,MAAM,GAAG,GAAG,CAAC;iBACd;aACF;SACF;AACD,QAAA,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;AAChC,QAAA,MAAM,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,CAAC;AAC3C,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAEK,SAAU,kBAAkB,CAChC,QAAkB,EAAA;AAElB,IAAA,MAAM,MAAM,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAE5C,IAAA,SAAS,YAAY,CACnB,GAA+B,EAC/B,GAAiB,EAAA;QAEjB,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9B,QAAA,IAAI,GAAG,CAAC,SAAS,EAAE,KAAK,GAAG,EAAE;AAC3B,YAAA,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACxC;AACD,QAAA,MAAM,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,CAAC;AAC3C,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;SAEeC,aAAW,CACzB,OAA4B,EAC5B,QAAkB,EAClB,QAAkB,EAAA;AAElB,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;AACzC,IAAA,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;AAC9C,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,CACjC,GAAG,EACH,MAAM,EACN,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,EAClC,OAAO,CACR,CAAC;AACF,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAEK,SAAUC,MAAI,CAClB,OAA4B,EAC5B,QAAkB,EAClB,SAAkB,EAClB,SAAyB,EACzB,UAA0B,EAAA;IAE1B,MAAM,SAAS,GAAc,EAAE,CAAC;AAChC,IAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,QAAA,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;KAC1B;SAAM;QACL,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC;KAC3C;IACD,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,QAAA,SAAS,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;KACpC;IACD,IAAI,SAAS,EAAE;AACb,QAAA,SAAS,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;KACpC;IACD,IAAI,UAAU,EAAE;AACd,QAAA,SAAS,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;KACtC;AACD,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;AAC/C,IAAA,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;IAC9C,MAAM,WAAW,GAAG,IAAI,WAAW,CACjC,GAAG,EACH,MAAM,EACN,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,EACrC,OAAO,CACR,CAAC;AACF,IAAA,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;AAClC,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;SAEeC,UAAQ,CACtB,OAA4B,EAC5B,QAAkB,EAClB,oBAA6B,EAAA;AAE7B,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;AACzC,IAAA,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC;IAC7E,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;IAC9C,MAAM,WAAW,GAAG,IAAI,WAAW,CACjC,GAAG,EACH,MAAM,EACN,CAAC,CAAgB,EAAE,IAAO,KAAK,IAAI,EACnC,OAAO,CACR,CAAC;AACF,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,IAAI,oBAAoB,KAAK,SAAS,EAAE;QACtC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAA,QAAA,EAAW,oBAAoB,CAAA,CAAE,CAAC;AACjE,QAAA,WAAW,CAAC,YAAY,GAAG,CAAC,GAAG,WAAW,GAAG,uBAAuB,CAAC;KACtE;AACD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;SAEe,cAAc,CAC5B,OAA4B,EAC5B,QAAkB,EAClB,QAAkB,EAAA;AAElB,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;AACzC,IAAA,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;AAC9C,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,CACjC,GAAG,EACH,MAAM,EACN,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC,EACrC,OAAO,CACR,CAAC;AACF,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAEK,SAAUC,gBAAc,CAC5B,OAA4B,EAC5B,QAAkB,EAClB,QAA2B,EAC3B,QAAkB,EAAA;AAElB,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;AACzC,IAAA,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,OAAO,CAAC;IACvB,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAClD,IAAA,MAAM,OAAO,GAAG,EAAE,cAAc,EAAE,iCAAiC,EAAE,CAAC;AACtE,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;AAC9C,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,CACjC,GAAG,EACH,MAAM,EACN,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,EAClC,OAAO,CACR,CAAC;AACF,IAAA,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;AAC9B,IAAA,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAEe,SAAAC,cAAY,CAC1B,OAA4B,EAC5B,QAAkB,EAAA;AAElB,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;AACzC,IAAA,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,CAAC;AAE9C,IAAA,SAAS,OAAO,CAAC,IAAwB,EAAE,KAAa,KAAU;AAClE,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACnE,WAAW,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACtC,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAEe,SAAA,qBAAqB,CACnC,QAAyB,EACzB,IAAoB,EAAA;IAEpB,QACE,CAAC,QAAQ,IAAI,QAAQ,CAAC,aAAa,CAAC;AACpC,SAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,QAAA,0BAA0B,EAC1B;AACJ,CAAC;SAEe,kBAAkB,CAChC,QAAkB,EAClB,IAAa,EACb,QAA0B,EAAA;IAE1B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AAClD,IAAA,aAAa,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;IAC1C,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACpC,IAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;QACjC,aAAa,CAAC,aAAa,CAAC,GAAG,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAClE;AACD,IAAA,OAAO,aAAa,CAAC;AACvB,CAAC;AAED;;AAEG;AACG,SAAU,eAAe,CAC7B,OAA4B,EAC5B,QAAkB,EAClB,QAAkB,EAClB,IAAa,EACb,QAA0B,EAAA;AAE1B,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;AAC/C,IAAA,MAAM,OAAO,GAA+B;AAC1C,QAAA,wBAAwB,EAAE,WAAW;KACtC,CAAC;AAEF,IAAA,SAAS,WAAW,GAAA;QAClB,IAAI,GAAG,GAAG,EAAE,CAAC;AACb,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,YAAA,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAC/C;AACD,QAAA,OAAO,GAAG,CAAC;KACZ;AACD,IAAA,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AAC/B,IAAA,OAAO,CAAC,cAAc,CAAC,GAAG,8BAA8B,GAAG,QAAQ,CAAC;IACpE,MAAM,SAAS,GAAG,kBAAkB,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/D,MAAM,cAAc,GAAG,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC7D,MAAM,WAAW,GACf,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,uDAAuD;QACvD,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,gBAAgB;QAChB,SAAS,CAAC,aAAa,CAAC;AACxB,QAAA,UAAU,CAAC;AACb,IAAA,MAAM,YAAY,GAAG,QAAQ,GAAG,QAAQ,GAAG,IAAI,CAAC;AAChD,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AAC9D,IAAA,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,MAAM,eAAe,EAAE,CAAC;KACzB;IACD,MAAM,SAAS,GAAc,EAAE,IAAI,EAAE,SAAS,CAAC,UAAU,CAAE,EAAE,CAAC;AAC9D,IAAA,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,MAAM,CAAC;AACtB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAC3C,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,CACjC,GAAG,EACH,MAAM,EACN,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,EAClC,OAAO,CACR,CAAC;AACF,IAAA,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;AAClC,IAAA,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;AAC9B,IAAA,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACrC,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;;;AAMG;MACU,qBAAqB,CAAA;AAIhC,IAAA,WAAA,CACS,OAAe,EACf,KAAa,EACpB,SAAmB,EACnB,QAA0B,EAAA;QAHnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;AAIpB,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;AAC7B,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC;KAClC;AACF,CAAA;AAEe,SAAA,kBAAkB,CAChC,GAAuB,EACvB,OAAkB,EAAA;IAElB,IAAI,MAAM,GAAkB,IAAI,CAAC;AACjC,IAAA,IAAI;AACF,QAAA,MAAM,GAAG,GAAG,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;KACxD;IAAC,OAAO,CAAC,EAAE;QACV,YAAY,CAAC,KAAK,CAAC,CAAC;KACrB;AACD,IAAA,MAAM,aAAa,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5C,IAAA,YAAY,CAAC,CAAC,CAAC,MAAM,IAAI,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/D,IAAA,OAAO,MAAgB,CAAC;AAC1B,CAAC;AAEK,SAAU,qBAAqB,CACnC,OAA4B,EAC5B,QAAkB,EAClB,QAAkB,EAClB,IAAa,EACb,QAA0B,EAAA;AAE1B,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;IAC/C,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACvE,MAAM,SAAS,GAAc,EAAE,IAAI,EAAE,iBAAiB,CAAC,UAAU,CAAE,EAAE,CAAC;AACtE,IAAA,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,MAAM,CAAC;AACtB,IAAA,MAAM,OAAO,GAAG;AACd,QAAA,wBAAwB,EAAE,WAAW;AACrC,QAAA,uBAAuB,EAAE,OAAO;AAChC,QAAA,qCAAqC,EAAE,CAAG,EAAA,IAAI,CAAC,IAAI,EAAE,CAAE,CAAA;AACvD,QAAA,mCAAmC,EAAE,iBAAiB,CAAC,aAAa,CAAE;AACtE,QAAA,cAAc,EAAE,iCAAiC;KAClD,CAAC;IACF,MAAM,IAAI,GAAG,gBAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;AAC3D,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAE3C,SAAS,OAAO,CAAC,GAAuB,EAAA;QACtC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AACxB,QAAA,IAAI,GAAG,CAAC;AACR,QAAA,IAAI;AACF,YAAA,GAAG,GAAG,GAAG,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;SAClD;QAAC,OAAO,CAAC,EAAE;YACV,YAAY,CAAC,KAAK,CAAC,CAAC;SACrB;AACD,QAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B,QAAA,OAAO,GAAa,CAAC;KACtB;AACD,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACnE,IAAA,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;AAClC,IAAA,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;AAC9B,IAAA,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;AAEG;AACG,SAAU,wBAAwB,CACtC,OAA4B,EAC5B,QAAkB,EAClB,GAAW,EACX,IAAa,EAAA;AAEb,IAAA,MAAM,OAAO,GAAG,EAAE,uBAAuB,EAAE,OAAO,EAAE,CAAC;IAErD,SAAS,OAAO,CAAC,GAAuB,EAAA;AACtC,QAAA,MAAM,MAAM,GAAG,kBAAkB,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;QAC5D,IAAI,UAAU,GAAkB,IAAI,CAAC;AACrC,QAAA,IAAI;AACF,YAAA,UAAU,GAAG,GAAG,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,CAAC;SACnE;QAAC,OAAO,CAAC,EAAE;YACV,YAAY,CAAC,KAAK,CAAC,CAAC;SACrB;QAED,IAAI,CAAC,UAAU,EAAE;;YAEf,YAAY,CAAC,KAAK,CAAC,CAAC;SACrB;AAED,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AAChC,QAAA,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3B,QAAA,OAAO,IAAI,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;KACzE;IACD,MAAM,MAAM,GAAG,MAAM,CAAC;AACtB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAC3C,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACnE,IAAA,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;AAC9B,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;AAGG;AACI,MAAM,2BAA2B,GAAW,GAAG,GAAG,IAAI,CAAC;AAE9D;;;;;;;;AAQG;SACa,uBAAuB,CACrC,QAAkB,EAClB,OAA4B,EAC5B,GAAW,EACX,IAAa,EACb,SAAiB,EACjB,QAAkB,EAClB,MAAqC,EACrC,gBAA4D,EAAA;;;IAI5D,MAAM,OAAO,GAAG,IAAI,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChD,IAAI,MAAM,EAAE;AACV,QAAA,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AACjC,QAAA,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;KAC9B;SAAM;AACL,QAAA,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;AACpB,QAAA,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC7B;IACD,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,KAAK,EAAE;QACjC,MAAM,mBAAmB,EAAE,CAAC;KAC7B;IACD,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC;IAClD,IAAI,aAAa,GAAG,SAAS,CAAC;AAC9B,IAAA,IAAI,SAAS,GAAG,CAAC,EAAE;QACjB,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;KACpD;AACD,IAAA,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC;AAClC,IAAA,MAAM,OAAO,GAAG,SAAS,GAAG,aAAa,CAAC;IAC1C,IAAI,aAAa,GAAG,EAAE,CAAC;AACvB,IAAA,IAAI,aAAa,KAAK,CAAC,EAAE;QACvB,aAAa,GAAG,UAAU,CAAC;KAC5B;AAAM,SAAA,IAAI,SAAS,KAAK,aAAa,EAAE;QACtC,aAAa,GAAG,kBAAkB,CAAC;KACpC;SAAM;QACL,aAAa,GAAG,QAAQ,CAAC;KAC1B;AACD,IAAA,MAAM,OAAO,GAAG;AACd,QAAA,uBAAuB,EAAE,aAAa;AACtC,QAAA,sBAAsB,EAAE,CAAA,EAAG,OAAO,CAAC,OAAO,CAAE,CAAA;KAC7C,CAAC;IACF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC5C,IAAA,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,MAAM,eAAe,EAAE,CAAC;KACzB;AAED,IAAA,SAAS,OAAO,CACd,GAAuB,EACvB,IAAY,EAAA;;;;;AAMZ,QAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAClE,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,GAAG,aAAa,CAAC;AACnD,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACzB,QAAA,IAAI,QAAQ,CAAC;AACb,QAAA,IAAI,YAAY,KAAK,OAAO,EAAE;AAC5B,YAAA,QAAQ,GAAG,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAC1D;aAAM;YACL,QAAQ,GAAG,IAAI,CAAC;SACjB;AACD,QAAA,OAAO,IAAI,qBAAqB,CAC9B,UAAU,EACV,IAAI,EACJ,YAAY,KAAK,OAAO,EACxB,QAAQ,CACT,CAAC;KACH;IACD,MAAM,MAAM,GAAG,MAAM,CAAC;AACtB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAC3C,IAAA,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACnE,IAAA,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;AAC9B,IAAA,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACrC,IAAA,WAAW,CAAC,gBAAgB,GAAG,gBAAgB,IAAI,IAAI,CAAC;AACxD,IAAA,WAAW,CAAC,YAAY,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAA,OAAO,WAAW,CAAC;AACrB;;AC1kBA;;;;;;;;;;;;;;;AAeG;AAYH;;;AAGG;AACU,MAAA,SAAS,GAAG;AACvB;;;;;;;;;;;AAWG;AACH,IAAA,aAAa,EAAE,eAAe;EAC9B;AAqBF;AACA;;;AAGG;AACU,MAAA,SAAS,GAAG;;AAEvB,IAAA,OAAO,EAAE,SAAS;;AAGlB,IAAA,MAAM,EAAE,QAAQ;;AAGhB,IAAA,OAAO,EAAE,SAAS;;AAGlB,IAAA,QAAQ,EAAE,UAAU;;AAGpB,IAAA,KAAK,EAAE,OAAO;EACL;AAEL,SAAU,8BAA8B,CAC5C,KAAwB,EAAA;IAExB,QAAQ,KAAK;QACX,KAA+B,SAAA,iCAAA;QAC/B,KAA+B,SAAA,iCAAA;AAC/B,QAAA,KAAA,WAAA;YACE,OAAO,SAAS,CAAC,OAAO,CAAC;AAC3B,QAAA,KAAA,QAAA;YACE,OAAO,SAAS,CAAC,MAAM,CAAC;AAC1B,QAAA,KAAA,SAAA;YACE,OAAO,SAAS,CAAC,OAAO,CAAC;AAC3B,QAAA,KAAA,UAAA;YACE,OAAO,SAAS,CAAC,QAAQ,CAAC;AAC5B,QAAA,KAAA,OAAA;YACE,OAAO,SAAS,CAAC,KAAK,CAAC;AACzB,QAAA;;YAEE,OAAO,SAAS,CAAC,KAAK,CAAC;KAC1B;AACH;;AC5GA;;;;;;;;;;;;;;;AAeG;MAsDU,QAAQ,CAAA;AAKnB,IAAA,WAAA,CACE,cAA+C,EAC/C,KAAe,EACf,QAAqB,EAAA;AAErB,QAAA,MAAM,WAAW,GACf,UAAU,CAAC,cAAc,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC;QAClE,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,CAAC,IAAI,GAAG,cAA2B,CAAC;AACxC,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,SAAS,CAAC;AAChC,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,SAAS,CAAC;SACvC;aAAM;YACL,MAAM,QAAQ,GAAG,cAIhB,CAAC;AACF,YAAA,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC1B,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC5B,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;SACnC;KACF;AACF;;AChGD;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;AACH;AACM,SAAU,KAAK,CAAC,CAAW,EAAA;AAC/B,IAAA,OAAO,CAAC,GAAG,aAAwB,KAAI;;AAErC,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;AACpD,KAAC,CAAC;AACJ;;AC5BA;;;;;;;;;;;;;;;AAeG;AAWH;AACA,IAAI,mBAAmB,GAAsC,IAAI,CAAC;AAElE;;;AAGG;AACH,MAAe,aAAa,CAAA;AAQ1B,IAAA,WAAA,GAAA;QAFU,IAAK,CAAA,KAAA,GAAY,KAAK,CAAC;AAG/B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,cAAc,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,OAAO,IAAG;YACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;AACvC,gBAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC;AAClC,gBAAA,OAAO,EAAE,CAAC;AACZ,aAAC,CAAC,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;AACvC,gBAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,aAAa,CAAC;AAC1C,gBAAA,OAAO,EAAE,CAAC;AACZ,aAAC,CAAC,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAK;AACtC,gBAAA,OAAO,EAAE,CAAC;AACZ,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;IAID,IAAI,CACF,GAAW,EACX,MAAc,EACd,eAAwB,EACxB,IAAsC,EACtC,OAAiB,EAAA;AAEjB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,aAAa,CAAC,+BAA+B,CAAC,CAAC;SACtD;AACD,QAAA,IAAI,kBAAkB,CAAC,GAAG,CAAC,IAAI,eAAe,EAAE;AAC9C,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAClC;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAClC,QAAA,IAAI,OAAO,KAAK,SAAS,EAAE;AACzB,YAAA,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;AACzB,gBAAA,IAAI,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC/B,oBAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;iBAC1D;aACF;SACF;AACD,QAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACtB;aAAM;AACL,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;SAClB;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;IAED,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,MAAM,aAAa,CAAC,uCAAuC,CAAC,CAAC;SAC9D;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,MAAM,aAAa,CAAC,oCAAoC,CAAC,CAAC;SAC3D;AACD,QAAA,IAAI;AACF,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;SACzB;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,CAAC,CAAC,CAAC;SACX;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,MAAM,aAAa,CAAC,sCAAsC,CAAC,CAAC;SAC7D;AACD,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC3B;IAED,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,MAAM,aAAa,CAAC,uCAAuC,CAAC,CAAC;SAC9D;AACD,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;KAC7B;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;KACnB;AAED,IAAA,iBAAiB,CAAC,MAAc,EAAA;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;KAC5C;AAED,IAAA,yBAAyB,CAAC,QAAqC,EAAA;QAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;SACzD;KACF;AAED,IAAA,4BAA4B,CAAC,QAAqC,EAAA;QAChE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;SAC5D;KACF;AACF,CAAA;AAEK,MAAO,iBAAkB,SAAQ,aAAqB,CAAA;IAC1D,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;KACjC;AACF,CAAA;SAEe,iBAAiB,GAAA;AAC/B,IAAA,OAAO,mBAAmB,GAAG,mBAAmB,EAAE,GAAG,IAAI,iBAAiB,EAAE,CAAC;AAC/E,CAAC;AAEK,MAAO,kBAAmB,SAAQ,aAA0B,CAAA;IAGhE,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;KACxC;AACF,CAAA;SAEe,kBAAkB,GAAA;IAChC,OAAO,IAAI,kBAAkB,EAAE,CAAC;AAClC,CAAC;AAEK,MAAO,iBAAkB,SAAQ,aAAmB,CAAA;IACxD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;KACjC;AACF,CAAA;SAEe,iBAAiB,GAAA;IAC/B,OAAO,IAAI,iBAAiB,EAAE,CAAC;AACjC;;AChLA;;;;;;;;;;;;;;;AAeG;AA6CH;;;;AAIG;MACU,UAAU,CAAA;IAsCrB,2BAA2B,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;KAC3C;AAED;;;;AAIG;AACH,IAAA,WAAA,CAAY,GAAc,EAAE,IAAa,EAAE,WAA4B,IAAI,EAAA;AApC3E;;AAEG;QACH,IAAY,CAAA,YAAA,GAAW,CAAC,CAAC;QACjB,IAAkB,CAAA,kBAAA,GAAY,KAAK,CAAC;QACpC,IAAoB,CAAA,oBAAA,GAAY,KAAK,CAAC;QACtC,IAAU,CAAA,UAAA,GAAuD,EAAE,CAAC;QAMpE,IAAM,CAAA,MAAA,GAAkB,SAAS,CAAC;QAClC,IAAU,CAAA,UAAA,GAAY,SAAS,CAAC;QAChC,IAAQ,CAAA,QAAA,GAAsB,SAAS,CAAC;QACxC,IAAgB,CAAA,gBAAA,GAAW,CAAC,CAAC;QAG7B,IAAQ,CAAA,QAAA,GAAsC,SAAS,CAAC;QACxD,IAAO,CAAA,OAAA,GAAgC,SAAS,CAAC;AAkBvD,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AAChB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,GAAA,SAAA,iCAA6B;AACxC,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,IAAG;AAC3B,YAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,YAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC1B,IAAI,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;AAChD,gBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC7B;iBAAM;AACL,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBAC1D,IAAI,iBAAiB,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBACvC,IAAI,cAAc,EAAE;wBAClB,KAAK,GAAG,kBAAkB,EAAE,CAAC;qBAC9B;yBAAM;AACL,wBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CACvB,IAAI,CAAC,SAAS,GAAG,CAAC,EAClB,6BAA6B,CAC9B,CAAC;AACF,wBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBAC/B,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC5B,OAAO;qBACR;iBACF;AACD,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,WAAW,CAAA,OAAA,+BAAyB,CAAC;aAC3C;AACH,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,IAAG;AACnC,YAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;YAC1B,IAAI,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;gBAChD,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC7B;iBAAM;AACL,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,WAAW,CAAA,OAAA,+BAAyB,CAAC;aAC3C;AACH,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;QACzD,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AAC9C,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AACxB,YAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB,SAAC,CAAC,CAAC;;;AAIH,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAK,GAAG,CAAC,CAAC;KACpC;IAEO,qBAAqB,GAAA;AAC3B,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC;AACrC,QAAA,OAAO,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC;KAC5D;AAEO,IAAA,kBAAkB,CAAC,IAAa,EAAA;QACtC,OAAO,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;KACjC;IAEO,MAAM,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,MAAM,KAAA,SAAA,kCAAgC;;YAE7C,OAAO;SACR;AACD,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,OAAO;SACR;AACD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;gBACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACzB;iBAAM;AACL,gBAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBAC3B,IAAI,CAAC,YAAY,EAAE,CAAC;iBACrB;qBAAM;AACL,oBAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;;wBAE7B,IAAI,CAAC,cAAc,EAAE,CAAC;qBACvB;yBAAM;AACL,wBAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,MAAK;AACpC,4BAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;4BAChC,IAAI,CAAC,eAAe,EAAE,CAAC;AACzB,yBAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;qBACpB;iBACF;aACF;SACF;aAAM;YACL,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;KACF;AAEO,IAAA,aAAa,CACnB,QAA0E,EAAA;;QAG1E,OAAO,CAAC,GAAG,CAAC;AACV,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AACjC,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;SACtC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,aAAa,CAAC,KAAI;AACrC,YAAA,QAAQ,IAAI,CAAC,MAAM;AACjB,gBAAA,KAAA,SAAA;AACE,oBAAA,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;oBACnC,MAAM;AACR,gBAAA,KAAA,WAAA;oBACE,IAAI,CAAC,WAAW,CAAA,UAAA,kCAA4B,CAAC;oBAC7C,MAAM;AACR,gBAAA,KAAA,SAAA;oBACE,IAAI,CAAC,WAAW,CAAA,QAAA,gCAA0B,CAAC;oBAC3C,MAAM;aAET;AACH,SAAC,CAAC,CAAC;KACJ;;IAIO,gBAAgB,GAAA;QACtB,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,EAAE,aAAa,KAAI;AAC9C,YAAA,MAAM,WAAW,GAAG,qBAAqB,CACvC,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,IAAI,CAAC,SAAS,EACnB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,SAAS,CACf,CAAC;AACF,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAClD,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,aAAa,CACd,CAAC;AACF,YAAA,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;YAC9B,aAAa,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,GAAW,KAAI;AAC9C,gBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,gBAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;AACtB,gBAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;gBAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC9B,aAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACzB,SAAC,CAAC,CAAC;KACJ;IAEO,YAAY,GAAA;;AAElB,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAoB,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,EAAE,aAAa,KAAI;YAC9C,MAAM,WAAW,GAAG,wBAAwB,CAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,IAAI,CAAC,SAAS,EACnB,GAAG,EACH,IAAI,CAAC,KAAK,CACX,CAAC;AACF,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAClD,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,aAAa,CACd,CAAC;AACF,YAAA,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;YAC9B,aAAa,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,IAAG;gBACvC,MAAM,GAAG,MAA+B,CAAC;AACzC,gBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,gBAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AAChC,gBAAA,IAAI,MAAM,CAAC,SAAS,EAAE;AACpB,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;iBAClC;gBACD,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC9B,aAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACzB,SAAC,CAAC,CAAC;KACJ;IAEO,eAAe,GAAA;AACrB,QAAA,MAAM,SAAS,GAAG,2BAA2B,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACtE,QAAA,MAAM,MAAM,GAAG,IAAI,qBAAqB,CACtC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAClB,CAAC;;AAGF,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAoB,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,EAAE,aAAa,KAAI;AAC9C,YAAA,IAAI,WAAW,CAAC;AAChB,YAAA,IAAI;AACF,gBAAA,WAAW,GAAG,uBAAuB,CACnC,IAAI,CAAC,IAAI,CAAC,SAAS,EACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,GAAG,EACH,IAAI,CAAC,KAAK,EACV,SAAS,EACT,IAAI,CAAC,SAAS,EACd,MAAM,EACN,IAAI,CAAC,qBAAqB,EAAE,CAC7B,CAAC;aACH;YAAC,OAAO,CAAC,EAAE;AACV,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAiB,CAAC;gBAChC,IAAI,CAAC,WAAW,CAAA,OAAA,+BAAyB,CAAC;gBAC1C,OAAO;aACR;AACD,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAClD,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,aAAa;uBACF,KAAK;aACjB,CAAC;AACF,YAAA,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;YAC9B,aAAa,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,SAAgC,KAAI;gBACnE,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,gBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,gBAAA,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACxC,gBAAA,IAAI,SAAS,CAAC,SAAS,EAAE;AACvB,oBAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC;oBACpC,IAAI,CAAC,WAAW,CAAA,SAAA,iCAA2B,CAAC;iBAC7C;qBAAM;oBACL,IAAI,CAAC,oBAAoB,EAAE,CAAC;iBAC7B;AACH,aAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACzB,SAAC,CAAC,CAAC;KACJ;IAEO,mBAAmB,GAAA;AACzB,QAAA,MAAM,WAAW,GAAG,2BAA2B,GAAG,IAAI,CAAC,gBAAgB,CAAC;;QAGxE,IAAI,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;AACtC,YAAA,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;SAC5B;KACF;IAEO,cAAc,GAAA;QACpB,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,EAAE,aAAa,KAAI;YAC9C,MAAM,WAAW,GAAGJ,aAAW,CAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,IAAI,CAAC,SAAS,EACnB,IAAI,CAAC,SAAS,CACf,CAAC;AACF,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CACpD,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,aAAa,CACd,CAAC;AACF,YAAA,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;YAChC,eAAe,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAG;AAC3C,gBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,WAAW,CAAA,SAAA,iCAA2B,CAAC;AAC9C,aAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACjC,SAAC,CAAC,CAAC;KACJ;IAEO,cAAc,GAAA;QACpB,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,EAAE,aAAa,KAAI;AAC9C,YAAA,MAAM,WAAW,GAAG,eAAe,CACjC,IAAI,CAAC,IAAI,CAAC,OAAO,EACjB,IAAI,CAAC,IAAI,CAAC,SAAS,EACnB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,SAAS,CACf,CAAC;AACF,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CACrD,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,aAAa,CACd,CAAC;AACF,YAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YACjC,gBAAgB,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAG;AAC5C,gBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBACxC,IAAI,CAAC,WAAW,CAAA,SAAA,iCAA2B,CAAC;AAC9C,aAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACzB,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,eAAe,CAAC,WAAmB,EAAA;AACzC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC;AAC9B,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;;;;AAKhC,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,GAAG,EAAE;YAC7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACzB;KACF;AAEO,IAAA,WAAW,CAAC,KAAwB,EAAA;AAC1C,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YACzB,OAAO;SACR;QACD,QAAQ,KAAK;YACX,KAAiC,WAAA,mCAAA;AACjC,YAAA,KAAA,SAAA;;;;AAIE,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,gBAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC/B,oBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;iBACxB;AAAM,qBAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AAC9B,oBAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAClC,oBAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;oBAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;iBAC7B;gBACD,MAAM;AACR,YAAA,KAAA,SAAA;;;;AAIE,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,6CAA8B;AAC3D,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,SAAS,EAAE;oBACb,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,MAAM,EAAE,CAAC;iBACf;gBACD,MAAM;AACR,YAAA,KAAA,QAAA;;;AAGE,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;AACR,YAAA,KAAA,UAAA;;;;AAIE,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;AACzB,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;AACR,YAAA,KAAA,OAAA;;;;;AAKE,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;AACR,YAAA,KAAA,SAAA;;;;;AAKE,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;SAET;KACF;IAEO,oBAAoB,GAAA;AAC1B,QAAA,QAAQ,IAAI,CAAC,MAAM;AACjB,YAAA,KAAA,SAAA;gBACE,IAAI,CAAC,WAAW,CAAA,QAAA,gCAA0B,CAAC;gBAC3C,MAAM;AACR,YAAA,KAAA,WAAA;gBACE,IAAI,CAAC,WAAW,CAAA,UAAA,kCAA4B,CAAC;gBAC7C,MAAM;AACR,YAAA,KAAA,SAAA;gBACE,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,MAAM;SAIT;KACF;AAED;;AAEG;AACH,IAAA,IAAI,QAAQ,GAAA;QACV,MAAM,aAAa,GAAG,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClE,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,YAAY;AACnC,YAAA,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;AAC7B,YAAA,KAAK,EAAE,aAAa;YACpB,QAAQ,EAAE,IAAI,CAAC,SAAU;AACzB,YAAA,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,IAAI,CAAC,IAAI;SACf,CAAC;KACH;AAED;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,EAAE,CACA,IAAe,EACf,cAG+C,EAC/C,KAA6C,EAC7C,SAA6B,EAAA;;AAG7B,QAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAC1B,cAE8B,IAAI,SAAS,EAC5C,KAAK,IAAI,SAAS,EAClB,SAAS,IAAI,SAAS,CACvB,CAAC;AACF,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC5B,QAAA,OAAO,MAAK;AACV,YAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AACjC,SAAC,CAAC;KACH;AAED;;;;;AAKG;IACH,IAAI,CACF,WAAoE,EACpE,UAA6D,EAAA;;;QAI7D,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvB,WAA4D,EAC5D,UAAyD,CAC1D,CAAC;KACH;AAED;;AAEG;AACH,IAAA,KAAK,CAAI,UAAgD,EAAA;QACvD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KACpC;AAED;;AAEG;AACK,IAAA,YAAY,CAAC,QAAsC,EAAA;AACzD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;KAChC;AAED;;AAEG;AACK,IAAA,eAAe,CAAC,QAAsC,EAAA;QAC5D,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC9B;KACF;IAEO,gBAAgB,GAAA;QACtB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AAC1C,QAAA,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;AAC3B,YAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AACjC,SAAC,CAAC,CAAC;KACJ;IAEO,cAAc,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,YAAA,QAAQ,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD,KAAK,SAAS,CAAC,OAAO;AACpB,oBAAAK,KAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;oBACpD,MAAM;gBACR,KAAK,SAAS,CAAC,QAAQ,CAAC;gBACxB,KAAK,SAAS,CAAC,KAAK;AAClB,oBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAqC,CAAC;AAC1D,oBAAAA,KAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAsB,CAAC,CAAC,EAAE,CAAC;oBAC3D,MAAM;AACR,gBAAA;oBACE,SAAS,GAAG,KAAK,CAAC;oBAClB,MAAM;aACT;YACD,IAAI,SAAS,EAAE;AACb,gBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,gBAAA,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;aAC1B;SACF;KACF;AAEO,IAAA,eAAe,CAAC,QAAsC,EAAA;QAC5D,MAAM,aAAa,GAAG,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClE,QAAQ,aAAa;YACnB,KAAK,SAAS,CAAC,OAAO,CAAC;YACvB,KAAK,SAAS,CAAC,MAAM;AACnB,gBAAA,IAAI,QAAQ,CAAC,IAAI,EAAE;AACjB,oBAAAA,KAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;iBACzD;gBACD,MAAM;YACR,KAAK,SAAS,CAAC,OAAO;AACpB,gBAAA,IAAI,QAAQ,CAAC,QAAQ,EAAE;oBACrBA,KAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;iBAC9C;gBACD,MAAM;YACR,KAAK,SAAS,CAAC,QAAQ,CAAC;YACxB,KAAK,SAAS,CAAC,KAAK;AAClB,gBAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;AAClB,oBAAAA,KAAQ,CACN,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAsB,CAAC,CAC3D,EAAE,CAAC;iBACL;gBACD,MAAM;AACR,YAAA;;AAEE,gBAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;AAClB,oBAAAA,KAAQ,CACN,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAsB,CAAC,CAC3D,EAAE,CAAC;iBACL;SACJ;KACF;AAED;;;AAGG;IACH,MAAM,GAAA;AACJ,QAAA,MAAM,KAAK,GACT,IAAI,CAAC,MAAM,KAA6B,QAAA;YACxC,IAAI,CAAC,MAAM,KAAA,SAAA,iCAA+B;QAC5C,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,WAAW,CAAA,SAAA,iCAA2B,CAAC;SAC7C;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;AAGG;IACH,KAAK,GAAA;AACH,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,+CAA+B;QACxD,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,WAAW,CAAA,SAAA,iCAA2B,CAAC;SAC7C;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;;AAIG;IACH,MAAM,GAAA;AACJ,QAAA,MAAM,KAAK,GACT,IAAI,CAAC,MAAM,KAA8B,SAAA;YACzC,IAAI,CAAC,MAAM,KAAA,SAAA,iCAA+B;QAC5C,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,WAAW,CAAA,WAAA,mCAA6B,CAAC;SAC/C;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AACF;;AC7qBD;;;;;;;;;;;;;;;AAeG;AAmCH;;;;;;;;;;;AAWG;MACU,SAAS,CAAA;IAGpB,WACU,CAAA,QAA6B,EACrC,QAA2B,EAAA;QADnB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAqB;AAGrC,QAAA,IAAI,QAAQ,YAAY,QAAQ,EAAE;AAChC,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;SAC3B;aAAM;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;SAChE;KACF;AAED;;;;AAIG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KACpE;IAES,OAAO,CACf,OAA4B,EAC5B,QAAkB,EAAA;AAElB,QAAA,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KACzC;AAED;;AAEG;AACH,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;KAC9C;AAED;;AAEG;AACH,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;KAC9B;AAED;;AAEG;AACH,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AAED;;;AAGG;AACH,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3C;AAED;;AAEG;AACH,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAED;;;AAGG;AACH,IAAA,IAAI,MAAM,GAAA;QACR,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC9D,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;KAC/C;AAED;;AAEG;AACH,IAAA,YAAY,CAAC,IAAY,EAAA;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,EAAE,EAAE;AAC9B,YAAA,MAAM,oBAAoB,CAAC,IAAI,CAAC,CAAC;SAClC;KACF;AACF,CAAA;AAED;;;AAGG;AACa,SAAA,gBAAgB,CAC9B,GAAc,EACd,oBAA6B,EAAA;AAE7B,IAAA,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AAC7B,IAAA,MAAM,WAAW,GAAGH,UAAQ,CAC1B,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,SAAS,EACb,oBAAoB,CACrB,CAAC;IACF,OAAO,GAAG,CAAC,OAAO;AACf,SAAA,qBAAqB,CAAC,WAAW,EAAE,kBAAkB,CAAC;AACtD,SAAA,IAAI,CAAC,KAAK,IACT,oBAAoB,KAAK,SAAS;AAChC;AACG,YAAA,KAAqB,CAAC,KAAK,CAAC,CAAC,EAAE,oBAAoB,CAAC;UACpD,KAAqB,CAC3B,CAAC;AACN,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC7B,GAAc,EACd,oBAA6B,EAAA;AAE7B,IAAA,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC5B,IAAA,MAAM,WAAW,GAAGA,UAAQ,CAC1B,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,SAAS,EACb,oBAAoB,CACrB,CAAC;IACF,OAAO,GAAG,CAAC,OAAO;AACf,SAAA,qBAAqB,CAAC,WAAW,EAAE,iBAAiB,CAAC;AACrD,SAAA,IAAI,CAAC,IAAI,IACR,oBAAoB,KAAK,SAAS;AAChC;AACG,YAAA,IAAa,CAAC,KAAK,CAAC,CAAC,EAAE,oBAAoB,CAAC;UAC5C,IAAa,CACnB,CAAC;AACN,CAAC;AA4CD;;;;;;;;AAQG;SACaI,aAAW,CACzB,GAAc,EACd,IAAqC,EACrC,QAAmB,EAAA;AAEnB,IAAA,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IAChC,MAAM,WAAW,GAAG,eAAe,CACjC,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,SAAS,EACb,WAAW,EAAE,EACb,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EACvB,QAAQ,CACT,CAAC;IACF,OAAO,GAAG,CAAC,OAAO;AACf,SAAA,qBAAqB,CAAC,WAAW,EAAE,iBAAiB,CAAC;SACrD,IAAI,CAAC,aAAa,IAAG;QACpB,OAAO;AACL,YAAA,QAAQ,EAAE,aAAa;YACvB,GAAG;SACJ,CAAC;AACJ,KAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;;;;AAQG;SACaC,sBAAoB,CAClC,GAAc,EACd,IAAqC,EACrC,QAAmB,EAAA;AAEnB,IAAA,GAAG,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;AACzC,IAAA,OAAO,IAAI,UAAU,CAAC,GAAG,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;;;;AASG;AACa,SAAAC,cAAY,CAC1B,GAAc,EACd,KAAa,EACb,MAAA,GAAuB,YAAY,CAAC,GAAG,EACvC,QAAmB,EAAA;AAEnB,IAAA,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IACjC,MAAM,IAAI,GAAG,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3C,IAAA,MAAM,aAAa,GAAG,EAAE,GAAG,QAAQ,EAAc,CAAC;AAClD,IAAA,IAAI,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE;AACpE,QAAA,aAAa,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,WAAY,CAAC;KAClD;IACD,OAAOF,aAAW,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AACpD,CAAC;AAED;;;;;;;;;;;;;;;;;;AAkBG;AACG,SAAUG,SAAO,CAAC,GAAc,EAAA;AACpC,IAAA,MAAM,WAAW,GAAe;AAC9B,QAAA,QAAQ,EAAE,EAAE;AACZ,QAAA,KAAK,EAAE,EAAE;KACV,CAAC;AACF,IAAA,OAAO,aAAa,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;AACjE,CAAC;AAED;;;;;AAKG;AACH,eAAe,aAAa,CAC1B,GAAc,EACd,WAAuB,EACvB,SAAkB,EAAA;AAElB,IAAA,MAAM,GAAG,GAAgB;;QAEvB,SAAS;KACV,CAAC;IACF,MAAM,QAAQ,GAAG,MAAMR,MAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACtC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAChD,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC1C,IAAA,IAAI,QAAQ,CAAC,aAAa,IAAI,IAAI,EAAE;QAClC,MAAM,aAAa,CAAC,GAAG,EAAE,WAAW,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;KAC/D;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;AACa,SAAAA,MAAI,CAClB,GAAc,EACd,OAA4B,EAAA;AAE5B,IAAA,IAAI,OAAO,IAAI,IAAI,EAAE;AACnB,QAAA,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE;AAC1C,YAAA,cAAc,CACZ,oBAAoB;AACpB,4BAAgB,CAAC;AACjB,4BAAgB,IAAI,EACpB,OAAO,CAAC,UAAU,CACnB,CAAC;SACH;KACF;AACD,IAAA,MAAM,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;IACzB,MAAM,WAAW,GAAGS,MAAY,CAC9B,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,SAAS;oBACG,GAAG,EACnB,EAAE,CAAC,SAAS,EACZ,EAAE,CAAC,UAAU,CACd,CAAC;IACF,OAAO,GAAG,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;;AAMG;AACG,SAAUV,aAAW,CAAC,GAAc,EAAA;AACxC,IAAA,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AAChC,IAAA,MAAM,WAAW,GAAGW,aAAmB,CACrC,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,SAAS,EACb,WAAW,EAAE,CACd,CAAC;IACF,OAAO,GAAG,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAAR,gBAAc,CAC5B,GAAc,EACd,QAA2B,EAAA;AAE3B,IAAA,GAAG,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AACnC,IAAA,MAAM,WAAW,GAAGS,gBAAsB,CACxC,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,SAAS,EACb,QAAQ,EACR,WAAW,EAAE,CACd,CAAC;IACF,OAAO,GAAG,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;AAKG;AACG,SAAUC,gBAAc,CAAC,GAAc,EAAA;AAC3C,IAAA,GAAG,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AACnC,IAAA,MAAM,WAAW,GAAGC,cAAsB,CACxC,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,SAAS,EACb,WAAW,EAAE,CACd,CAAC;IACF,OAAO,GAAG,CAAC,OAAO;AACf,SAAA,qBAAqB,CAAC,WAAW,EAAE,iBAAiB,CAAC;SACrD,IAAI,CAAC,GAAG,IAAG;AACV,QAAA,IAAI,GAAG,KAAK,IAAI,EAAE;YAChB,MAAM,aAAa,EAAE,CAAC;SACvB;AACD,QAAA,OAAO,GAAG,CAAC;AACb,KAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;AAKG;AACG,SAAUV,cAAY,CAAC,GAAc,EAAA;AACzC,IAAA,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;AACjC,IAAA,MAAM,WAAW,GAAGW,cAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;IACrE,OAAO,GAAG,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;;;;;AASG;AACa,SAAAC,WAAS,CAAC,GAAc,EAAE,SAAiB,EAAA;AACzD,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrD,IAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7D,OAAO,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC9C;;ACzfA;;;;;;;;;;;;;;;AAeG;AAsCG,SAAU,KAAK,CAAC,IAAa,EAAA;AACjC,IAAA,OAAO,iBAAiB,CAAC,IAAI,CAAC,IAAc,CAAC,CAAC;AAChD,CAAC;AAED;;AAEG;AACH,SAAS,UAAU,CAAC,OAA4B,EAAE,GAAW,EAAA;AAC3D,IAAA,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACrC,CAAC;AAED;;;AAGG;AACH,SAAS,WAAW,CAClB,GAAoC,EACpC,IAAa,EAAA;AAEb,IAAA,IAAI,GAAG,YAAY,mBAAmB,EAAE;QACtC,MAAM,OAAO,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE;YAC3B,MAAM,eAAe,EAAE,CAAC;SACzB;QACD,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,OAAQ,CAAC,CAAC;AAC3D,QAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,YAAA,OAAO,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SACrC;aAAM;AACL,YAAA,OAAO,SAAS,CAAC;SAClB;KACF;SAAM;;AAEL,QAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,YAAA,OAAOA,WAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAC7B;aAAM;AACL,YAAA,OAAO,GAAG,CAAC;SACZ;KACF;AACH,CAAC;AAqBe,SAAAC,KAAG,CACjB,YAA6C,EAC7C,SAAkB,EAAA;AAElB,IAAA,IAAI,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE;AACjC,QAAA,IAAI,YAAY,YAAY,mBAAmB,EAAE;AAC/C,YAAA,OAAO,UAAU,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;SAC5C;aAAM;AACL,YAAA,MAAM,eAAe,CACnB,0EAA0E,CAC3E,CAAC;SACH;KACF;SAAM;AACL,QAAA,OAAO,WAAW,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;KAC7C;AACH,CAAC;AAED,SAAS,aAAa,CACpB,IAAY,EACZ,MAAwB,EAAA;AAExB,IAAA,MAAM,YAAY,GAAG,MAAM,GAAG,yBAAyB,CAAC,CAAC;AACzD,IAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AACxB,QAAA,OAAO,IAAI,CAAC;KACb;IACD,OAAO,QAAQ,CAAC,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACzD,CAAC;AAEK,SAAUC,wBAAsB,CACpC,OAA4B,EAC5B,IAAY,EACZ,IAAY,EACZ,OAAA,GAEI,EAAE,EAAA;IAEN,OAAO,CAAC,IAAI,GAAG,CAAA,EAAG,IAAI,CAAI,CAAA,EAAA,IAAI,EAAE,CAAC;AACjC,IAAA,MAAM,MAAM,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;;IAExC,IAAI,MAAM,EAAE;QACV,KAAK,UAAU,CAAC,CAAW,QAAA,EAAA,OAAO,CAAC,IAAI,CAAA,EAAA,CAAI,CAAC,CAAC;AAC7C,QAAA,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KACvC;AACD,IAAA,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAChC,IAAA,OAAO,CAAC,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,CAAC;AAC9C,IAAA,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;IAClC,IAAI,aAAa,EAAE;AACjB,QAAA,OAAO,CAAC,kBAAkB;YACxB,OAAO,aAAa,KAAK,QAAQ;AAC/B,kBAAE,aAAa;AACf,kBAAE,mBAAmB,CAAC,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;KACzE;AACH,CAAC;AAED;;;;;AAKG;MACU,mBAAmB,CAAA;AAgB9B,IAAA,WAAA;AACE;;AAEG;AACM,IAAA,GAAgB,EAChB,aAAiD;AAC1D;;AAEG;IACM,iBAA0D;AACnE;;AAEG;AACM,IAAA,IAAa,EACb,gBAAyB,EAC3B,gBAAA,GAAmB,KAAK,EAAA;QAXtB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAChB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAoC;QAIjD,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAyC;QAI1D,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAS;QACb,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAS;QAC3B,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAQ;QA9BjC,IAAO,CAAA,OAAA,GAAoB,IAAI,CAAC;AAChC;;;;AAIG;QACK,IAAK,CAAA,KAAA,GAAW,YAAY,CAAC;QACrC,IAAS,CAAA,SAAA,GAAW,OAAO,CAAC;QACT,IAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;QAExC,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;AAsBhC,QAAA,IAAI,CAAC,sBAAsB,GAAG,gCAAgC,CAAC;AAC/D,QAAA,IAAI,CAAC,mBAAmB,GAAG,6BAA6B,CAAC;AACzD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3B,QAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SAC9D;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SAC5D;KACF;AAED;;;AAGG;AACH,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,IAAI,IAAI,CAAC,IAAY,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;AACrB,YAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC7D;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SACtD;KACF;AAED;;AAEG;AACH,IAAA,IAAI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC;KACjC;IAED,IAAI,kBAAkB,CAAC,IAAY,EAAA;AACjC,QAAA,cAAc,CACZ,MAAM;AACN,uBAAe,CAAC;AAChB,wBAAgB,MAAM,CAAC,iBAAiB,EACxC,IAAI,CACL,CAAC;AACF,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;KACjC;AAED;;;AAGG;AACH,IAAA,IAAI,qBAAqB,GAAA;QACvB,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACpC;IAED,IAAI,qBAAqB,CAAC,IAAY,EAAA;AACpC,QAAA,cAAc,CACZ,MAAM;AACN,uBAAe,CAAC;AAChB,wBAAgB,MAAM,CAAC,iBAAiB,EACxC,IAAI,CACL,CAAC;AACF,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;KACpC;AAED,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,OAAO,IAAI,CAAC,kBAAkB,CAAC;SAChC;AACD,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QACjE,IAAI,IAAI,EAAE;AACR,YAAA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACxC,YAAA,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,OAAO,SAAS,CAAC,WAAW,CAAC;aAC9B;SACF;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE;AACrE,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;SACxC;AACD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QACzE,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC;;;;;YAKzC,OAAO,MAAM,CAAC,KAAK,CAAC;SACrB;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACpD,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SACxB;AACD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AAED;;;AAGG;AACH,IAAA,qBAAqB,CAAC,GAAa,EAAA;AACjC,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KACjC;AAED;;;AAGG;IACH,YAAY,CACV,WAA8B,EAC9B,cAAmC,EACnC,SAAwB,EACxB,aAA4B,EAC5B,KAAK,GAAG,IAAI,EAAA;AAEZ,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,OAAO,GAAG,WAAW,CACzB,WAAW,EACX,IAAI,CAAC,MAAM,EACX,SAAS,EACT,aAAa,EACb,cAAc,EACd,IAAI,CAAC,gBAAgB,EACrB,KAAK,EACL,IAAI,CAAC,gBAAgB,CACtB,CAAC;AACF,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;AAE5B,YAAA,OAAO,CAAC,UAAU,EAAE,CAAC,IAAI,CACvB,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,EACpC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CACrC,CAAC;AACF,YAAA,OAAO,OAAO,CAAC;SAChB;aAAM;AACL,YAAA,OAAO,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC;SACtC;KACF;AAED,IAAA,MAAM,qBAAqB,CACzB,WAA8B,EAC9B,cAAmC,EAAA;QAEnC,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACnD,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,iBAAiB,EAAE;AACzB,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC,YAAY,CACtB,WAAW,EACX,cAAc,EACd,SAAS,EACT,aAAa,CACd,CAAC,UAAU,EAAE,CAAC;KAChB;AACF;;;;;AC/WD;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,MAAM,YAAY,GAAG,SAAS;;ACpBrC;;;;;;;;;;;;;;;AAeG;AAqEH;;;;;;;;;;;;;AAaG;AACa,SAAA,QAAQ,CACtB,GAAqB,EACrB,oBAA6B,EAAA;AAE7B,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,OAAO,gBAAgB,CAAC,GAAgB,EAAE,oBAAoB,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;;AAQG;SACa,WAAW,CACzB,GAAqB,EACrB,IAAqC,EACrC,QAAyB,EAAA;AAEzB,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC9B,OAAOC,aAAmB,CACxB,GAAgB,EAChB,IAAI,EACJ,QAA4B,CAC7B,CAAC;AACJ,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,YAAY,CAC1B,GAAqB,EACrB,KAAa,EACb,MAAqB,EACrB,QAAyB,EAAA;AAEzB,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC9B,OAAOC,cAAoB,CACzB,GAAgB,EAChB,KAAK,EACL,MAAM,EACN,QAA4B,CAC7B,CAAC;AACJ,CAAC;AAED;;;;;;;;AAQG;SACa,oBAAoB,CAClC,GAAqB,EACrB,IAAqC,EACrC,QAAyB,EAAA;AAEzB,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC9B,OAAOC,sBAA4B,CACjC,GAAgB,EAChB,IAAI,EACJ,QAA4B,CACf,CAAC;AAClB,CAAC;AAED;;;;;;AAMG;AACG,SAAU,WAAW,CAAC,GAAqB,EAAA;AAC/C,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,OAAOC,aAAmB,CAAC,GAAgB,CAA0B,CAAC;AACxE,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,cAAc,CAC5B,GAAqB,EACrB,QAA0B,EAAA;AAE1B,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,OAAOC,gBAAsB,CAC3B,GAAgB,EAChB,QAAqC,CACb,CAAC;AAC7B,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;AACa,SAAA,IAAI,CAClB,GAAqB,EACrB,OAAqB,EAAA;AAErB,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,OAAOC,MAAY,CAAC,GAAgB,EAAE,OAAO,CAAC,CAAC;AACjD,CAAC;AAED;;;;;;;;;;;;;;;;;;AAkBG;AACG,SAAU,OAAO,CAAC,GAAqB,EAAA;AAC3C,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,OAAOC,SAAe,CAAC,GAAgB,CAAC,CAAC;AAC3C,CAAC;AAED;;;;;;AAMG;AACG,SAAU,cAAc,CAAC,GAAqB,EAAA;AAClD,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,OAAOC,gBAAsB,CAAC,GAAgB,CAAC,CAAC;AAClD,CAAC;AAED;;;;;AAKG;AACG,SAAU,YAAY,CAAC,GAAqB,EAAA;AAChD,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,OAAOC,cAAoB,CAAC,GAAgB,CAAC,CAAC;AAChD,CAAC;AAqBe,SAAA,GAAG,CACjB,YAAgD,EAChD,SAAkB,EAAA;AAElB,IAAA,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAChD,IAAA,OAAOC,KAAW,CAChB,YAA+C,EAC/C,SAAS,CACV,CAAC;AACJ,CAAC;AAED;;AAEG;AACa,SAAA,SAAS,CAAC,GAAqB,EAAE,SAAiB,EAAA;AAChE,IAAA,OAAOC,WAAiB,CAAC,GAAgB,EAAE,SAAS,CAAC,CAAC;AACxD,CAAC;AAED;;;;;;;AAOG;SACa,UAAU,CACxB,MAAmB,MAAM,EAAE,EAC3B,SAAkB,EAAA;AAElB,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,eAAe,GAAwB,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;AAC7E,IAAA,MAAM,eAAe,GAAG,eAAe,CAAC,YAAY,CAAC;AACnD,QAAA,UAAU,EAAE,SAAS;AACtB,KAAA,CAAC,CAAC;AACH,IAAA,MAAM,QAAQ,GAAG,iCAAiC,CAAC,SAAS,CAAC,CAAC;IAC9D,IAAI,QAAQ,EAAE;AACZ,QAAA,sBAAsB,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC,CAAC;KACtD;AACD,IAAA,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,sBAAsB,CACpC,OAAwB,EACxB,IAAY,EACZ,IAAY,EACZ,OAAA,GAEI,EAAE,EAAA;IAENC,wBAAuB,CAAC,OAA8B,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC/E;;AC5WA;;;;;;;;;;;;;;;AAeG;AAMH;;;;;;;;;;;;;;;AAeG;AACa,SAAA,OAAO,CACrB,GAAqB,EACrB,oBAA6B,EAAA;AAE7B,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,OAAO,eAAe,CAAC,GAAgB,EAAE,oBAAoB,CAAC,CAAC;AACjE,CAAC;AAED;;;;;;;;;;;AAWG;AACa,SAAA,SAAS,CACvB,GAAqB,EACrB,oBAA6B,EAAA;AAE7B,IAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AACpE;;AC9DA;;;;AAIG;AAyCH,SAAS,OAAO,CACd,SAA6B,EAC7B,EAAE,kBAAkB,EAAE,GAAG,EAA0B,EAAA;IAEnD,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;IACxD,MAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;IAC5D,MAAM,gBAAgB,GAAG,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;AAErE,IAAA,OAAO,IAAI,mBAAmB,CAC5B,GAAG,EACH,YAAY,EACZ,gBAAgB,EAChB,GAAG,EACH,WAAW,CACZ,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,GAAA;AACtB,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,YAAY,EACZ,OAAO,EAER,QAAA,4BAAA,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAC7B,CAAC;;AAEF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,EAAiB,CAAC,CAAC;;AAElD,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;AACrD,CAAC;AAED,eAAe,EAAE;;;;"}
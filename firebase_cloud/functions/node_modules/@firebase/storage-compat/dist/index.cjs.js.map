{"version": 3, "file": "index.cjs.js", "sources": ["../src/tasksnapshot.ts", "../src/task.ts", "../src/list.ts", "../src/reference.ts", "../src/service.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { UploadTaskSnapshot } from '@firebase/storage';\nimport { ReferenceCompat } from './reference';\nimport { UploadTaskCompat } from './task';\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class UploadTaskSnapshotCompat\n  implements types.UploadTaskSnapshot, Compat<UploadTaskSnapshot>\n{\n  constructor(\n    readonly _delegate: UploadTaskSnapshot,\n    readonly task: UploadTaskCompat,\n    readonly ref: ReferenceCompat\n  ) {}\n\n  get bytesTransferred(): number {\n    return this._delegate.bytesTransferred;\n  }\n  get metadata(): types.FullMetadata {\n    return this._delegate.metadata as types.FullMetadata;\n  }\n  get state(): string {\n    return this._delegate.state;\n  }\n  get totalBytes(): number {\n    return this._delegate.totalBytes;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  UploadTask,\n  StorageError,\n  UploadTaskSnapshot,\n  TaskEvent,\n  StorageObserver\n} from '@firebase/storage';\nimport { UploadTaskSnapshotCompat } from './tasksnapshot';\nimport { ReferenceCompat } from './reference';\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class UploadTaskCompat implements types.UploadTask, Compat<UploadTask> {\n  constructor(\n    readonly _delegate: UploadTask,\n    private readonly _ref: ReferenceCompat\n  ) {}\n\n  get snapshot(): UploadTaskSnapshotCompat {\n    return new UploadTaskSnapshotCompat(\n      this._delegate.snapshot,\n      this,\n      this._ref\n    );\n  }\n\n  cancel = this._delegate.cancel.bind(this._delegate);\n  catch = this._delegate.catch.bind(this._delegate);\n  pause = this._delegate.pause.bind(this._delegate);\n  resume = this._delegate.resume.bind(this._delegate);\n\n  then(\n    onFulfilled?: ((a: UploadTaskSnapshotCompat) => unknown) | null,\n    onRejected?: ((a: StorageError) => unknown) | null\n  ): Promise<unknown> {\n    return this._delegate.then(snapshot => {\n      if (onFulfilled) {\n        return onFulfilled(\n          new UploadTaskSnapshotCompat(snapshot, this, this._ref)\n        );\n      }\n    }, onRejected);\n  }\n\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | types.StorageObserver<UploadTaskSnapshotCompat>\n      | null\n      | ((a: UploadTaskSnapshotCompat) => unknown),\n    error?: ((error: StorageError) => void) | null,\n    completed?: () => void | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshotCompat> {\n    let wrappedNextOrObserver:\n      | StorageObserver<UploadTaskSnapshot>\n      | undefined\n      | ((a: UploadTaskSnapshot) => unknown) = undefined;\n    if (!!nextOrObserver) {\n      if (typeof nextOrObserver === 'function') {\n        wrappedNextOrObserver = (taskSnapshot: UploadTaskSnapshot) =>\n          nextOrObserver(\n            new UploadTaskSnapshotCompat(taskSnapshot, this, this._ref)\n          );\n      } else {\n        wrappedNextOrObserver = {\n          next: !!nextOrObserver.next\n            ? (taskSnapshot: UploadTaskSnapshot) =>\n                nextOrObserver.next!(\n                  new UploadTaskSnapshotCompat(taskSnapshot, this, this._ref)\n                )\n            : undefined,\n          complete: nextOrObserver.complete || undefined,\n          error: nextOrObserver.error || undefined\n        };\n      }\n    }\n    return this._delegate.on(\n      type,\n      wrappedNextOrObserver,\n      error || undefined,\n      completed || undefined\n    );\n  }\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `FirebaseStorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ListResult } from '@firebase/storage';\nimport * as types from '@firebase/storage-types';\nimport { ReferenceCompat } from './reference';\nimport { StorageServiceCompat } from './service';\nimport { Compat } from '@firebase/util';\n\nexport class ListResultCompat implements types.ListResult, Compat<ListResult> {\n  constructor(\n    readonly _delegate: ListResult,\n    private readonly _service: StorageServiceCompat\n  ) {}\n\n  get prefixes(): ReferenceCompat[] {\n    return this._delegate.prefixes.map(\n      ref => new ReferenceCompat(ref, this._service)\n    );\n  }\n  get items(): ReferenceCompat[] {\n    return this._delegate.items.map(\n      ref => new ReferenceCompat(ref, this._service)\n    );\n  }\n  get nextPageToken(): string | null {\n    return this._delegate.nextPageToken || null;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *  http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  StorageReference,\n  uploadBytesResumable,\n  list,\n  listAll,\n  getDownloadURL,\n  getMetadata,\n  updateMetadata,\n  deleteObject,\n  UploadTask,\n  StringFormat,\n  UploadMetadata,\n  FullMetadata,\n  SettableMetadata,\n  _UploadTask,\n  _getChild,\n  _Reference,\n  _FbsBlob,\n  _dataFromString,\n  _invalidRootOperation\n} from '@firebase/storage';\n\nimport { UploadTaskCompat } from './task';\nimport { ListResultCompat } from './list';\nimport { StorageServiceCompat } from './service';\n\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class ReferenceCompat\n  implements types.Reference, Compat<StorageReference>\n{\n  constructor(\n    readonly _delegate: StorageReference,\n    public storage: StorageServiceCompat\n  ) {}\n\n  get name(): string {\n    return this._delegate.name;\n  }\n\n  get bucket(): string {\n    return this._delegate.bucket;\n  }\n\n  get fullPath(): string {\n    return this._delegate.fullPath;\n  }\n\n  toString(): string {\n    return this._delegate.toString();\n  }\n\n  /**\n   * @returns A reference to the object obtained by\n   * appending childPath, removing any duplicate, beginning, or trailing\n   * slashes.\n   */\n  child(childPath: string): types.Reference {\n    const reference = _getChild(this._delegate, childPath);\n    return new ReferenceCompat(reference, this.storage);\n  }\n\n  get root(): types.Reference {\n    return new ReferenceCompat(this._delegate.root, this.storage);\n  }\n\n  /**\n   * @returns A reference to the parent of the\n   * current object, or null if the current object is the root.\n   */\n  get parent(): types.Reference | null {\n    const reference = this._delegate.parent;\n    if (reference == null) {\n      return null;\n    }\n    return new ReferenceCompat(reference, this.storage);\n  }\n\n  /**\n   * Uploads a blob to this object's location.\n   * @param data - The blob to upload.\n   * @returns An UploadTask that lets you control and\n   * observe the upload.\n   */\n  put(\n    data: Blob | Uint8Array | ArrayBuffer,\n    metadata?: types.FullMetadata\n  ): types.UploadTask {\n    this._throwIfRoot('put');\n    return new UploadTaskCompat(\n      uploadBytesResumable(this._delegate, data, metadata as UploadMetadata),\n      this\n    );\n  }\n\n  /**\n   * Uploads a string to this object's location.\n   * @param value - The string to upload.\n   * @param format - The format of the string to upload.\n   * @returns An UploadTask that lets you control and\n   * observe the upload.\n   */\n  putString(\n    value: string,\n    format: StringFormat = StringFormat.RAW,\n    metadata?: types.UploadMetadata\n  ): types.UploadTask {\n    this._throwIfRoot('putString');\n    const data = _dataFromString(format, value);\n    const metadataClone = { ...metadata };\n    if (metadataClone['contentType'] == null && data.contentType != null) {\n      metadataClone['contentType'] = data.contentType;\n    }\n    return new UploadTaskCompat(\n      new _UploadTask(\n        this._delegate as _Reference,\n        new _FbsBlob(data.data, true),\n        metadataClone as FullMetadata & { [k: string]: string }\n      ) as UploadTask,\n      this\n    );\n  }\n\n  /**\n   * List all items (files) and prefixes (folders) under this storage reference.\n   *\n   * This is a helper method for calling list() repeatedly until there are\n   * no more results. The default pagination size is 1000.\n   *\n   * Note: The results may not be consistent if objects are changed while this\n   * operation is running.\n   *\n   * Warning: listAll may potentially consume too many resources if there are\n   * too many results.\n   *\n   * @returns A Promise that resolves with all the items and prefixes under\n   *  the current storage reference. `prefixes` contains references to\n   *  sub-directories and `items` contains references to objects in this\n   *  folder. `nextPageToken` is never returned.\n   */\n  listAll(): Promise<types.ListResult> {\n    return listAll(this._delegate).then(\n      r => new ListResultCompat(r, this.storage)\n    );\n  }\n\n  /**\n   * List items (files) and prefixes (folders) under this storage reference.\n   *\n   * List API is only available for Firebase Rules Version 2.\n   *\n   * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n   * delimited folder structure. Refer to GCS's List API if you want to learn more.\n   *\n   * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n   * support objects whose paths end with \"/\" or contain two consecutive\n   * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n   * list() may fail if there are too many unsupported objects in the bucket.\n   *\n   * @param options - See ListOptions for details.\n   * @returns A Promise that resolves with the items and prefixes.\n   * `prefixes` contains references to sub-folders and `items`\n   * contains references to objects in this folder. `nextPageToken`\n   * can be used to get the rest of the results.\n   */\n  list(options?: types.ListOptions | null): Promise<types.ListResult> {\n    return list(this._delegate, options || undefined).then(\n      r => new ListResultCompat(r, this.storage)\n    );\n  }\n\n  /**\n   * A `Promise` that resolves with the metadata for this object. If this\n   * object doesn't exist or metadata cannot be retrieved, the promise is\n   * rejected.\n   */\n  getMetadata(): Promise<types.FullMetadata> {\n    return getMetadata(this._delegate) as Promise<types.FullMetadata>;\n  }\n\n  /**\n   * Updates the metadata for this object.\n   * @param metadata - The new metadata for the object.\n   * Only values that have been explicitly set will be changed. Explicitly\n   * setting a value to null will remove the metadata.\n   * @returns A `Promise` that resolves\n   * with the new metadata for this object.\n   * @see firebaseStorage.Reference.prototype.getMetadata\n   */\n  updateMetadata(\n    metadata: types.SettableMetadata\n  ): Promise<types.FullMetadata> {\n    return updateMetadata(\n      this._delegate,\n      metadata as SettableMetadata\n    ) as Promise<types.FullMetadata>;\n  }\n\n  /**\n   * @returns A `Promise` that resolves with the download\n   * URL for this object.\n   */\n  getDownloadURL(): Promise<string> {\n    return getDownloadURL(this._delegate);\n  }\n\n  /**\n   * Deletes the object at this location.\n   * @returns A `Promise` that resolves if the deletion succeeds.\n   */\n  delete(): Promise<void> {\n    this._throwIfRoot('delete');\n    return deleteObject(this._delegate);\n  }\n\n  private _throwIfRoot(name: string): void {\n    if ((this._delegate as _Reference)._location.path === '') {\n      throw _invalidRootOperation(name);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as types from '@firebase/storage-types';\nimport { FirebaseApp } from '@firebase/app-types';\n\nimport {\n  ref,\n  connectStorageEmulator,\n  FirebaseStorage,\n  _Location,\n  _invalidArgument,\n  _FirebaseStorageImpl\n} from '@firebase/storage';\nimport { ReferenceCompat } from './reference';\nimport { Compat, EmulatorMockTokenOptions } from '@firebase/util';\n\n/**\n * A service that provides firebaseStorage.Reference instances.\n * @param opt_url gs:// url to a custom Storage Bucket\n */\nexport class StorageServiceCompat\n  implements types.FirebaseStorage, Compat<FirebaseStorage>\n{\n  constructor(public app: FirebaseApp, readonly _delegate: FirebaseStorage) {}\n\n  get maxOperationRetryTime(): number {\n    return this._delegate.maxOperationRetryTime;\n  }\n\n  get maxUploadRetryTime(): number {\n    return this._delegate.maxUploadRetryTime;\n  }\n\n  /**\n   * Returns a firebaseStorage.Reference for the given path in the default\n   * bucket.\n   */\n  ref(path?: string): types.Reference {\n    if (isUrl(path)) {\n      throw _invalidArgument(\n        'ref() expected a child path but got a URL, use refFromURL instead.'\n      );\n    }\n    return new ReferenceCompat(ref(this._delegate, path), this);\n  }\n\n  /**\n   * Returns a firebaseStorage.Reference object for the given absolute URL,\n   * which must be a gs:// or http[s]:// URL.\n   */\n  refFromURL(url: string): types.Reference {\n    if (!isUrl(url)) {\n      throw _invalidArgument(\n        'refFromURL() expected a full URL but got a child path, use ref() instead.'\n      );\n    }\n    try {\n      _Location.makeFromUrl(url, (this._delegate as _FirebaseStorageImpl).host);\n    } catch (e) {\n      throw _invalidArgument(\n        'refFromUrl() expected a valid full URL but got an invalid one.'\n      );\n    }\n    return new ReferenceCompat(ref(this._delegate, url), this);\n  }\n\n  setMaxUploadRetryTime(time: number): void {\n    this._delegate.maxUploadRetryTime = time;\n  }\n\n  setMaxOperationRetryTime(time: number): void {\n    this._delegate.maxOperationRetryTime = time;\n  }\n\n  useEmulator(\n    host: string,\n    port: number,\n    options: {\n      mockUserToken?: EmulatorMockTokenOptions | string;\n    } = {}\n  ): void {\n    connectStorageEmulator(this._delegate, host, port, options);\n  }\n}\n\nfunction isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport firebase from '@firebase/app-compat';\nimport { _FirebaseNamespace } from '@firebase/app-types/private';\nimport {\n  StringFormat,\n  _TaskEvent as TaskEvent,\n  _TaskState as TaskState\n} from '@firebase/storage';\n\nimport { ReferenceCompat } from './reference';\nimport { StorageServiceCompat } from './service';\nimport * as types from '@firebase/storage-types';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\n/**\n * Type constant for Firebase Storage.\n */\nconst STORAGE_TYPE = 'storage-compat';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): types.FirebaseStorage {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const storageExp = container\n    .getProvider('storage')\n    .getImmediate({ identifier: url });\n\n  const storageServiceCompat: StorageServiceCompat = new StorageServiceCompat(\n    app,\n    storageExp\n  );\n  return storageServiceCompat;\n}\n\nexport function registerStorage(instance: _FirebaseNamespace): void {\n  const namespaceExports = {\n    // no-inline\n    TaskState,\n    TaskEvent,\n    StringFormat,\n    Storage: StorageServiceCompat,\n    Reference: ReferenceCompat\n  };\n  instance.INTERNAL.registerComponent(\n    new Component(STORAGE_TYPE, factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n\n  instance.registerVersion(name, version);\n}\n\nregisterStorage(firebase as unknown as _FirebaseNamespace);\n\n/**\n * Define extension behavior for `registerStorage`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    storage?: {\n      (app?: FirebaseApp, url?: string): types.FirebaseStorage;\n      Storage: typeof types.FirebaseStorage;\n\n      StringFormat: {\n        BASE64: types.StringFormat;\n        BASE64URL: types.StringFormat;\n        DATA_URL: types.StringFormat;\n        RAW: types.StringFormat;\n      };\n      TaskEvent: {\n        STATE_CHANGED: types.TaskEvent;\n      };\n      TaskState: {\n        CANCELED: types.TaskState;\n        ERROR: types.TaskState;\n        PAUSED: types.TaskState;\n        RUNNING: types.TaskState;\n        SUCCESS: types.TaskState;\n      };\n    };\n  }\n  interface FirebaseApp {\n    storage?(storageBucket?: string): types.FirebaseStorage;\n  }\n}\n"], "names": ["_get<PERSON><PERSON>d", "uploadBytesResumable", "StringFormat", "_dataFromString", "_UploadTask", "_FbsBlob", "listAll", "list", "getMetadata", "updateMetadata", "getDownloadURL", "deleteObject", "_invalidRootOperation", "_invalidArgument", "ref", "_Location", "connectStorageEmulator", "TaskState", "TaskEvent", "Component", "firebase"], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;MAQU,wBAAwB,CAAA;AAGnC,IAAA,WAAA,CACW,SAA6B,EAC7B,IAAsB,EACtB,GAAoB,EAAA;QAFpB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;QAC7B,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAkB;QACtB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAiB;KAC3B;AAEJ,IAAA,IAAI,gBAAgB,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;KACxC;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAA8B,CAAC;KACtD;AACD,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;KAC7B;AACD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;KAClC;AACF;;AC5CD;;;;;;;;;;;;;;;AAeG;MAcU,gBAAgB,CAAA;IAC3B,WACW,CAAA,SAAqB,EACb,IAAqB,EAAA;QAD7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAY;QACb,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAiB;AAWxC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACpD,QAAA,IAAA,CAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAClD,QAAA,IAAA,CAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAClD,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAbhD;AAEJ,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,wBAAwB,CACjC,IAAI,CAAC,SAAS,CAAC,QAAQ,EACvB,IAAI,EACJ,IAAI,CAAC,IAAI,CACV,CAAC;KACH;IAOD,IAAI,CACF,WAA+D,EAC/D,UAAkD,EAAA;QAElD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAG;YACpC,IAAI,WAAW,EAAE;AACf,gBAAA,OAAO,WAAW,CAChB,IAAI,wBAAwB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CACxD,CAAC;aACH;SACF,EAAE,UAAU,CAAC,CAAC;KAChB;AAED,IAAA,EAAE,CACA,IAAe,EACf,cAG8C,EAC9C,KAA8C,EAC9C,SAA6B,EAAA;QAE7B,IAAI,qBAAqB,GAGkB,SAAS,CAAC;AACrD,QAAA,IAAI,CAAC,CAAC,cAAc,EAAE;AACpB,YAAA,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;gBACxC,qBAAqB,GAAG,CAAC,YAAgC,KACvD,cAAc,CACZ,IAAI,wBAAwB,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAC5D,CAAC;aACL;iBAAM;AACL,gBAAA,qBAAqB,GAAG;AACtB,oBAAA,IAAI,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI;0BACvB,CAAC,YAAgC,KAC/B,cAAc,CAAC,IAAK,CAClB,IAAI,wBAAwB,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAC5D;AACL,0BAAE,SAAS;AACb,oBAAA,QAAQ,EAAE,cAAc,CAAC,QAAQ,IAAI,SAAS;AAC9C,oBAAA,KAAK,EAAE,cAAc,CAAC,KAAK,IAAI,SAAS;iBACzC,CAAC;aACH;SACF;AACD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CACtB,IAAI,EACJ,qBAAqB,EACrB,KAAK,IAAI,SAAS,EAClB,SAAS,IAAI,SAAS,CACvB,CAAC;KACH;AACF;;MC9EY,gBAAgB,CAAA;IAC3B,WACW,CAAA,SAAqB,EACb,QAA8B,EAAA;QADtC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAY;QACb,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;KAC7C;AAEJ,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAChC,GAAG,IAAI,IAAI,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAC/C,CAAC;KACH;AACD,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAC7B,GAAG,IAAI,IAAI,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAC/C,CAAC;KACH;AACD,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,IAAI,IAAI,CAAC;KAC7C;AACF;;ACzCD;;;;;;;;;;;;;;;AAeG;MA+BU,eAAe,CAAA;IAG1B,WACW,CAAA,SAA2B,EAC7B,OAA6B,EAAA;QAD3B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAkB;QAC7B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAsB;KAClC;AAEJ,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AAED,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;KAC9B;AAED,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;IAED,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAClC;AAED;;;;AAIG;AACH,IAAA,KAAK,CAAC,SAAiB,EAAA;QACrB,MAAM,SAAS,GAAGA,iBAAS,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACvD,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KACrD;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KAC/D;AAED;;;AAGG;AACH,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACxC,QAAA,IAAI,SAAS,IAAI,IAAI,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KACrD;AAED;;;;;AAKG;IACH,GAAG,CACD,IAAqC,EACrC,QAA6B,EAAA;AAE7B,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACzB,QAAA,OAAO,IAAI,gBAAgB,CACzBC,4BAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAA0B,CAAC,EACtE,IAAI,CACL,CAAC;KACH;AAED;;;;;;AAMG;IACH,SAAS,CACP,KAAa,EACb,MAAA,GAAuBC,oBAAY,CAAC,GAAG,EACvC,QAA+B,EAAA;AAE/B,QAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAC/B,MAAM,IAAI,GAAGC,uBAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5C,QAAA,MAAM,aAAa,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;AACtC,QAAA,IAAI,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE;AACpE,YAAA,aAAa,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;SACjD;QACD,OAAO,IAAI,gBAAgB,CACzB,IAAIC,mBAAW,CACb,IAAI,CAAC,SAAuB,EAC5B,IAAIC,gBAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAC7B,aAAuD,CAC1C,EACf,IAAI,CACL,CAAC;KACH;AAED;;;;;;;;;;;;;;;;AAgBG;IACH,OAAO,GAAA;QACL,OAAOC,eAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CACjC,CAAC,IAAI,IAAI,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAC3C,CAAC;KACH;AAED;;;;;;;;;;;;;;;;;;AAkBG;AACH,IAAA,IAAI,CAAC,OAAkC,EAAA;QACrC,OAAOC,YAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI,CACpD,CAAC,IAAI,IAAI,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAC3C,CAAC;KACH;AAED;;;;AAIG;IACH,WAAW,GAAA;AACT,QAAA,OAAOC,mBAAW,CAAC,IAAI,CAAC,SAAS,CAAgC,CAAC;KACnE;AAED;;;;;;;;AAQG;AACH,IAAA,cAAc,CACZ,QAAgC,EAAA;QAEhC,OAAOC,sBAAc,CACnB,IAAI,CAAC,SAAS,EACd,QAA4B,CACE,CAAC;KAClC;AAED;;;AAGG;IACH,cAAc,GAAA;AACZ,QAAA,OAAOC,sBAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACvC;AAED;;;AAGG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC5B,QAAA,OAAOC,oBAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACrC;AAEO,IAAA,YAAY,CAAC,IAAY,EAAA;QAC/B,IAAK,IAAI,CAAC,SAAwB,CAAC,SAAS,CAAC,IAAI,KAAK,EAAE,EAAE;AACxD,YAAA,MAAMC,6BAAqB,CAAC,IAAI,CAAC,CAAC;SACnC;KACF;AACF;;AC9OD;;;;;;;;;;;;;;;AAeG;AAgBH;;;AAGG;MACU,oBAAoB,CAAA;IAG/B,WAAmB,CAAA,GAAgB,EAAW,SAA0B,EAAA;QAArD,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAAW,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;KAAI;AAE5E,IAAA,IAAI,qBAAqB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC;KAC7C;AAED,IAAA,IAAI,kBAAkB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;KAC1C;AAED;;;AAGG;AACH,IAAA,GAAG,CAAC,IAAa,EAAA;AACf,QAAA,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;AACf,YAAA,MAAMC,wBAAgB,CACpB,oEAAoE,CACrE,CAAC;SACH;AACD,QAAA,OAAO,IAAI,eAAe,CAACC,WAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;KAC7D;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,GAAW,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACf,YAAA,MAAMD,wBAAgB,CACpB,2EAA2E,CAC5E,CAAC;SACH;AACD,QAAA,IAAI;YACFE,iBAAS,CAAC,WAAW,CAAC,GAAG,EAAG,IAAI,CAAC,SAAkC,CAAC,IAAI,CAAC,CAAC;SAC3E;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAMF,wBAAgB,CACpB,gEAAgE,CACjE,CAAC;SACH;AACD,QAAA,OAAO,IAAI,eAAe,CAACC,WAAG,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;KAC5D;AAED,IAAA,qBAAqB,CAAC,IAAY,EAAA;AAChC,QAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1C;AAED,IAAA,wBAAwB,CAAC,IAAY,EAAA;AACnC,QAAA,IAAI,CAAC,SAAS,CAAC,qBAAqB,GAAG,IAAI,CAAC;KAC7C;AAED,IAAA,WAAW,CACT,IAAY,EACZ,IAAY,EACZ,UAEI,EAAE,EAAA;QAENE,8BAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KAC7D;AACF,CAAA;AAED,SAAS,KAAK,CAAC,IAAa,EAAA;AAC1B,IAAA,OAAO,iBAAiB,CAAC,IAAI,CAAC,IAAc,CAAC,CAAC;AAChD;;;;;ACtGA;;;;;;;;;;;;;;;AAeG;AAuBH;;AAEG;AACH,MAAM,YAAY,GAAG,gBAAgB,CAAC;AAEtC,SAAS,OAAO,CACd,SAA6B,EAC7B,EAAE,kBAAkB,EAAE,GAAG,EAA0B,EAAA;;IAGnD,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;IAC/D,MAAM,UAAU,GAAG,SAAS;SACzB,WAAW,CAAC,SAAS,CAAC;AACtB,SAAA,YAAY,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;IAErC,MAAM,oBAAoB,GAAyB,IAAI,oBAAoB,CACzE,GAAG,EACH,UAAU,CACX,CAAC;AACF,IAAA,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAEK,SAAU,eAAe,CAAC,QAA4B,EAAA;AAC1D,IAAA,MAAM,gBAAgB,GAAG;;mBAEvBC,kBAAS;mBACTC,kBAAS;sBACThB,oBAAY;AACZ,QAAA,OAAO,EAAE,oBAAoB;AAC7B,QAAA,SAAS,EAAE,eAAe;KAC3B,CAAC;IACF,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CACjC,IAAIiB,mBAAS,CAAC,YAAY,EAAE,OAAO,EAAuB,QAAA,4BAAA;SACvD,eAAe,CAAC,gBAAgB,CAAC;AACjC,SAAA,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAC;AAEF,IAAA,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC1C,CAAC;AAED,eAAe,CAACC,4BAAyC,CAAC;;;;"}
// lib/features/home/<USER>/repositories/home_repository_impl.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:math' as math;
import '../../../../core/services/link_preview_service.dart';
import '../../../../core/services/user_data_cache_service.dart';
import '../../../../core/services/location_country_cache.dart';

import '../../../../core/utils/logger.dart';
import '../../domain/entities/map_marker.dart';
import '../../domain/entities/post.dart';
import '../../domain/entities/location_query.dart';
import '../../domain/entities/place_result.dart';
import '../../domain/entities/link_preview.dart';
import '../../domain/entities/geocoding_result.dart';
import '../../domain/repositories/home_repository.dart';
import '../models/post_model.dart';

import '../../../../core/database/local_database_manager.dart';
import '../../../notifications/data/services/notification_trigger_service.dart';
import '../../../activity/domain/services/activity_service.dart';
import '../../../admin/domain/services/admin_permission_service.dart';
import '../../../admin/presentation/providers/fake_account_provider.dart';
import '../../../activity/data/repositories/activity_repository_impl.dart';
import '../../domain/use_cases/create_post_use_case.dart';
import '../../domain/entities/comment.dart';
import '../../../../core/services/interaction_cache_service.dart';
import '../services/view_tracking_service.dart';
import '../../../../core/services/firestore_query_counter.dart';
import '../../domain/entities/geo_hash.dart';
import '../../domain/entities/post_sort_by.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/services/ip_address_service.dart';
import '../../../../core/config/api_config.dart';
import 'dart:ui' as ui;
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../services/user_follow_service.dart';
import '../services/user_like_service.dart';
import '../services/user_favorite_service.dart';
import '../../../../core/services/enhanced_analytics_service.dart';

/// 🏠 首頁資料倉庫實現
class HomeRepositoryImpl implements HomeRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final LocalDatabaseManager _localDB;
  final UserFollowService _followService;
  final UserLikeService _likeService;
  final UserFavoriteService _favoriteService;
  final ViewTrackingService _viewTrackingService;

  // Firestore 集合引用
  late final CollectionReference _postsCollection;
  late final CollectionReference _broadcastsCollection;
  late final CollectionReference _commentsCollection;
  late final CollectionReference _viewsCollection;
  late final CollectionReference _likesCollection;
  late final CollectionReference _favoritesCollection;

  // 活動記錄服務（懶載）
  ActivityService? _activityService;

  HomeRepositoryImpl({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    LocalDatabaseManager? localDB,
    UserFollowService? followService,
    UserLikeService? likeService,
    UserFavoriteService? favoriteService,
    ViewTrackingService? viewTrackingService,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance,
        _localDB = localDB ?? LocalDatabaseManager(),
        _followService = followService ?? UserFollowService(),
        _likeService = likeService ?? UserLikeService(),
        _favoriteService = favoriteService ?? UserFavoriteService(),
        _viewTrackingService = viewTrackingService ?? ViewTrackingService() {
    _postsCollection = _firestore.collection('posts');
    _broadcastsCollection = _firestore.collection('broadcasts');
    _commentsCollection = _firestore.collection('comments');
    _viewsCollection = _firestore.collection('views');
    _likesCollection = _firestore.collection('likes');
    _favoritesCollection = _firestore.collection('favorites');
  }

  // ========================================
  // 👤 用戶相關私有方法
  // ========================================

  /// 獲取當前用戶資訊並建立 PostAuthor
  Future<PostAuthorModel> _getPostAuthor(bool isAnonymous,
      {FakeAccount? fakeAccount}) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('user_not_authenticated');
    }

    Logger.i(
        '🎭 _getPostAuthor: isAnonymous=$isAnonymous, fakeAccount=${fakeAccount?.displayName}',
        'HomeRepository');

    // 如果正在使用假帳號，檢查是否匿名發文
    if (fakeAccount != null) {
      if (isAnonymous) {
        // 假帳號匿名發文，返回匿名資訊但使用假帳號的 userHash
        Logger.i(
            '🎭 假帳號匿名發文: ${fakeAccount.displayName} -> 匿名', 'HomeRepository');
        return PostAuthorModel(
          userId: fakeAccount.uid,
          displayName: tr('common.anonymous'),
          photoURL: null,
          userHash: _generateFakeAccountHash(fakeAccount.uid), // 使用假帳號的 hash
          isAnonymous: true,
          reputation: 0,
          title: tr('common.anonymous'),
        );
      } else {
        // 假帳號非匿名發文，返回假帳號資訊
        Logger.i('✅ 使用假帳號資訊: ${fakeAccount.displayName} (${fakeAccount.uid})',
            'HomeRepository');
        return PostAuthorModel(
          userId: fakeAccount.uid,
          displayName: fakeAccount.displayName,
          photoURL: fakeAccount.avatarUrl,
          userHash: _generateFakeAccountHash(fakeAccount.uid),
          isAnonymous: false,
          reputation: fakeAccount.stats.reputation.toInt(),
          title: _getReputationTitle(fakeAccount.stats.reputation),
        );
      }
    }

    // 獲取對應的用戶 hash（匿名或非匿名）
    final userHash = await _getUserHash(currentUser.uid, isAnonymous);

    // 如果是匿名發文，返回匿名作者
    if (isAnonymous) {
      return PostAuthorModel(
        userId: currentUser.uid,
        displayName: tr('common.anonymous'),
        photoURL: null,
        userHash: userHash, // 使用匿名專用的 hash
        isAnonymous: true,
        reputation: 0,
        title: tr('common.anonymous'),
      );
    }

    // 從 Firestore 獲取用戶完整資料
    final userProfile = await _getUserProfile(currentUser.uid);

    // 安全地處理 reputation 字段
    final reputation = (userProfile['reputation'] as num?) ?? 0;

    return PostAuthorModel(
      userId: currentUser.uid,
      displayName: userProfile['displayName'] ??
          currentUser.displayName ??
          tr('common.unknown_user'),
      photoURL: userProfile['photoURL'] ?? currentUser.photoURL,
      userHash: userHash, // 使用公開發文專用的 hash
      isAnonymous: false,
      reputation: reputation.toInt(),
      title: _getReputationTitle(reputation),
    );
  }

  /// 從快取或 Firestore 獲取用戶詳細資料
  Future<Map<String, dynamic>> _getUserProfile(String userId) async {
    try {
      // 使用快取服務獲取用戶資料，大幅減少 Firestore 讀取
      return await UserDataCacheService.getUserProfile(userId);
    } catch (e) {
      Logger.w('獲取用戶資料失敗: $e');
      return {};
    }
  }

  /// 根據聲譽分數獲取用戶頭銜
  String _getReputationTitle(num reputation) {
    if (reputation < 10) return tr('profile.titles.newbie'); // 新芽
    if (reputation < 50) return tr('profile.titles.resident'); // 居民
    if (reputation < 200) return tr('profile.titles.expert'); // 達人
    if (reputation < 500) return tr('profile.titles.master'); // 大師
    if (reputation < 1000) return tr('profile.titles.legend'); // 傳奇
    return tr('profile.titles.mythic'); // 傳說
  }

  /// 獲取當前用戶 ID
  String? _getCurrentUserId() {
    return _auth.currentUser?.uid;
  }

  /// 根據發文類型獲取用戶 Hash
  Future<String> _getUserHash(String userId, bool isAnonymous) async {
    try {
      // 從 Firestore 獲取用戶的 hash 資料
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;

        // 根據是否匿名返回對應的 hash
        if (isAnonymous) {
          return userData['anonymousHash'] ??
              _generateFallbackAnonymousHash(userId);
        } else {
          return userData['publicHash'] ?? _generateFallbackPublicHash(userId);
        }
      }

      // 如果用戶文檔不存在，生成備用 hash
      return isAnonymous
          ? _generateFallbackAnonymousHash(userId)
          : _generateFallbackPublicHash(userId);
    } catch (e) {
      Logger.w('獲取用戶 Hash 失敗: $e');
      // 發生錯誤時使用備用 hash
      return isAnonymous
          ? _generateFallbackAnonymousHash(userId)
          : _generateFallbackPublicHash(userId);
    }
  }

  /// 生成備用的公開 hash
  String _generateFallbackPublicHash(String userId) {
    final publicSeed = '$userId-public-v1';
    final hash = publicSeed.hashCode.toRadixString(16).toUpperCase();
    return 'PUB_$hash';
  }

  /// 生成備用的匿名 hash
  String _generateFallbackAnonymousHash(String userId) {
    final anonymousSeed = '$userId-anonymous-v1';
    final hash = anonymousSeed.hashCode.toRadixString(16).toUpperCase();
    return 'ANON_$hash';
  }

  /// 生成假帳號 hash
  String _generateFakeAccountHash(String fakeAccountId) {
    final fakeSeed = '$fakeAccountId-fake-v1';
    final hash = fakeSeed.hashCode.toRadixString(16).toUpperCase();
    return 'FAKE_$hash';
  }

  // ========================================
  // 📍 位置相關操作
  // ========================================

  @override
  Future<PostLocation> getCurrentLocation() async {
    try {
      // 檢查位置權限
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        throw Exception('location_permission_denied');
      }

      // 獲取當前位置
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      // 反向地理編碼
      final geoTags = await reverseGeocode(
        position.latitude,
        position.longitude,
      );

      return PostLocation(
        latitude: position.latitude,
        longitude: position.longitude,
        fuzzyLatitude: position.latitude,
        fuzzyLongitude: position.longitude,
        geoTags: geoTags,
      );
    } catch (error) {
      throw Exception('get_location_failed: $error');
    }
  }

  @override
  Future<List<String>> reverseGeocode(double lat, double lng) async {
    try {
      // 先測試新版本的原生geocoding是否改善了多語系支援
      Logger.d('🧪 測試新版本geocoding 4.0.0的多語系支援', 'HomeRepository');
      final nativeResult = await _reverseGeocodeWithNativeGeocoding(lat, lng);

      if (nativeResult.isNotEmpty) {
        Logger.d('✅ 新版本原生geocoding成功獲取地址', 'HomeRepository');
        return nativeResult;
      }

      // 如果原生geocoding失敗，嘗試Google Maps API
      Logger.w('⚠️ 原生geocoding失敗，嘗試Google Maps API', 'HomeRepository');
      final googleResult = await _reverseGeocodeWithGoogleMaps(lat, lng);
      if (googleResult.isNotEmpty) {
        Logger.d('✅ Google Maps API獲取地址成功', 'HomeRepository');
        return googleResult;
      }

      Logger.w('⚠️ 所有地址反轉方法都失敗了', 'HomeRepository');
      return [];
    } catch (error) {
      Logger.e('Reverse geocoding failed', 'LOCATION', error);
      return [];
    }
  }

  @override
  Future<GeocodingResult> reverseGeocodeWithCountry(
      double lat, double lng) async {
    try {
      Logger.d('🌍 開始擴展地址反查，獲取地理標籤和國家資訊', 'HomeRepository');

      // 1. 獲取使用者語系的地理標籤
      final geoTags = await reverseGeocode(lat, lng);

      // 2. 獲取國家資訊（優先使用快取）
      String? countryNameEn;
      String? countryNameLocal;
      String? countryCode;

      // 嘗試從位置國家快取獲取資訊
      try {
        final countryInfo =
            await LocationCountryCache.updateCountryFromPosition(lat, lng);
        if (countryInfo != null) {
          countryCode = countryInfo['countryCode'];
          countryNameEn = countryInfo['countryName'];
          Logger.d(
              '🌍 從快取獲取國家資訊: $countryNameEn ($countryCode)', 'HomeRepository');
        }
      } catch (error) {
        Logger.w('⚠️ 從快取獲取國家資訊失敗，嘗試其他方法: $error', 'HomeRepository');
      }

      // 如果快取失敗，嘗試使用Google Maps API獲取英文國家名稱
      if (countryNameEn == null && ApiConfig.isGoogleMapsApiKeyConfigured) {
        final englishCountryResult =
            await _reverseGeocodeWithGoogleMapsForCountry(lat, lng, 'en');
        countryNameEn = englishCountryResult;
        Logger.d('🌍 英文國家名稱: $countryNameEn', 'HomeRepository');
      }

      // 如果Google Maps API失敗，嘗試使用原生geocoding獲取英文國家名稱
      if (countryNameEn == null) {
        final englishCountryResult =
            await _reverseGeocodeWithNativeGeocodingForCountry(
                lat, lng, 'en_US');
        countryNameEn = englishCountryResult;
        Logger.d('🌍 原生geocoding英文國家名稱: $countryNameEn', 'HomeRepository');
      }

      // 從現有的地理標籤中提取本地語系的國家名稱（通常是第一個）
      if (geoTags.isNotEmpty) {
        countryNameLocal = geoTags.first;
        Logger.d('🌍 本地語系國家名稱: $countryNameLocal', 'HomeRepository');
      }

      final hasCountryInfo = countryNameEn != null || countryNameLocal != null;
      final result = GeocodingResult(
        geoTags: geoTags,
        countryNameEn: countryNameEn,
        countryNameLocal: countryNameLocal,
        countryCode: countryCode,
        hasCountryInfo: hasCountryInfo,
      );

      Logger.d('✅ 擴展地址反查完成: ${result.effectiveCountryName}', 'HomeRepository');
      return result;
    } catch (error) {
      Logger.e('擴展地址反查失敗: $error', 'HomeRepository');
      return const GeocodingResult();
    }
  }

  /// 使用Google Maps Geocoding API進行地址反轉（支援多語系）
  Future<List<String>> _reverseGeocodeWithGoogleMaps(
      double lat, double lng) async {
    try {
      // 檢查API Key是否已設定
      if (!ApiConfig.isGoogleMapsApiKeyConfigured) {
        Logger.w(
            '⚠️ Google Maps API Key未設定，跳過Google Maps API', 'HomeRepository');
        return [];
      }

      // 獲取當前語系設定
      final currentLocale = ui.PlatformDispatcher.instance.locale;
      final languageCode = ApiConfig.getLanguageCode(
        currentLocale.languageCode,
        currentLocale.countryCode,
      );

      Logger.d('🌐 使用Google Maps API，語系: $languageCode', 'HomeRepository');

      // 構建Google Maps Geocoding API請求
      final url = ApiConfig.buildGeocodingUrl(lat, lng, languageCode);

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK' &&
            data['results'] != null &&
            data['results'].isNotEmpty) {
          final result = data['results'][0];
          final addressComponents = result['address_components'] as List;

          final geoTags = <String>[];

          // 按照重要性順序提取地址組件
          for (final component in addressComponents) {
            final types = component['types'] as List;
            final longName = component['long_name'] as String;

            if (types.contains('country')) {
              geoTags.add(longName);
            } else if (types.contains('administrative_area_level_1')) {
              geoTags.add(longName);
            } else if (types.contains('administrative_area_level_2')) {
              geoTags.add(longName);
            } else if (types.contains('locality')) {
              geoTags.add(longName);
            } else if (types.contains('sublocality') ||
                types.contains('sublocality_level_1')) {
              geoTags.add(longName);
            }
          }

          // 去重並保持順序
          final uniqueGeoTags = <String>[];
          for (final tag in geoTags) {
            if (!uniqueGeoTags.contains(tag)) {
              uniqueGeoTags.add(tag);
            }
          }

          Logger.d(
              '🏷️ Google Maps API地址反轉結果: $uniqueGeoTags', 'HomeRepository');
          return uniqueGeoTags;
        }
      }

      Logger.w(
          '⚠️ Google Maps API返回錯誤: ${response.statusCode}', 'HomeRepository');
      return [];
    } catch (error) {
      Logger.e('Google Maps geocoding failed: $error', 'HomeRepository');
      return [];
    }
  }

  /// 使用原生geocoding進行地址反轉（新版本4.0.0）
  Future<List<String>> _reverseGeocodeWithNativeGeocoding(
      double lat, double lng) async {
    try {
      // 獲取當前語系設定
      final currentLocale = ui.PlatformDispatcher.instance.locale;
      final localeIdentifier =
          '${currentLocale.languageCode}_${currentLocale.countryCode ?? currentLocale.languageCode.toUpperCase()}';

      Logger.d(
          '🌐 使用原生geocoding 4.0.0，語系: $localeIdentifier', 'HomeRepository');

      // 新版本API：先設定locale，然後調用placemarkFromCoordinates
      await setLocaleIdentifier(localeIdentifier);

      final placemarks = await placemarkFromCoordinates(lat, lng);

      if (placemarks.isEmpty) {
        Logger.w('⚠️ 原生geocoding未返回結果', 'HomeRepository');
        return [];
      }

      final placemark = placemarks.first;
      final geoTags = <String>[];

      // 按照層級添加地理標籤
      if (placemark.country != null && placemark.country!.isNotEmpty) {
        geoTags.add(placemark.country!);
        Logger.d('🏷️ 國家: ${placemark.country}', 'HomeRepository');
      }
      if (placemark.administrativeArea != null &&
          placemark.administrativeArea!.isNotEmpty) {
        geoTags.add(placemark.administrativeArea!);
        Logger.d('🏷️ 行政區域: ${placemark.administrativeArea}', 'HomeRepository');
      }
      if (placemark.locality != null && placemark.locality!.isNotEmpty) {
        geoTags.add(placemark.locality!);
        Logger.d('🏷️ 城市: ${placemark.locality}', 'HomeRepository');
      }
      if (placemark.subLocality != null && placemark.subLocality!.isNotEmpty) {
        geoTags.add(placemark.subLocality!);
        Logger.d('🏷️ 子區域: ${placemark.subLocality}', 'HomeRepository');
      }
      if (placemark.thoroughfare != null &&
          placemark.thoroughfare!.isNotEmpty) {
        geoTags.add(placemark.thoroughfare!);
        Logger.d('🏷️ 街道: ${placemark.thoroughfare}', 'HomeRepository');
      }

      Logger.d('🏷️ 原生geocoding 4.0.0地址反轉結果: $geoTags', 'HomeRepository');

      // 檢查結果是否包含中文字符（簡單檢測多語系是否生效）
      final hasNonEnglish = geoTags.any((tag) =>
          RegExp(r'[\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff]').hasMatch(tag));

      if (hasNonEnglish) {
        Logger.d('✅ 檢測到非英文字符，多語系可能生效', 'HomeRepository');
      } else {
        Logger.w('⚠️ 未檢測到非英文字符，可能仍為英文結果', 'HomeRepository');
      }

      return geoTags;
    } catch (error) {
      Logger.e('Native geocoding 4.0.0 failed: $error', 'HomeRepository');
      return [];
    }
  }

  /// 使用Google Maps API專門獲取國家名稱
  Future<String?> _reverseGeocodeWithGoogleMapsForCountry(
      double lat, double lng, String languageCode) async {
    try {
      // 檢查API Key是否已設定
      if (!ApiConfig.isGoogleMapsApiKeyConfigured) {
        Logger.w(
            '⚠️ Google Maps API Key未設定，跳過Google Maps API', 'HomeRepository');
        return null;
      }

      Logger.d(
          '🌐 使用Google Maps API獲取國家名稱，語系: $languageCode', 'HomeRepository');

      // 構建Google Maps Geocoding API請求
      final url = ApiConfig.buildGeocodingUrl(lat, lng, languageCode);

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK' &&
            data['results'] != null &&
            data['results'].isNotEmpty) {
          final result = data['results'][0];
          final addressComponents = result['address_components'] as List;

          // 只查找國家組件
          for (final component in addressComponents) {
            final types = component['types'] as List;
            if (types.contains('country')) {
              final countryName = component['long_name'] as String;
              Logger.d(
                  '🏷️ Google Maps API找到國家: $countryName', 'HomeRepository');
              return countryName;
            }
          }
        }
      }

      Logger.w('⚠️ Google Maps API未找到國家資訊', 'HomeRepository');
      return null;
    } catch (error) {
      Logger.e(
          'Google Maps geocoding for country failed: $error', 'HomeRepository');
      return null;
    }
  }

  /// 使用原生geocoding專門獲取國家名稱
  Future<String?> _reverseGeocodeWithNativeGeocodingForCountry(
      double lat, double lng, String localeIdentifier) async {
    try {
      Logger.d(
          '🌐 使用原生geocoding獲取國家名稱，語系: $localeIdentifier', 'HomeRepository');

      // 設定locale並調用placemarkFromCoordinates
      await setLocaleIdentifier(localeIdentifier);
      final placemarks = await placemarkFromCoordinates(lat, lng);

      if (placemarks.isEmpty) {
        Logger.w('⚠️ 原生geocoding未返回結果', 'HomeRepository');
        return null;
      }

      final placemark = placemarks.first;
      if (placemark.country != null && placemark.country!.isNotEmpty) {
        Logger.d('🏷️ 原生geocoding找到國家: ${placemark.country}', 'HomeRepository');
        return placemark.country!;
      }

      Logger.w('⚠️ 原生geocoding未找到國家資訊', 'HomeRepository');
      return null;
    } catch (error) {
      Logger.e('Native geocoding for country failed: $error', 'HomeRepository');
      return null;
    }
  }

  @override
  Future<List<PlaceResult>> searchPlaces(String query) async {
    try {
      final locations = await locationFromAddress(query);
      if (locations.isEmpty) return [];

      final results = <PlaceResult>[];

      for (final location in locations) {
        // 獲取詳細地址信息
        final placemarks = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        );

        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          final address = _formatAddress(placemark);

          results.add(PlaceResult(
            name: placemark.name?.isNotEmpty == true
                ? placemark.name!
                : placemark.locality ?? query,
            address: address,
            latitude: location.latitude,
            longitude: location.longitude,
            type: _determinePlaceType(placemark),
            placeId: '${location.latitude}_${location.longitude}',
            formattedAddress: address,
          ));
        }
      }

      return results;
    } catch (error) {
      Logger.e('Place search failed', 'LOCATION', error);
      return [];
    }
  }

  /// 格式化地址
  String _formatAddress(Placemark placemark) {
    final parts = <String>[];

    if (placemark.street != null && placemark.street!.isNotEmpty) {
      parts.add(placemark.street!);
    }
    if (placemark.subLocality != null && placemark.subLocality!.isNotEmpty) {
      parts.add(placemark.subLocality!);
    }
    if (placemark.locality != null && placemark.locality!.isNotEmpty) {
      parts.add(placemark.locality!);
    }
    if (placemark.administrativeArea != null &&
        placemark.administrativeArea!.isNotEmpty) {
      parts.add(placemark.administrativeArea!);
    }
    if (placemark.country != null && placemark.country!.isNotEmpty) {
      parts.add(placemark.country!);
    }

    return parts.join(', ');
  }

  /// 判斷地點類型
  String _determinePlaceType(Placemark placemark) {
    if (placemark.name?.isNotEmpty == true &&
        placemark.name != placemark.locality) {
      return 'establishment';
    } else if (placemark.street?.isNotEmpty == true) {
      return 'street_address';
    } else if (placemark.locality?.isNotEmpty == true) {
      return 'locality';
    } else if (placemark.administrativeArea?.isNotEmpty == true) {
      return 'administrative_area_level_1';
    } else {
      return 'geocode';
    }
  }

  @override
  Future<PostLocation> generateFuzzyLocation({
    required double lat,
    required double lng,
    required int privacyLevel,
    String? userId,
  }) async {
    try {
      // 根據隱私等級決定偏移距離
      final offsetDistance = _getPrivacyOffset(privacyLevel);

      // 生成偏移
      final fuzzyCoords = _addLocationOffset(lat, lng, offsetDistance, userId);

      // 獲取地理標籤
      final geoTags = await reverseGeocode(lat, lng);

      return PostLocation(
        latitude: lat,
        longitude: lng,
        fuzzyLatitude: fuzzyCoords.latitude,
        fuzzyLongitude: fuzzyCoords.longitude,
        geoTags: geoTags,
      );
    } catch (error) {
      throw Exception('generate_fuzzy_location_failed: $error');
    }
  }

  /// 根據隱私等級獲取偏移距離
  double _getPrivacyOffset(int privacyLevel) {
    switch (privacyLevel) {
      case 1:
        return 50.0 + (math.Random().nextDouble() * 15.0); // 50-65m
      case 3:
        return 125.0 + (math.Random().nextDouble() * 50.0); // 125-175m
      case 2:
      default:
        return 75.0 + (math.Random().nextDouble() * 25.0); // 75-100m
    }
  }

  /// 添加位置偏移
  ({double latitude, double longitude}) _addLocationOffset(
    double lat,
    double lng,
    double offsetMeters,
    String? userId,
  ) {
    // 使用用戶ID作為種子，確保短時間內位置相對穩定
    final seed = userId?.hashCode ?? DateTime.now().millisecondsSinceEpoch;
    final random = math.Random(seed);

    // 生成隨機角度
    final angle = random.nextDouble() * 2 * math.pi;

    // 地球半徑（公尺）
    const earthRadius = 6371000.0;

    // 計算偏移
    final deltaLat = (offsetMeters * math.cos(angle)) / earthRadius;
    final deltaLng = (offsetMeters * math.sin(angle)) /
        (earthRadius * math.cos(lat * math.pi / 180));

    final newLat = lat + (deltaLat * 180 / math.pi);
    final newLng = lng + (deltaLng * 180 / math.pi);

    return (latitude: newLat, longitude: newLng);
  }

  // ========================================
  // 📝 貼文查詢操作
  // ========================================

  @override
  Future<List<Post>> getNearbyPosts(LocationQuery query) async {
    try {
      Logger.d(
          '開始查詢地圖可視範圍內貼文: lat=${query.latitude}, lng=${query.longitude}, radius=${query.radiusMeters}',
          'HomeRepository');

      // 先嘗試 GeoHash 查詢，如果失敗則降級到邊界框查詢
      try {
        final geoHashPosts = await _getNearbyPostsWithGeoHash(query);
        if (geoHashPosts.isNotEmpty) {
          Logger.d(
              'GeoHash 查詢成功，返回 ${geoHashPosts.length} 個貼文', 'HomeRepository');
          return geoHashPosts;
        }
        Logger.w('GeoHash 查詢返回0個結果，嘗試降級到邊界框查詢', 'HomeRepository');
      } catch (e) {
        Logger.w('GeoHash 查詢失敗: $e，降級到邊界框查詢', 'HomeRepository');
      }

      // 降級方案：使用邊界框查詢
      return await _getNearbyPostsWithBounds(query);
    } catch (error) {
      Logger.e('查詢附近貼文失敗: $error', 'HomeRepository');
      throw Exception('get_nearby_posts_failed: $error');
    }
  }

  /// 使用 GeoHash 查詢附近貼文（優化版）
  Future<List<Post>> _getNearbyPostsWithGeoHash(LocationQuery query) async {
    final centerGeoHash =
        GeoHash.encode(query.latitude, query.longitude, precision: 5);
    final geoHashPrefixes =
        _getGeoHashPrefixes(centerGeoHash, query.radiusMeters);

    Logger.d('🔍 使用 GeoHash 查詢，中心點: $centerGeoHash, 前綴: $geoHashPrefixes',
        'HomeRepository');

    final allPosts = <Post>[];
    final processedPostIds = <String>{};

    // 🔧 優化：動態調整每個前綴的查詢數量
    final targetTotal = query.limit * 2; // 目標總數（考慮距離過濾）
    final perPrefixLimit =
        (targetTotal / geoHashPrefixes.length).ceil().clamp(10, 30);

    Logger.d(
        '🔍 查詢策略: 目標總數=$targetTotal, 每前綴限制=$perPrefixLimit', 'HomeRepository');

    // 對每個 GeoHash 前綴進行查詢
    for (final prefix in geoHashPrefixes) {
      // 🔧 早期退出：如果已經有足夠的結果
      if (allPosts.length >= query.limit) {
        Logger.d('🔍 已獲得足夠結果 (${allPosts.length})，跳過剩餘前綴', 'HomeRepository');
        break;
      }

      try {
        var prefixQuery = _postsCollection
            .where('is_deleted', isEqualTo: false)
            .where('location.geo_hash', isGreaterThanOrEqualTo: prefix)
            .where('location.geo_hash', isLessThan: '${prefix}z');

        // 添加國家過濾
        if (query.countryFilter != null) {
          prefixQuery =
              prefixQuery.where('country', isEqualTo: query.countryFilter);
        }

        prefixQuery = prefixQuery
            .orderBy('location.geo_hash')
            .orderBy('created_at', descending: true)
            .limit(perPrefixLimit); // 🔧 使用動態限制

        final snapshot = await prefixQuery.get();
        Logger.d('🔍 GeoHash 前綴 $prefix: ${snapshot.docs.length} 個文檔',
            'HomeRepository');

        final posts = snapshot.docs
            .map((doc) {
              try {
                return PostModel.fromFirestoreQuery(doc).toDomain();
              } catch (e) {
                Logger.e('解析貼文文檔失敗: ${doc.id}, 錯誤: $e', 'HomeRepository');
                return null;
              }
            })
            .where((post) => post != null)
            .cast<Post>()
            .where((post) {
              // 🔧 去重檢查
              if (processedPostIds.contains(post.postId)) {
                return false;
              }
              processedPostIds.add(post.postId);

              // 🔧 距離過濾（移除詳細日誌）
              final distance =
                  post.distanceToLocation(query.latitude, query.longitude);
              return distance <= query.radiusMeters;
            })
            .toList();

        allPosts.addAll(posts);
        Logger.d('🔍 前綴 $prefix 有效結果: ${posts.length} 個', 'HomeRepository');
      } catch (e) {
        Logger.w('GeoHash 前綴 $prefix 查詢失敗: $e', 'HomeRepository');
      }
    }

    // 排序並限制數量
    final finalPosts = allPosts
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final result = finalPosts.take(query.limit).toList();
    Logger.d('🔍 GeoHash 查詢完成: ${result.length} 個最終結果', 'HomeRepository');

    return result;
  }

  /// 使用邊界框查詢附近貼文（降級方案）
  Future<List<Post>> _getNearbyPostsWithBounds(LocationQuery query) async {
    Logger.d('使用邊界框查詢作為降級方案', 'HomeRepository');

    // 計算地圖可視範圍的邊界
    final bounds = _calculateMapBounds(
        query.latitude, query.longitude, query.radiusMeters);

    Logger.d(
        '地圖邊界: 北=${bounds['north']}, 南=${bounds['south']}, 東=${bounds['east']}, 西=${bounds['west']}',
        'HomeRepository');

    try {
      // 使用緯度範圍查詢
      var baseQuery = _postsCollection
          .where('is_deleted', isEqualTo: false)
          .where('location.fuzzy_lat', isGreaterThanOrEqualTo: bounds['south'])
          .where('location.fuzzy_lat', isLessThanOrEqualTo: bounds['north']);

      // 添加國家過濾
      if (query.countryFilter != null) {
        baseQuery = baseQuery.where('country', isEqualTo: query.countryFilter);
      }

      baseQuery = baseQuery
          .orderBy('location.fuzzy_lat')
          .orderBy('created_at', descending: true)
          .limit(query.limit * 3); // 多查一些，因為還要過濾經度

      final snapshot = await baseQuery.get();
      Logger.d('邊界框查詢完成，獲取到 ${snapshot.docs.length} 個文檔', 'HomeRepository');

      final posts = snapshot.docs
          .map((doc) {
            try {
              return PostModel.fromFirestoreQuery(doc).toDomain();
            } catch (e) {
              Logger.e('解析貼文文檔失敗: ${doc.id}, 錯誤: $e', 'HomeRepository');
              return null;
            }
          })
          .where((post) => post != null)
          .cast<Post>()
          .where((post) {
            // 過濾經度範圍
            final lng = post.location.fuzzyLongitude;
            final lat = post.location.fuzzyLatitude;
            final inBounds = lat >= bounds['south']! &&
                lat <= bounds['north']! &&
                lng >= bounds['west']! &&
                lng <= bounds['east']!;

            if (inBounds) {
              // 🔧 精確距離過濾（移除詳細日誌）
              final distance =
                  post.distanceToLocation(query.latitude, query.longitude);
              return distance <= query.radiusMeters;
            }
            return false;
          })
          .take(query.limit)
          .toList();

      Logger.d('邊界框過濾完成，最終返回 ${posts.length} 個貼文', 'HomeRepository');
      return posts;
    } catch (e) {
      Logger.e('邊界框查詢失敗: $e', 'HomeRepository');

      // 最後的降級方案：簡單的時間查詢
      Logger.d('使用最簡單的時間查詢作為最後降級方案', 'HomeRepository');
      var simpleQuery = _postsCollection.where('is_deleted', isEqualTo: false);

      // 添加國家過濾
      if (query.countryFilter != null) {
        simpleQuery =
            simpleQuery.where('country', isEqualTo: query.countryFilter);
      }

      simpleQuery =
          simpleQuery.orderBy('created_at', descending: true).limit(100);

      final snapshot = await simpleQuery.get();
      Logger.d('簡單查詢完成，獲取到 ${snapshot.docs.length} 個文檔', 'HomeRepository');

      final posts = snapshot.docs
          .map((doc) {
            try {
              return PostModel.fromFirestoreQuery(doc).toDomain();
            } catch (e) {
              Logger.e('解析貼文文檔失敗: ${doc.id}, 錯誤: $e', 'HomeRepository');
              return null;
            }
          })
          .where((post) => post != null)
          .cast<Post>()
          .where((post) {
            final distance =
                post.distanceToLocation(query.latitude, query.longitude);
            return distance <= query.radiusMeters;
          })
          .take(query.limit)
          .toList();

      Logger.d('簡單查詢過濾完成，最終返回 ${posts.length} 個貼文', 'HomeRepository');
      return posts;
    }
  }

  /// 確保位置包含 GeoHash
  PostLocation _ensureLocationHasGeoHash(PostLocation location) {
    // 如果已經有 GeoHash，直接返回
    if (location.geoHash != null && location.geoHash!.isNotEmpty) {
      return location;
    }

    // 生成 GeoHash（使用模糊位置以保護隱私）
    final geoHash = GeoHash.encode(
        location.fuzzyLatitude, location.fuzzyLongitude,
        precision: 8 // 使用8位精度，約9.5m x 19m
        );

    Logger.d(
        '生成 GeoHash: $geoHash (lat: ${location.fuzzyLatitude}, lng: ${location.fuzzyLongitude})',
        'HomeRepository');

    // 返回包含 GeoHash 的新位置
    return location.copyWith(geoHash: geoHash);
  }

  /// 🚀 優化的 GeoHash 前綴列表 - 減少 Firestore 查詢次數
  List<String> _getGeoHashPrefixes(String centerHash, double radiusMeters) {
    // 🎯 根據半徑智慧選擇精度，平衡精確度與查詢數量
    int precision;
    int maxPrefixes;

    if (radiusMeters <= 500) {
      // 小範圍：高精度，少查詢
      precision = 6;
      maxPrefixes = 2;
    } else if (radiusMeters <= 2000) {
      // 中等範圍：平衡精度
      precision = 5;
      maxPrefixes = 3;
    } else if (radiusMeters <= 10000) {
      // 大範圍：低精度，控制查詢數
      precision = 4;
      maxPrefixes = 4;
    } else {
      // 超大範圍：最低精度
      precision = 3;
      maxPrefixes = 2;
    }

    final decoded = GeoHash.decode(centerHash);
    final centerLat = decoded['latitude']!;
    final centerLng = decoded['longitude']!;

    // 🔧 獲取邊界框 hashes 並限制數量
    final allPrefixes = GeoHash.getBoundingBoxHashes(
        centerLat, centerLng, radiusMeters,
        precision: precision);

    // 📊 限制最大前綴數量以控制 Firestore 查詢成本
    final limitedPrefixes = allPrefixes.take(maxPrefixes).toList();

    Logger.d(
        '🔍 GeoHash 優化: 半徑${radiusMeters}m, 精度$precision, 前綴數量${limitedPrefixes.length}/${allPrefixes.length}',
        'HomeRepository');

    return limitedPrefixes;
  }

  /// 計算地圖可視範圍邊界
  Map<String, double> _calculateMapBounds(
      double centerLat, double centerLng, double radiusMeters) {
    // 將半徑從米轉換為度數（粗略計算）
    final latDelta = radiusMeters / 111000.0; // 1度緯度約111km
    final lngDelta =
        radiusMeters / (111000.0 * math.cos(centerLat * math.pi / 180));

    return {
      'north': centerLat + latDelta,
      'south': centerLat - latDelta,
      'east': centerLng + lngDelta,
      'west': centerLng - lngDelta,
    };
  }

  Future<Post?> getPost(String postId) async {
    try {
      final snapshot = await _postsCollection.doc(postId).get();
      if (!snapshot.exists) return null;
      return PostModel.fromFirestore(snapshot).toDomain();
    } catch (error) {
      throw Exception('get_post_failed: $error');
    }
  }

  @override
  Future<Post?> getPostById(String postId) async {
    try {
      // 先檢查一般貼文
      final postDoc = await _postsCollection.doc(postId).get();
      if (postDoc.exists) {
        return PostModel.fromFirestore(postDoc).toDomain();
      }

      // 再檢查廣播
      final broadcastDoc = await _broadcastsCollection.doc(postId).get();
      if (broadcastDoc.exists) {
        return PostModel.fromFirestore(broadcastDoc).toDomain();
      }

      return null;
    } catch (error) {
      throw Exception('get_post_by_id_failed: $error');
    }
  }

  @override
  Future<Post?> getPostByHash(String postHash) async {
    try {
      // 查詢一般貼文
      final postQuery = await _postsCollection
          .where('post_hash', isEqualTo: postHash)
          .limit(1)
          .get();

      if (postQuery.docs.isNotEmpty) {
        return PostModel.fromFirestoreQuery(postQuery.docs.first).toDomain();
      }

      // 查詢廣播
      final broadcastQuery = await _broadcastsCollection
          .where('post_hash', isEqualTo: postHash)
          .limit(1)
          .get();

      if (broadcastQuery.docs.isNotEmpty) {
        return PostModel.fromFirestoreQuery(broadcastQuery.docs.first)
            .toDomain();
      }

      return null;
    } catch (error) {
      throw Exception('get_post_by_hash_failed: $error');
    }
  }

  @override
  Future<List<Post>> searchPostsByHashtag({
    required String hashtag,
    LocationQuery? locationFilter,
  }) async {
    try {
      Logger.d('搜尋標籤: "$hashtag"', 'HomeRepository');

      // 自動為標籤添加 # 符號（如果沒有的話）
      String searchTag = hashtag.trim();
      if (searchTag.isEmpty) {
        Logger.d('搜尋標籤為空，返回空結果', 'HomeRepository');
        return [];
      }

      // 如果用戶輸入沒有 #，自動添加
      if (!searchTag.startsWith('#')) {
        searchTag = '#$searchTag';
      }

      Logger.d('處理後的搜尋標籤: "$searchTag"', 'HomeRepository');

      // 使用精確匹配進行搜尋（成本最低）
      Query query = _postsCollection
          .where('hashtags', arrayContains: searchTag)
          .where('is_deleted', isEqualTo: false)
          .orderBy('created_at', descending: true)
          .limit(locationFilter?.limit ?? 50);

      final querySnapshot = await query.get();
      Logger.d('標籤搜尋查詢結果: ${querySnapshot.docs.length} 個文檔', 'HomeRepository');

      final posts = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data() as Map<String, dynamic>;
              Logger.d(
                  '文檔 ${doc.id} 的標籤: ${data['hashtags']}', 'HomeRepository');
              return PostModel.fromFirestoreQuery(doc).toDomain();
            } catch (e) {
              Logger.e('解析文檔 ${doc.id} 失敗: $e', 'HomeRepository');
              return null;
            }
          })
          .where((post) => post != null)
          .cast<Post>()
          .toList();

      Logger.d('最終返回 ${posts.length} 個標籤搜尋結果', 'HomeRepository');
      return posts;
    } catch (error) {
      throw Exception('search_posts_by_hashtag_failed: $error');
    }
  }

  @override
  Future<List<Post>> searchPostsByGeoTag({
    required String geoTag,
    LocationQuery? locationFilter,
  }) async {
    try {
      Query query = _postsCollection
          .where('location.geo_tags', arrayContains: geoTag)
          .where('is_deleted', isEqualTo: false)
          .orderBy('created_at', descending: true)
          .limit(locationFilter?.limit ?? 50);

      final querySnapshot = await query.get();
      final posts = querySnapshot.docs
          .map((doc) => PostModel.fromFirestoreQuery(doc).toDomain())
          .toList();

      // 搜尋頁面不進行位置過濾
      // if (locationFilter != null) {
      //   return posts.where((post) {
      //     final distance = post.distanceToLocation(
      //       locationFilter.latitude,
      //       locationFilter.longitude,
      //     );
      //     return distance <= locationFilter.radiusMeters;
      //   }).toList();
      // }

      return posts;
    } catch (error) {
      throw Exception('search_posts_by_geo_tag_failed: $error');
    }
  }

  @override
  Future<List<Post>> getPostsByCountry({
    required String country,
    PostSortBy sortBy = PostSortBy.newest,
    int limit = 100,
    String? lastPostId,
    List<String>? hashtags,
    List<String>? geoTags,
    double? userLatitude,
    double? userLongitude,
  }) async {
    try {
      Logger.d('開始查詢國家貼文: country=$country, sortBy=$sortBy, limit=$limit',
          'HomeRepository');

      Query query = _postsCollection
          .where('is_deleted', isEqualTo: false)
          .where('country', isEqualTo: country);

      // 添加標籤過濾
      if (hashtags != null && hashtags.isNotEmpty) {
        query = query.where('hashtags', arrayContainsAny: hashtags);
      }

      // 添加地理標籤過濾
      if (geoTags != null && geoTags.isNotEmpty) {
        query = query.where('location.geo_tags', arrayContainsAny: geoTags);
      }

      // 添加排序
      switch (sortBy) {
        case PostSortBy.newest:
          query = query.orderBy('created_at', descending: true);
          break;
        case PostSortBy.oldest:
          query = query.orderBy('created_at', descending: false);
          break;
        case PostSortBy.distance:
          // 距離排序在客戶端進行，這裡先按創建時間排序獲取數據
          query = query.orderBy('created_at', descending: true);
          break;
        case PostSortBy.popular:
          // 暫時使用 created_at 排序，避免索引問題
          query = query.orderBy('created_at', descending: true);
          break;
        case PostSortBy.engagement:
          // 暫時使用 created_at 排序，避免索引問題
          query = query.orderBy('created_at', descending: true);
          break;
        case PostSortBy.viewCount:
          // 暫時使用 created_at 排序，避免索引問題
          query = query.orderBy('created_at', descending: true);
          break;
      }

      // 🚨 TODO: 優化分頁實現 - 當前每次分頁都需要額外的 Firestore 讀取
      // 建議改為傳遞 lastCreatedAt 時間戳而非 lastPostId，避免額外查詢
      if (lastPostId != null) {
        final lastDoc = await _postsCollection.doc(lastPostId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();
      // 🔍 記錄查詢次數
      FirestoreQueryCounter.incrementRead('getPostsByCountry',
          count: querySnapshot.docs.length);

      var posts = querySnapshot.docs
          .map((doc) => PostModel.fromFirestoreQuery(doc).toDomain())
          .toList();

      // 如果是距離排序且有用戶位置，在客戶端進行距離排序
      if (sortBy == PostSortBy.distance &&
          userLatitude != null &&
          userLongitude != null) {
        Logger.d(
            '執行距離排序，用戶位置: ($userLatitude, $userLongitude)', 'HomeRepository');

        posts.sort((a, b) {
          final distanceA = a.distanceToLocation(userLatitude, userLongitude);
          final distanceB = b.distanceToLocation(userLatitude, userLongitude);

          // 添加調試日誌
          if (posts.indexOf(a) < 3 || posts.indexOf(b) < 3) {
            Logger.d(
                '距離比較: 貼文A距離=${distanceA.toStringAsFixed(0)}m, 貼文B距離=${distanceB.toStringAsFixed(0)}m',
                'HomeRepository');
          }

          return distanceA.compareTo(distanceB);
        });

        Logger.d(
            '距離排序完成，前3個貼文距離: ${posts.take(3).map((p) => p.distanceToLocation(userLatitude, userLongitude).toStringAsFixed(0)).join(', ')}m',
            'HomeRepository');
      } else {
        Logger.d(
            '跳過距離排序: sortBy=$sortBy, userLatitude=$userLatitude, userLongitude=$userLongitude',
            'HomeRepository');
      }

      Logger.d('國家貼文查詢完成，找到 ${posts.length} 個貼文', 'HomeRepository');

      return posts;
    } catch (error) {
      Logger.e('查詢國家貼文失敗: $error', 'HomeRepository');
      throw Exception('get_posts_by_country_failed: $error');
    }
  }

  @override
  Future<List<Post>> searchAllPosts({
    String? keyword,
    List<String>? hashtags,
    List<String>? geoTags,
    PostSortBy sortBy = PostSortBy.newest,
    int limit = 100,
    String? lastPostId,
    String? countryFilter, // 添加國家過濾參數
  }) async {
    try {
      Logger.d(
          '開始全域搜尋: keyword=$keyword, hashtags=$hashtags, geoTags=$geoTags, sortBy=$sortBy, limit=$limit, countryFilter=$countryFilter',
          'HomeRepository');

      Query query = _postsCollection.where('is_deleted', isEqualTo: false);

      // 添加國家過濾 - 如果指定了國家，則只搜尋該國的貼文
      if (countryFilter != null && countryFilter.isNotEmpty) {
        query = query.where('country', isEqualTo: countryFilter);
        Logger.d('🔍 應用國家過濾: "$countryFilter"', 'HomeRepository');
        Logger.d('🔍 查詢條件：country == "$countryFilter"', 'HomeRepository');
      }

      // 添加標籤過濾
      if (hashtags != null && hashtags.isNotEmpty) {
        query = query.where('hashtags', arrayContainsAny: hashtags);
      }

      // 添加地理標籤過濾
      if (geoTags != null && geoTags.isNotEmpty) {
        query = query.where('location.geo_tags', arrayContainsAny: geoTags);
      }

      // 添加排序 - 使用簡單的排序避免索引問題
      switch (sortBy) {
        case PostSortBy.newest:
          // 使用 created_at 排序，這個索引通常已存在
          query = query.orderBy('created_at', descending: true);
          break;
        case PostSortBy.oldest:
          query = query.orderBy('created_at', descending: false);
          break;
        case PostSortBy.distance:
          // 距離排序在客戶端進行，這裡先按創建時間排序獲取數據
          query = query.orderBy('created_at', descending: true);
          break;
        case PostSortBy.popular:
          // 暫時使用 created_at 排序，避免索引問題
          query = query.orderBy('created_at', descending: true);
          break;
        case PostSortBy.engagement:
          // 暫時使用 created_at 排序，避免索引問題
          query = query.orderBy('created_at', descending: true);
          break;
        case PostSortBy.viewCount:
          // 暫時使用 created_at 排序，避免索引問題
          query = query.orderBy('created_at', descending: true);
          break;
      }

      // 添加分頁
      if (lastPostId != null) {
        final lastDoc = await _postsCollection.doc(lastPostId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();

      Logger.d('🔍 查詢結果：取得 ${querySnapshot.docs.length} 筆文件', 'HomeRepository');

      // 如果有國家過濾但沒有結果，顯示詳細資訊
      if (countryFilter != null && querySnapshot.docs.isEmpty) {
        Logger.w('🔍 注意：國家 "$countryFilter" 沒有找到任何貼文', 'HomeRepository');
      }

      var posts = querySnapshot.docs
          .map((doc) => PostModel.fromFirestoreQuery(doc).toDomain())
          .toList();

      // 如果有關鍵字，進行客戶端過濾
      if (keyword != null && keyword.isNotEmpty) {
        posts = _filterPostsByKeyword(posts, keyword);
      }

      Logger.d('全域搜尋完成，返回 ${posts.length} 個結果', 'HomeRepository');
      return posts;
    } catch (error) {
      Logger.e('全域搜尋失敗: $error', 'HomeRepository');
      throw Exception('search_all_posts_failed: $error');
    }
  }

  /// 根據關鍵字過濾貼文
  List<Post> _filterPostsByKeyword(List<Post> posts, String keyword) {
    final keywordLower = keyword.toLowerCase();

    return posts.where((post) {
      // 搜尋內容
      final contentMatch = post.content.toLowerCase().contains(keywordLower);

      // 搜尋作者名稱
      final authorMatch =
          post.author.displayName.toLowerCase().contains(keywordLower);

      // 搜尋標籤
      final hashtagMatch =
          post.hashtags.any((tag) => tag.toLowerCase().contains(keywordLower));

      // 搜尋地理標籤
      final geoTagMatch = post.location.geoTags
          .any((tag) => tag.toLowerCase().contains(keywordLower));

      return contentMatch || authorMatch || hashtagMatch || geoTagMatch;
    }).toList();
  }

  // ========================================
  // 🗺️ 地圖標記操作
  // ========================================

  @override
  Future<List<MapMarker>> getMapMarkers(LocationQuery query) async {
    final posts = await getNearbyPosts(query);
    if (query.shouldCluster) {
      return await performSmartClustering(posts: posts, query: query);
    } else {
      return posts.map((post) => MapMarker.fromPost(post)).toList();
    }
  }

  @override
  Future<double> calculatePriorityScore(Post post, LocationQuery query) async {
    try {
      final now = DateTime.now();

      // 時間新鮮度分數 (0-1)
      final ageInHours = now.difference(post.createdAt).inHours;
      final recencyScore = math.max(0, 1 - (ageInHours / 24.0)).clamp(0.0, 1.0);

      // 互動熱度分數
      final engagement = post.stats.likeCount + (post.stats.commentCount * 2);
      final engagementScore = math.min(1.0, engagement / 50.0);

      // 作者聲譽分數 (0-1)
      final reputation = post.author.reputation ?? 0;
      final reputationScore = math.min(1.0, reputation / 1000.0);

      // 距離近程度分數 (0-1)
      final distance = post.distanceToLocation(query.latitude, query.longitude);
      final proximityScore =
          math.max(0, 1 - (distance / query.radiusMeters)).clamp(0.0, 1.0);

      // 內容多樣性分數（簡化版）
      final diversityScore = post.hasMedia ? 0.2 : 0.1;

      // 綜合計算
      final priorityScore = (recencyScore * 0.3) +
          (engagementScore * 0.25) +
          (reputationScore * 0.2) +
          (proximityScore * 0.15) +
          (diversityScore * 0.1);

      return priorityScore.clamp(0.0, 1.0);
    } catch (error) {
      Logger.e('Calculate priority score failed: $error', 'HomeRepository');
      return 0.0;
    }
  }

  @override
  Future<List<MapMarker>> performSmartClustering({
    required List<Post> posts,
    required LocationQuery query,
  }) async {
    try {
      if (posts.isEmpty) return [];

      final markers = <MapMarker>[];
      final processed = <String>{};
      final clusterRadius = query.clusterRadiusMeters;

      // 按優先級排序
      posts.sort(
          (a, b) => b.stats.priorityScore.compareTo(a.stats.priorityScore));

      for (final post in posts) {
        if (processed.contains(post.postId)) continue;

        // 找到附近的貼文
        final nearbyPosts = posts.where((otherPost) {
          if (processed.contains(otherPost.postId)) return false;
          if (otherPost.postId == post.postId) return true;

          final distance = _calculateDistance(
            post.location.fuzzyLatitude,
            post.location.fuzzyLongitude,
            otherPost.location.fuzzyLatitude,
            otherPost.location.fuzzyLongitude,
          );

          return distance <= clusterRadius;
        }).toList();

        // 標記為已處理
        for (final nearbyPost in nearbyPosts) {
          processed.add(nearbyPost.postId);
        }

        // 決定標記類型
        if (nearbyPosts.length == 1) {
          // 個別標記
          markers.add(MapMarker.fromPost(post));
        } else {
          // 聚合標記
          final clusterType = _determineClusterType(nearbyPosts);
          final centerCoords = _calculateClusterCenter(nearbyPosts);

          final clusterMarker = MapMarker.createCluster(
            clusterId: DateTime.now().millisecondsSinceEpoch.toString(),
            latitude: centerCoords.latitude,
            longitude: centerCoords.longitude,
            posts: nearbyPosts,
            clusterType: clusterType,
          );

          markers.add(clusterMarker);
        }
      }

      return markers;
    } catch (error) {
      throw Exception('smart_clustering_failed: $error');
    }
  }

  /// 決定聚合類型
  MarkerType _determineClusterType(List<Post> posts) {
    if (posts.isEmpty) return MarkerType.cluster;

    // 計算平均互動數
    final avgEngagement = posts.fold<double>(
          0.0,
          (total, post) => total + post.stats.totalEngagement,
        ) /
        posts.length;

    // 計算平均聲譽
    final avgReputation = posts.fold<double>(
          0.0,
          (total, post) => total + (post.author.reputation ?? 0),
        ) /
        posts.length;

    // 檢查是否有最近發布的內容
    final hasRecentPost = posts
        .any((post) => DateTime.now().difference(post.createdAt).inHours < 2);

    // 決定類型
    if (avgEngagement > 20) {
      return MarkerType.hot;
    } else if (avgReputation > 200) {
      return MarkerType.quality;
    } else if (hasRecentPost) {
      return MarkerType.newest;
    } else {
      return MarkerType.cluster;
    }
  }

  /// 計算聚合中心點
  ({double latitude, double longitude}) _calculateClusterCenter(
      List<Post> posts) {
    if (posts.isEmpty) return (latitude: 0.0, longitude: 0.0);

    final totalLat = posts.fold<double>(
      0.0,
      (total, post) => total + post.location.fuzzyLatitude,
    );
    final totalLng = posts.fold<double>(
      0.0,
      (total, post) => total + post.location.fuzzyLongitude,
    );

    return (
      latitude: totalLat / posts.length,
      longitude: totalLng / posts.length,
    );
  }

  /// 計算兩點間距離
  double _calculateDistance(
      double lat1, double lng1, double lat2, double lng2) {
    const double earthRadius = 6371000; // 地球半徑（公尺）

    final double lat1Rad = lat1 * (math.pi / 180);
    final double lat2Rad = lat2 * (math.pi / 180);
    final double deltaLatRad = (lat2 - lat1) * (math.pi / 180);
    final double deltaLngRad = (lng2 - lng1) * (math.pi / 180);

    final double a = math.pow(math.sin(deltaLatRad / 2), 2) +
        math.cos(lat1Rad) *
            math.cos(lat2Rad) *
            math.pow(math.sin(deltaLngRad / 2), 2);

    final double c = 2 * math.asin(math.sqrt(a));

    return earthRadius * c;
  }

  // ========================================
  // 💫 互動操作
  // ========================================

  @override
  Future<Post> toggleLike(String postId, String? userId,
      {bool isAnonymous = false, FakeAccount? fakeAccount}) async {
    print(
        '🔍 Repository toggleLike called with postId: $postId, userId: $userId, isAnonymous: $isAnonymous');

    if (userId == null) {
      print('❌ Repository: userId is null');
      throw Exception('user_not_authenticated');
    }

    try {
      final batch = _firestore.batch();

      // 決定使用的識別符和文檔ID
      final String userIdentifier =
          isAnonymous ? _generateAnonymousHash(userId) : userId;
      final String documentId = '${postId}_$userIdentifier';

      // 檢查是否已按讚
      final likeDoc = _likesCollection.doc(documentId);
      final likeSnapshot = await likeDoc.get();

      final isLiked = likeSnapshot.exists;
      print(
          '🔍 Repository: User has ${isLiked ? 'already' : 'not'} liked this post (${isAnonymous ? 'anonymous' : 'public'})');

      // 獲取貼文
      final post = await getPostById(postId);
      if (post == null) {
        print('❌ Repository: Post not found');
        throw Exception('post_not_found');
      }

      print('📊 Repository: Current like count: ${post.stats.likeCount}');

      // 更新按讚記錄
      if (isLiked) {
        print('🗑️ Repository: Removing like record');
        batch.delete(likeDoc);
      } else {
        print('➕ Repository: Adding like record');
        batch.set(likeDoc, {
          'post_id': postId,
          'user_id': userId, // 真實用戶ID（用於權限驗證）
          'user_identifier': userIdentifier, // 顯示用的識別符
          'is_anonymous': isAnonymous, // 是否匿名
          'created_at': FieldValue.serverTimestamp(),
        });
      }

      // 更新貼文統計
      final postRef = post.isBroadcast
          ? _broadcastsCollection.doc(postId)
          : _postsCollection.doc(postId);

      final increment = isLiked ? -1 : 1;
      print('📈 Repository: Updating like_count by $increment');

      batch.update(postRef, {
        'stats.likeCount': FieldValue.increment(increment),
      });

      print('🚀 Repository: Committing batch...');
      await batch.commit();
      print('✅ Repository: Batch committed successfully');

      // 記錄互動
      await recordPostInteraction(
        postId: postId,
        interactionType: isLiked ? 'unlike' : 'like',
        userId: userId,
      );

      // 如果是按讚（不是取消按讚），發送通知和記錄活動
      if (!isLiked) {
        // 獲取真實用戶 ID（如果使用假帳號）
        final realUserId = fakeAccount != null ? _getCurrentUserId() : null;

        Logger.i('🔔 Triggering like notification for post: $postId',
            'HomeRepository');
        Logger.i(
            '🔔 Liker: $userId, Anonymous: $isAnonymous, RealUser: $realUserId',
            'HomeRepository');
        Logger.i('🔔 Post author: ${post.author.userId}', 'HomeRepository');

        // 確保通知被發送並記錄詳細日誌
        try {
          await NotificationTriggerService.sendLikeNotification(
            postId: postId,
            likerId: userId,
            isAnonymous: isAnonymous,
            realUserId: realUserId,
          );
          Logger.i('✅ Like notification call completed', 'HomeRepository');
        } catch (error, stackTrace) {
          Logger.e('❌ Like notification call failed: $error', 'HomeRepository');
          Logger.e('📚 Stack trace: $stackTrace', 'HomeRepository');
        }

        // 記錄活動（異步執行，不影響主流程）
        _recordLikeActivity(
          postId: postId,
          targetUserId: post.author.userId,
          triggerUserId: userId,
          isAnonymous: isAnonymous,
        );
      }

      // 返回更新後的貼文
      return isLiked ? post.decrementLike() : post.incrementLike();
    } catch (error) {
      throw Exception('toggle_like_failed: $error');
    }
  }

  @override
  Future<bool> hasUserLiked(String postId, String? userId) async {
    if (userId == null) return false;

    try {
      // ✅ 優先使用快取
      final cacheService = InteractionCacheService.instance;
      final isLikedFromCache =
          await cacheService.isPostLiked(postId, accountId: userId);

      // 如果快取中有資料，直接返回
      if (isLikedFromCache) {
        Logger.d('✅ Cache hit for liked post: $postId', 'HomeRepository');
        return true;
      }

      // 如果快取中沒有，檢查是否需要同步
      final needsSync = await cacheService.needsSync();
      if (needsSync) {
        Logger.d('🔄 Cache expired, syncing user interactions for: $userId',
            'HomeRepository');
        // 同步用戶互動狀態
        await syncUserInteractions(userId);
        // 再次檢查快取
        return await cacheService.isPostLiked(postId, accountId: userId);
      }

      // 如果快取是最新的但沒有記錄，說明用戶沒有按讚
      Logger.d('✅ Cache miss (not liked) for post: $postId', 'HomeRepository');
      return false;
    } catch (error) {
      Logger.e('Check user liked failed: $error', 'HomeRepository');
      // 降級方案：直接查詢 Firestore
      try {
        Logger.w('🔄 Fallback to Firestore query for post: $postId',
            'HomeRepository');
        final likeDoc = await _likesCollection.doc('${postId}_$userId').get();
        return likeDoc.exists;
      } catch (fallbackError) {
        Logger.e(
            'Fallback query also failed: $fallbackError', 'HomeRepository');
        return false;
      }
    }
  }

  @override
  Future<List<String>> getPostLikers(String postId, {int limit = 50}) async {
    try {
      print('🔍 Repository: Getting likers for post $postId');

      final likesSnapshot = await _likesCollection
          .where('post_id', isEqualTo: postId)
          .orderBy('created_at', descending: true)
          .limit(limit)
          .get();

      final userIds = likesSnapshot.docs
          .map((doc) => doc.data() as Map<String, dynamic>)
          .map((data) => data['user_id'] as String)
          .toList();

      print('✅ Repository: Found ${userIds.length} likers');
      return userIds;
    } catch (error) {
      Logger.e('Get post likers failed: $error', 'HomeRepository');
      return [];
    }
  }

  @override
  Future<void> toggleFavorite(String postId, String userId,
      {FakeAccount? fakeAccount}) async {
    try {
      print(
          '🔍 Repository: toggleFavorite called for post: $postId, user: $userId');

      // 檢查用戶認證狀態
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('用戶未登入');
      }

      // 🔥 修復：如果是假帳號，跳過用戶ID驗證
      if (fakeAccount == null && currentUser.uid != userId) {
        throw Exception('用戶ID不匹配');
      }

      // 🔥 修復：如果是假帳號，驗證假帳號ID是否匹配
      if (fakeAccount != null && fakeAccount.uid != userId) {
        throw Exception('假帳號ID不匹配');
      }

      print('✅ Repository: User authenticated: ${currentUser.uid}');

      final favoriteDoc = _favoritesCollection.doc('${postId}_$userId');
      print('🔍 Repository: Document path: favorites/${postId}_$userId');

      final favoriteSnapshot = await favoriteDoc.get();
      print('📄 Repository: Document exists: ${favoriteSnapshot.exists}');

      if (favoriteSnapshot.exists) {
        print('🗑️ Repository: Deleting favorite...');
        await favoriteDoc.delete();
        print('✅ Repository: Favorite deleted successfully');
      } else {
        print('➕ Repository: Creating favorite...');
        await favoriteDoc.set({
          'post_id': postId,
          'user_id': userId,
          'created_at': FieldValue.serverTimestamp(),
        });
        print('✅ Repository: Favorite created successfully');

        // 發送收藏通知（只在新增收藏時發送）
        // 獲取真實用戶 ID（如果使用假帳號）
        final realUserId = fakeAccount != null ? _getCurrentUserId() : null;

        NotificationTriggerService.sendBookmarkNotification(
          postId: postId,
          bookmarkerId: userId,
          realUserId: realUserId,
        );

        // 記錄收藏活動
        final post = await getPostById(postId);
        if (post != null) {
          _recordBookmarkActivity(
            postId: postId,
            targetUserId: post.author.userId,
            triggerUserId: userId,
            isAnonymous: false, // 收藏功能通常不是匿名的
          );
        }
      }

      await recordPostInteraction(
        postId: postId,
        interactionType: favoriteSnapshot.exists ? 'unfavorite' : 'favorite',
        userId: userId,
      );

      print('✅ Repository: toggleFavorite completed successfully');
    } catch (error) {
      print('❌ Repository: toggleFavorite failed: $error');
      Logger.e('Toggle favorite failed: $error', 'HomeRepository');
      throw Exception('toggle_favorite_failed: $error');
    }
  }

  @override
  Future<bool> hasUserFavorited(String postId, String userId) async {
    try {
      // ✅ 優先使用 UserFavoriteService 的快取
      await _favoriteService.initializeUserCache(userId);
      return await _favoriteService.isPostFavorited(postId);
    } catch (error) {
      Logger.e('Check user favorited failed: $error', 'HomeRepository');

      // 降級方案：直接查詢 Firestore
      try {
        Logger.w('🔄 Fallback to Firestore query for post: $postId',
            'HomeRepository');

        // 使用新的結構查詢
        final userFavoritesDoc = await _favoritesCollection.doc(userId).get();
        if (!userFavoritesDoc.exists) {
          return false;
        }

        final data = userFavoritesDoc.data() as Map<String, dynamic>?;
        final favoritedPosts = data?['favorited_posts'] as List<dynamic>? ?? [];

        // 檢查是否包含該文章
        return favoritedPosts.any((favorite) {
          if (favorite is Map<String, dynamic>) {
            return favorite['post_id'] == postId;
          }
          return false;
        });
      } catch (fallbackError) {
        Logger.e(
            'Fallback query also failed: $fallbackError', 'HomeRepository');
        return false;
      }
    }
  }

  @override
  Future<List<Post>> getUserFavorites(String userId,
      {int limit = 20, String? lastPostId}) async {
    try {
      Logger.d(
          '🔍 getUserFavorites: Starting for userId=$userId, limit=$limit, lastPostId=$lastPostId',
          'HomeRepository');

      // 使用新的結構：一個用戶一個文檔
      final userFavoritesDoc = await _favoritesCollection.doc(userId).get();

      if (!userFavoritesDoc.exists) {
        Logger.d('🔍 getUserFavorites: No favorites document for user $userId',
            'HomeRepository');
        return [];
      }

      final data = userFavoritesDoc.data() as Map<String, dynamic>?;
      final favoritedPosts = data?['favorited_posts'] as List<dynamic>? ?? [];

      Logger.d(
          '🔍 getUserFavorites: Found ${favoritedPosts.length} favorite records',
          'HomeRepository');

      if (favoritedPosts.isEmpty) {
        return [];
      }

      // 按時間排序（最新的在前）
      final sortedFavorites = List<Map<String, dynamic>>.from(
          favoritedPosts.map((f) => f as Map<String, dynamic>))
        ..sort((a, b) {
          // 處理不同的時間格式
          DateTime? aTime;
          DateTime? bTime;

          // 嘗試解析時間
          final aTimeValue = a['favorited_at'];
          final bTimeValue = b['favorited_at'];

          if (aTimeValue is Timestamp) {
            aTime = aTimeValue.toDate();
          } else if (aTimeValue is String) {
            aTime = DateTime.tryParse(aTimeValue);
          }

          if (bTimeValue is Timestamp) {
            bTime = bTimeValue.toDate();
          } else if (bTimeValue is String) {
            bTime = DateTime.tryParse(bTimeValue);
          }

          if (aTime == null || bTime == null) return 0;
          return bTime.compareTo(aTime);
        });

      // 提取貼文 ID（實現分頁）
      final postIds = <String>[];
      int startIndex = 0;

      // 如果有 lastPostId，找到它的位置
      if (lastPostId != null) {
        for (int i = 0; i < sortedFavorites.length; i++) {
          if (sortedFavorites[i]['post_id'] == lastPostId) {
            startIndex = i + 1;
            break;
          }
        }
      }

      // 從 startIndex 開始取 limit 個
      for (int i = startIndex;
          i < sortedFavorites.length && postIds.length < limit;
          i++) {
        final postId = sortedFavorites[i]['post_id'] as String?;
        if (postId != null) {
          postIds.add(postId);
        }
      }

      Logger.d('🔍 getUserFavorites: Post IDs to fetch: ${postIds.join(", ")}',
          'HomeRepository');

      // 批量獲取貼文資料
      final posts = <Post>[];
      for (final postId in postIds) {
        final post = await getPostById(postId);
        if (post != null && !post.isDeleted) {
          posts.add(post);
          Logger.d('✅ getUserFavorites: Added post $postId', 'HomeRepository');
        } else {
          Logger.d('❌ getUserFavorites: Post $postId is null or deleted',
              'HomeRepository');
        }
      }

      Logger.d('🔍 getUserFavorites: Returning ${posts.length} posts',
          'HomeRepository');
      return posts;
    } catch (error) {
      Logger.e('Get user favorites failed: $error', 'HomeRepository');
      return [];
    }
  }

  @override
  Future<int> getUserFavoritesCount(String userId) async {
    try {
      final userFavoritesDoc = await _favoritesCollection.doc(userId).get();

      if (!userFavoritesDoc.exists) {
        return 0;
      }

      final data = userFavoritesDoc.data() as Map<String, dynamic>?;
      final favoritedPosts = data?['favorited_posts'] as List<dynamic>? ?? [];

      return favoritedPosts.length;
    } catch (error) {
      Logger.e('Get user favorites count failed: $error', 'HomeRepository');
      return 0;
    }
  }

  @override
  Future<void> sharePost(String postId, String? userId) async {
    try {
      // 更新分享計數
      final post = await getPostById(postId);
      if (post != null) {
        final postRef = post.isBroadcast
            ? _broadcastsCollection.doc(postId)
            : _postsCollection.doc(postId);

        await postRef.update({
          'stats.shareCount': FieldValue.increment(1),
        });

        // 記錄分享互動
        await recordPostInteraction(
          postId: postId,
          interactionType: 'share',
          userId: userId,
        );
      }
    } catch (error) {
      throw Exception('share_post_failed: $error');
    }
  }

  @override
  Future<void> viewPost(String postId, String? userId) async {
    if (userId == null) return;

    try {
      Logger.d('🔍 [DEBUG] 開始處理 viewPost: postId=$postId, userId=$userId',
          'ViewTracking');

      // 使用 ensureInitialized 確保快取已初始化（不會重複初始化）
      await _viewTrackingService.ensureInitialized(userId);
      Logger.d('🔍 [DEBUG] ViewTrackingService 初始化完成', 'ViewTracking');

      // 使用本地快取檢查是否已查看
      final hasViewed = _viewTrackingService.hasViewed(postId);
      Logger.d('🔍 [DEBUG] 檢查結果: hasViewed=$hasViewed', 'ViewTracking');

      if (hasViewed) {
        Logger.d('📊 [DEBUG] 用戶 $userId 已經查看過貼文 $postId，不重複計算', 'ViewTracking');
        return;
      }

      Logger.d('📊 [DEBUG] 記錄新的查看：用戶 $userId 查看貼文 $postId', 'ViewTracking');

      // 立即更新本地快取
      await _viewTrackingService.recordView(postId);
      Logger.d('🔍 [DEBUG] 本地快取更新完成', 'ViewTracking');

      // 更新 Firestore 中的 viewCount
      final post = await getPostById(postId);
      if (post != null) {
        final postRef = post.isBroadcast
            ? _broadcastsCollection.doc(postId)
            : _postsCollection.doc(postId);

        await postRef.update({
          'stats.viewCount': FieldValue.increment(1),
        });

        Logger.d('✅ [DEBUG] ViewCount +1 已同步到 Firestore', 'ViewTracking');
      }

      // 記錄查看互動
      await recordPostInteraction(
        postId: postId,
        interactionType: 'view',
        userId: userId,
      );
    } catch (error) {
      Logger.e('❌ [DEBUG] 記錄查看失敗: $error', 'ViewTracking');
    }
  }

  @override
  Future<bool> hasUserViewed(String postId, String userId) async {
    try {
      // 先確保 ViewTrackingService 已初始化
      await _viewTrackingService.initializeUserCache(userId);

      // 使用本地快取檢查
      return _viewTrackingService.hasViewed(postId);
    } catch (error) {
      Logger.e('檢查查看記錄失敗: $error', 'ViewTracking');
      return false;
    }
  }

  // ========================================
  // 👥 用戶追蹤
  // ========================================

  @override
  Future<void> followUser(String targetUserId, String currentUserId) async {
    try {
      // 使用新的追蹤服務（已包含通知發送）
      await _followService.followUser(currentUserId, targetUserId);

      Logger.i('✅ Repository: User $currentUserId followed $targetUserId',
          'HomeRepository');
    } catch (error) {
      Logger.e('❌ Repository: Follow user failed: $error', 'HomeRepository');
      throw Exception('follow_user_failed: $error');
    }
  }

  @override
  Future<void> unfollowUser(String targetUserId, String currentUserId) async {
    try {
      // 使用新的追蹤服務
      await _followService.unfollowUser(currentUserId, targetUserId);

      Logger.i('✅ Repository: User $currentUserId unfollowed $targetUserId',
          'HomeRepository');
    } catch (error) {
      Logger.e('❌ Repository: Unfollow user failed: $error', 'HomeRepository');
      throw Exception('unfollow_user_failed: $error');
    }
  }

  @override
  Future<bool> isFollowingUser(
      String targetUserId, String currentUserId) async {
    try {
      return await _followService.isFollowing(currentUserId, targetUserId);
    } catch (error) {
      Logger.e('❌ Repository: Check following status failed: $error',
          'HomeRepository');
      return false;
    }
  }

  @override
  Future<List<String>> getUserFollowing(String userId, {int limit = 50}) async {
    try {
      final followingList = await _followService.getFollowing(userId, limit);
      return followingList;
    } catch (error) {
      Logger.e(
          '❌ Repository: Get user following failed: $error', 'HomeRepository');
      return [];
    }
  }

  @override
  Future<List<String>> getUserFollowers(String userId, {int limit = 50}) async {
    try {
      final followersList = await _followService.getFollowers(userId, limit);
      return followersList;
    } catch (error) {
      Logger.e(
          '❌ Repository: Get user followers failed: $error', 'HomeRepository');
      return [];
    }
  }

  @override
  Future<Map<String, int>> getUserFollowStats(String userId) async {
    try {
      return await _followService.getFollowStats(userId);
    } catch (error) {
      Logger.e('❌ Repository: Get user follow stats failed: $error',
          'HomeRepository');
      return {'followersCount': 0, 'followingCount': 0};
    }
  }

  // ========================================
  // 🔄 即時更新
  // ========================================

  @override
  Stream<List<Post>> watchNearbyPosts(LocationQuery query) {
    throw Exception('watch_nearby_posts_failed: 此方法已被移除，請使用 getNearbyPosts 替代');
  }

  @override
  Stream<Post?> watchPost(String postId) {
    throw Exception('watch_post_failed: 此方法已被移除，請使用 getPost 替代');
  }

  @override
  Stream<List<MapMarker>> watchMapMarkers(LocationQuery query) {
    throw Exception('watch_map_markers_failed: 此方法已被移除，請使用 getMapMarkers 替代');
  }

  // ========================================
  // 📊 統計與分析
  // ========================================

  @override
  Future<void> updatePostStats(String postId, PostStats stats) async {
    try {
      final post = await getPostById(postId);
      if (post == null) return;

      final postRef = post.isBroadcast
          ? _broadcastsCollection.doc(postId)
          : _postsCollection.doc(postId);

      final statsModel = PostStatsModel.fromDomain(stats);
      await postRef.update(statsModel.toFirestore());
    } catch (error) {
      throw Exception('update_post_stats_failed: $error');
    }
  }

  /// 更新貼文單一統計值
  Future<void> updatePostStat(
      String postId, String statField, int increment) async {
    try {
      final post = await getPostById(postId);
      if (post == null) return;

      final postRef = post.isBroadcast
          ? _broadcastsCollection.doc(postId)
          : _postsCollection.doc(postId);

      await postRef.update({
        statField: FieldValue.increment(increment),
      });
    } catch (error) {
      throw Exception('update_post_stat_failed: $error');
    }
  }

  @override
  Future<void> recordPostImpression(String postId, String? userId) async {
    try {
      // iOS 修復：避免 FieldValue.serverTimestamp() 錯誤
      final dataToAdd = <String, dynamic>{
        'event_type': 'impression',
        'post_id': postId,
        'user_id': userId,
        'timestamp': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('analytics').add(dataToAdd);
    } catch (error) {
      Logger.e('Record post impression failed: $error', 'HomeRepository');
      // 如果是 iOS 的 serverTimestamp 錯誤，嘗試使用 Timestamp.now()
      if (error.toString().contains('serverTimestamp')) {
        try {
          final dataToAdd = <String, dynamic>{
            'event_type': 'impression',
            'post_id': postId,
            'user_id': userId,
            'timestamp': Timestamp.now(),
          };
          await _firestore.collection('analytics').add(dataToAdd);
          Logger.i('Used Timestamp.now() as fallback for impression',
              'HomeRepository');
        } catch (fallbackError) {
          Logger.e('Impression fallback also failed: $fallbackError',
              'HomeRepository');
        }
      }
    }
  }

  @override
  Future<void> recordPostInteraction({
    required String postId,
    required String interactionType,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // iOS 修復：避免 FieldValue.serverTimestamp() 錯誤
      // 先建立資料物件，確保不會在查詢中使用
      final dataToAdd = <String, dynamic>{
        'event_type': 'interaction',
        'interaction_type': interactionType,
        'post_id': postId,
        'user_id': userId,
        'metadata': metadata ?? {},
        'timestamp': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('analytics').add(dataToAdd);
    } catch (error) {
      Logger.e('Record post interaction failed: $error', 'HomeRepository');
      // 如果是 iOS 的 serverTimestamp 錯誤，嘗試使用 Timestamp.now()
      if (error.toString().contains('serverTimestamp')) {
        try {
          final dataToAdd = <String, dynamic>{
            'event_type': 'interaction',
            'interaction_type': interactionType,
            'post_id': postId,
            'user_id': userId,
            'metadata': metadata ?? {},
            'timestamp': Timestamp.now(),
          };
          await _firestore.collection('analytics').add(dataToAdd);
          Logger.i('Used Timestamp.now() as fallback', 'HomeRepository');
        } catch (fallbackError) {
          Logger.e('Fallback also failed: $fallbackError', 'HomeRepository');
        }
      }
    }
  }

  // ========================================
  // 🏷️ 標籤相關
  // ========================================

  @override
  Future<List<String>> getTrendingHashtags({
    LocationQuery? locationFilter,
    int limit = 10,
  }) async {
    try {
      // 計算24小時前的時間
      final twentyFourHoursAgo =
          DateTime.now().subtract(const Duration(hours: 24));

      // 查詢過去24小時內有使用 hashtag 的貼文
      final query = _postsCollection
          .where('created_at',
              isGreaterThan: Timestamp.fromDate(twentyFourHoursAgo))
          .where('is_deleted', isEqualTo: false)
          .where('hashtags', isNotEqualTo: []); // 有 hashtag 的貼文

      final querySnapshot = await query.get();

      // 統計 hashtag 使用頻率
      final hashtagCount = <String, int>{};

      for (final doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>?;
        final hashtagsRaw = data != null ? data['hashtags'] : null;
        final hashtags = hashtagsRaw is List
            ? List<String>.from(hashtagsRaw.whereType<String>())
            : <String>[];
        for (final hashtag in hashtags) {
          hashtagCount[hashtag] = (hashtagCount[hashtag] ?? 0) + 1;
        }
      }

      // 根據使用頻率排序
      final sortedHashtags = hashtagCount.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      // 返回前 limit 個熱門標籤
      final trendingHashtags =
          sortedHashtags.take(limit).map((entry) => entry.key).toList();

      Logger.database('獲取熱門標籤完成，找到 ${trendingHashtags.length} 個標籤');

      return trendingHashtags;
    } catch (error) {
      Logger.e('獲取熱門標籤失敗', 'DATABASE', error);
      // 如果查詢失敗，返回預設的熱門標籤
      return [
        '#生活',
        '#心情',
        '#美食',
        '#風景',
        '#工作',
        '#旅行',
        '#運動',
        '#電影',
        '#音樂',
        '#閱讀',
      ];
    }
  }

  @override
  Future<List<String>> getGeoTagSuggestions(String query) async {
    try {
      // 使用 Firestore 搜尋地理標籤
      final querySnapshot = await _firestore
          .collection('geo_tags')
          .where('tagName', isGreaterThanOrEqualTo: query)
          .where('tagName', isLessThan: '$query\uf8ff')
          .where('isActive', isEqualTo: true)
          .limit(10)
          .get();

      return querySnapshot.docs
          .map((doc) => doc.data()['tagName'] as String)
          .toList();
    } catch (error) {
      Logger.e('Get geo tag suggestions failed: $error', 'HomeRepository');
      return [];
    }
  }

  /// 根據地理標籤名稱獲取文檔 ID
  Future<List<String>> _getGeoTagIds(List<String> geoTagNames) async {
    final geoTagIds = <String>[];

    for (final geoTagName in geoTagNames) {
      if (_isSystemTag(geoTagName)) continue;

      try {
        // 首先嘗試 tagName 字段
        var query = await _firestore
            .collection('geo_tags')
            .where('tagName', isEqualTo: geoTagName)
            .where('isActive', isEqualTo: true)
            .limit(1)
            .get();

        if (query.docs.isNotEmpty) {
          geoTagIds.add(query.docs.first.id);
          continue;
        }

        // 備用：嘗試 tag_name 字段
        query = await _firestore
            .collection('geo_tags')
            .where('tag_name', isEqualTo: geoTagName)
            .where('is_active', isEqualTo: true)
            .limit(1)
            .get();

        if (query.docs.isNotEmpty) {
          geoTagIds.add(query.docs.first.id);
        }
      } catch (e) {
        Logger.w('⚠️ 獲取地理標籤 ID 失敗: $geoTagName - $e', 'HomeRepository');
      }
    }

    return geoTagIds;
  }

  /// 使用文檔 ID 更新地理標籤統計
  Future<void> updateGeoTagStatsByIds(List<String> geoTagIds) async {
    try {
      if (geoTagIds.isEmpty) {
        Logger.i('📍 沒有需要更新統計的地理標籤 ID', 'HomeRepository');
        return;
      }

      Logger.i('📊 開始更新地理標籤統計: ${geoTagIds.length} 個標籤', 'HomeRepository');

      for (final geoTagId in geoTagIds) {
        try {
          Logger.i('🔍 更新地理標籤: $geoTagId', 'HomeRepository');

          await _firestore.collection('geo_tags').doc(geoTagId).update({
            'postCount': FieldValue.increment(1),
            'post_count': FieldValue.increment(1), // 兼容性字段
            'updatedAt': FieldValue.serverTimestamp(),
            'updated_at': FieldValue.serverTimestamp(), // 兼容性字段
          });

          Logger.i('📊 更新地理標籤統計成功: $geoTagId', 'HomeRepository');
        } catch (tagError) {
          Logger.e('❌ 更新地理標籤統計失敗: $geoTagId - $tagError', 'HomeRepository');
        }
      }
    } catch (error) {
      Logger.e('Update geo tag stats by IDs failed: $error', 'HomeRepository');
    }
  }

  @override
  Future<void> updateGeoTagStats(List<String> geoTags) async {
    try {
      // 跳過系統標籤和座標字串
      final validGeoTags = geoTags.where((tag) => !_isSystemTag(tag)).toList();

      if (validGeoTags.isEmpty) {
        Logger.i('📍 沒有需要更新統計的地理標籤', 'HomeRepository');
        return;
      }

      Logger.i('📊 開始更新地理標籤統計: ${validGeoTags.join(', ')}', 'HomeRepository');

      // 獲取地理標籤 ID 並使用 ID 更新統計
      final geoTagIds = await _getGeoTagIds(validGeoTags);
      await updateGeoTagStatsByIds(geoTagIds);
    } catch (error) {
      Logger.e('Update geo tag stats failed: $error', 'HomeRepository');
    }
  }

  /// 檢查是否為系統標籤
  bool _isSystemTag(String tagName) {
    const systemTags = ['當前位置', '全台', 'Current Location', 'Taiwan'];

    // 檢查是否為座標字串格式
    if (tagName.contains(',') &&
        RegExp(r'^\d+\.\d+,\s*\d+\.\d+$').hasMatch(tagName)) {
      return true;
    }

    return systemTags.contains(tagName);
  }

  // ========================================
  // 📱 快取操作
  // ========================================

  @override
  Future<void> preloadNearbyContent(LocationQuery query) async {
    try {
      final posts = await getNearbyPosts(query);
      await _cachePosts(query, posts);
    } catch (error) {
      Logger.e('Preload nearby content failed: $error', 'HomeRepository');
    }
  }

  @override
  Future<void> cleanExpiredCache() async {
    try {
      await _localDB.cleanExpiredCache();
    } catch (error) {
      Logger.e('Clean expired cache failed: $error', 'HomeRepository');
    }
  }

  @override
  Future<int> getCacheSize() async {
    try {
      return await _localDB.getCacheSize();
    } catch (error) {
      Logger.e('Get cache size failed: $error', 'HomeRepository');
      return 0;
    }
  }

  @override
  Future<void> clearAllCache() async {
    try {
      await _localDB.clearImageCache();
      // 清理其他快取...
    } catch (error) {
      Logger.e('Clear all cache failed: $error', 'HomeRepository');
    }
  }

  // ========================================
  // 🛠️ 私有輔助方法
  // ========================================

  /// 快取貼文
  Future<void> _cachePosts(LocationQuery query, List<Post> posts) async {
    try {
      final cacheKey = _buildCacheKey(query);
      final postsData =
          posts.map((post) => PostModel.fromDomain(post).toJson()).toList();

      final cachedContent = CachedContent(
        contentId: cacheKey,
        contentType: 'nearby_posts',
        data: {'posts': postsData},
        expireAt: DateTime.now().add(const Duration(minutes: 10)),
      );

      await _localDB.cacheContent(cachedContent);
    } catch (error) {
      Logger.e('Cache posts failed: $error', 'HomeRepository');
    }
  }

  /// 建立快取鍵
  String _buildCacheKey(LocationQuery query) {
    return 'nearby_posts_${query.latitude.toStringAsFixed(3)}_${query.longitude.toStringAsFixed(3)}_${query.radiusMeters.toInt()}_${query.zoomLevel.toInt()}';
  }

  // ========================================
  // 📝 CRUD 操作實作
  // ========================================

  @override
  Future<Post> createPost(CreatePostParams params) async {
    Logger.i('📝 開始創建貼文...', 'HomeRepository');

    try {
      // 檢查用戶驗證
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        Logger.e('❌ 用戶未登入', 'HomeRepository');
        throw Exception('user_not_authenticated');
      }

      // 檢查用戶是否被標記為不當使用者（除非是假帳號發文）
      if (params.fakeAccount == null) {
        final canCreatePost =
            await AdminPermissionService.canUserCreatePost(currentUser.uid);
        if (!canCreatePost) {
          Logger.e('❌ 用戶被標記為不當使用者，無法發文', 'HomeRepository');
          throw Exception('user_flagged_cannot_post');
        }
      }

      Logger.i('👤 用戶驗證成功: ${currentUser.uid}', 'HomeRepository');

      // 生成貼文 ID 和雜湊值
      final postId = _firestore.collection('posts').doc().id;
      final postHash = _generatePostHash(params.content, DateTime.now());

      Logger.i('🔑 生成貼文 ID: $postId', 'HomeRepository');

      // 獲取作者資訊
      Logger.i('👤 獲取作者資訊...', 'HomeRepository');
      final author = await _getPostAuthor(params.isAnonymous,
          fakeAccount: params.fakeAccount);
      Logger.i('✅ 作者資訊獲取成功', 'HomeRepository');

      // 確保位置包含 GeoHash
      final locationWithGeoHash = _ensureLocationHasGeoHash(params.location);
      Logger.i(
          '📍 位置 GeoHash: ${locationWithGeoHash.geoHash}', 'HomeRepository');

      // 獲取創建者IP地址
      String? creatorIpAddress;
      try {
        creatorIpAddress = await IpAddressService.getCurrentIpAddress();
        Logger.i('🌐 創建者IP地址: $creatorIpAddress', 'HomeRepository');
      } catch (error) {
        Logger.w('⚠️ 獲取IP地址失敗，但不影響貼文創建: $error', 'HomeRepository');
      }

      // 獲取國家資訊（優先使用 params 中的快取資料，其次使用反地理編碼）
      String? countryCode = params.country;
      String? countryName = params.countryName;

      if (countryCode == null || countryName == null) {
        try {
          final geocodingResult = await reverseGeocodeWithCountry(
            locationWithGeoHash.latitude,
            locationWithGeoHash.longitude,
          );
          countryName = geocodingResult.effectiveCountryName;
          countryCode = geocodingResult.countryCode; // 如果有的話
          Logger.i(
              '🌍 貼文國家（反地理編碼）: $countryName ($countryCode)', 'HomeRepository');
        } catch (error) {
          Logger.w('⚠️ 獲取國家資訊失敗，但不影響貼文創建: $error', 'HomeRepository');
        }
      } else {
        Logger.i('🌍 貼文國家（快取）: $countryName ($countryCode)', 'HomeRepository');
      }

      // 🔗 解析連結預覽
      List<LinkPreviewModel> linkPreviews = [];
      try {
        Logger.i('🔗 開始解析連結預覽...', 'HomeRepository');
        final linkPreviewResult =
            await LinkPreviewService.parseLinksFromText(params.content);
        linkPreviews = linkPreviewResult.previews
            .map((preview) => LinkPreviewModel.fromDomain(preview))
            .toList();
        Logger.i('✅ 連結預覽解析完成: ${linkPreviews.length} 個', 'HomeRepository');
      } catch (error) {
        Logger.w('⚠️ 連結預覽解析失敗，但不影響貼文創建: $error', 'HomeRepository');
      }

      // 建立貼文模型
      final postModel = PostModel(
        postId: postId,
        postHash: postHash,
        author: author,
        content: params.content,
        location: PostLocationModel.fromDomain(locationWithGeoHash),
        stats: const PostStatsModel(),
        createdAt: DateTime.now(),
        type: _serializePostType(params.type),
        imageUrls: params.imageUrls,
        videoUrls: params.videoUrls,
        hashtags: params.hashtags,
        mentionedUsers: params.content
            .split(' ')
            .where((word) => word.startsWith('@'))
            .map((word) => word.substring(1))
            .toList(),
        linkPreviews: linkPreviews,
        expireAt: params.expireAt,
        country: _getCountryDisplayName(countryCode, countryName), // 使用友好的國家名稱
        countryName: countryName,
      );

      Logger.i('💾 正在儲存到 Firestore...', 'HomeRepository');

      // 準備儲存數據，包含IP地址
      final firestoreData = postModel.toFirestore();
      if (creatorIpAddress != null) {
        firestoreData['creator_ip_address'] = creatorIpAddress;
      }

      // 儲存到 Firestore
      await _postsCollection.doc(postId).set(firestoreData);

      Logger.i('✅ 貼文儲存成功', 'HomeRepository');

      // 記錄 Analytics 事件
      try {
        await enhancedAnalytics.logEvent('post_created', parameters: {
          'post_id': postId,
          'content_length': params.content.length,
          'has_images': params.imageUrls.isNotEmpty,
          'has_videos': params.videoUrls.isNotEmpty,
          'image_count': params.imageUrls.length,
          'video_count': params.videoUrls.length,
          'hashtag_count': params.hashtags.length,
          'is_anonymous': params.isAnonymous,
          'has_location': true,
          'geo_tags': params.location.geoTags.join(','),
        });

        // 記錄用戶行為
        await enhancedAnalytics.logUserAction('create_post', details: {
          'post_type': params.imageUrls.isNotEmpty ? 'with_media' : 'text_only',
          'anonymous': params.isAnonymous,
        });

        // 記錄位置使用
        await enhancedAnalytics.logLocation(
          lat: params.location.fuzzyLatitude,
          lng: params.location.fuzzyLongitude,
          label: 'post_creation',
        );
      } catch (analyticsError) {
        Logger.w(
            '⚠️ Analytics 記錄失敗，但不影響貼文創建: $analyticsError', 'HomeRepository');
      }

      // 更新地理標籤統計 (不影響主流程)
      try {
        await updateGeoTagStats(params.location.geoTags);
        Logger.i('📊 地理標籤統計更新成功', 'HomeRepository');
      } catch (statsError) {
        Logger.w('⚠️ 地理標籤統計更新失敗，但不影響貼文創建: $statsError', 'HomeRepository');
      }

      Logger.i('🎉 貼文創建完成: $postId', 'HomeRepository');

      // 發送關注者發文通知（如果不是匿名發文）
      if (!params.isAnonymous) {
        NotificationTriggerService.sendFollowedUserPostNotification(
          postId: postId,
          authorId: currentUser.uid,
          content: params.content,
        );
      }

      // 返回創建的貼文
      return postModel.toDomain();
    } catch (error) {
      Logger.e('❌ 創建貼文失敗: $error', 'HomeRepository');

      // 根據錯誤類型提供更詳細的錯誤訊息
      if (error.toString().contains('user_not_authenticated')) {
        throw Exception('user_not_authenticated');
      } else if (error.toString().contains('permission-denied')) {
        throw Exception('permission_denied');
      } else if (error.toString().contains('network')) {
        throw Exception('network_error');
      } else {
        throw Exception('create_post_failed: $error');
      }
    }
  }

  @override
  Future<Post> updatePost(String postId, CreatePostParams params) async {
    try {
      // 檢查權限
      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) throw Exception('user_not_authenticated');
      final canEdit = await canUserEditPost(postId, currentUserId);
      if (!canEdit) {
        throw Exception('permission_denied');
      }

      // 獲取原始貼文
      final originalDoc = await _postsCollection.doc(postId).get();
      if (!originalDoc.exists) {
        throw Exception('post_not_found');
      }

      final originalPost = PostModel.fromFirestore(originalDoc);

      // 確保位置包含 GeoHash
      final locationWithGeoHash = _ensureLocationHasGeoHash(params.location);

      // 更新貼文
      final updatedPost = originalPost.copyWith(
        content: params.content,
        location: PostLocationModel.fromDomain(locationWithGeoHash),
        imageUrls: params.imageUrls,
        videoUrls: params.videoUrls,
        hashtags: params.hashtags,
        updatedAt: DateTime.now(),
        editedAt: DateTime.now(),
        mentionedUsers: params.content
            .split(' ')
            .where((word) => word.startsWith('@'))
            .map((word) => word.substring(1))
            .toList(),
      );

      // 儲存更新
      await _postsCollection.doc(postId).update(updatedPost.toFirestore());

      return updatedPost.toDomain();
    } catch (error) {
      throw Exception('update_post_failed: $error');
    }
  }

  @override
  Future<void> deletePost(String postId) async {
    try {
      // 檢查權限
      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) throw Exception('user_not_authenticated');

      // 檢查是否為管理員或貼文作者
      final canDelete = await canUserDeletePost(postId, currentUserId);
      final isAdmin = await AdminPermissionService.isCurrentUserAdmin();

      if (!canDelete && !isAdmin) {
        throw Exception('permission_denied');
      }

      // 硬刪除貼文
      await _postsCollection.doc(postId).delete();

      // 清理相關資料（按讚、收藏等）
      await _cleanupPostData(postId);
    } catch (error) {
      throw Exception('delete_post_failed: $error');
    }
  }

  @override
  Future<void> softDeletePost(String postId) async {
    try {
      // 檢查權限
      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) throw Exception('user_not_authenticated');

      // 檢查是否為管理員或貼文作者
      final canDelete = await canUserDeletePost(postId, currentUserId);
      final isAdmin = await AdminPermissionService.isCurrentUserAdmin();

      if (!canDelete && !isAdmin) {
        throw Exception('permission_denied');
      }

      // 軟刪除（標記為已刪除）
      await _postsCollection.doc(postId).update({
        'is_deleted': true,
        'deleted_at': FieldValue.serverTimestamp(),
      });
    } catch (error) {
      throw Exception('soft_delete_post_failed: $error');
    }
  }

  @override
  Future<bool> canUserEditPost(String postId, String userId) async {
    try {
      final doc = await _postsCollection.doc(postId).get();
      if (!doc.exists) return false;

      final data = doc.data() as Map<String, dynamic>;
      return data['user_id'] == userId;
    } catch (error) {
      Logger.e('Check edit permission failed: $error', 'HomeRepository');
      return false;
    }
  }

  @override
  Future<bool> canUserDeletePost(String postId, String userId) async {
    try {
      final doc = await _postsCollection.doc(postId).get();
      if (!doc.exists) return false;

      final data = doc.data() as Map<String, dynamic>;
      return data['user_id'] == userId;
    } catch (error) {
      Logger.e('Check delete permission failed: $error', 'HomeRepository');
      return false;
    }
  }

  @override
  Future<List<Post>> getUserPosts(String userId,
      {int limit = 20, String? lastPostId}) async {
    try {
      Logger.d('查詢用戶貼文，用戶ID: $userId', 'HomeRepository');

      // 首先獲取用戶的匿名hash，用於構建查詢條件
      String? userAnonymousHash;
      try {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        if (userDoc.exists) {
          userAnonymousHash = userDoc.data()?['anonymousHash'] as String?;
          Logger.d('用戶匿名hash: $userAnonymousHash', 'HomeRepository');
        }
      } catch (e) {
        Logger.w('獲取用戶匿名hash失敗: $e', 'HomeRepository');
      }

      // 直接查詢該用戶的非匿名文章
      // 使用 author.user_id = userId 且 author.is_anonymous = false 的條件
      Query query = _postsCollection
          .where('author.user_id', isEqualTo: userId)
          .where('author.is_anonymous', isEqualTo: false)
          .where('is_deleted', isEqualTo: false)
          .orderBy('created_at', descending: true)
          .limit(limit);

      // 實現 startAfter 分頁
      if (lastPostId != null) {
        final lastDoc = await _postsCollection.doc(lastPostId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final snapshot = await query.get();
      Logger.d('使用 author.user_id 查詢到 ${snapshot.docs.length} 篇貼文',
          'HomeRepository');

      List<Post> posts = snapshot.docs
          .map((doc) {
            try {
              return PostModel.fromFirestoreQuery(doc).toDomain();
            } catch (e) {
              Logger.e('解析貼文失敗: ${doc.id}, 錯誤: $e', 'HomeRepository');
              return null;
            }
          })
          .where((post) => post != null)
          .cast<Post>()
          .toList();

      Logger.d('最終返回 ${posts.length} 篇貼文', 'HomeRepository');
      return posts;
    } catch (error) {
      Logger.e('Get user posts failed: $error', 'HomeRepository');
      return [];
    }
  }

  @override
  Future<List<Post>> getUserDrafts(String userId) async {
    try {
      // TODO: 實作草稿功能
      return [];
    } catch (error) {
      Logger.e('Get user drafts failed: $error', 'HomeRepository');
      return [];
    }
  }

  @override
  Future<void> saveDraft(CreatePostParams params, String userId) async {
    try {
      // TODO: 實作草稿儲存功能
    } catch (error) {
      Logger.e('Save draft failed: $error', 'HomeRepository');
    }
  }

  @override
  Future<void> deleteDraft(String draftId, String userId) async {
    try {
      // TODO: 實作草稿刪除功能
    } catch (error) {
      Logger.e('Delete draft failed: $error', 'HomeRepository');
    }
  }

  // ========================================
  // 🛠️ 私有輔助方法
  // ========================================

  /// 生成貼文雜湊值
  String _generatePostHash(String content, DateTime timestamp) {
    final hash = '${content}_${timestamp.millisecondsSinceEpoch}'.hashCode;
    return hash.toRadixString(16).toUpperCase();
  }

  /// 序列化貼文類型
  String _serializePostType(PostType type) {
    switch (type) {
      case PostType.broadcast:
        return 'broadcast';
      case PostType.normal:
      default:
        return 'normal';
    }
  }

  /// 清理貼文相關資料
  Future<void> _cleanupPostData(String postId) async {
    try {
      final batch = _firestore.batch();

      // 清理按讚記錄
      final likesSnapshot =
          await _likesCollection.where('post_id', isEqualTo: postId).get();
      for (final doc in likesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // 清理收藏記錄
      final favoritesSnapshot =
          await _favoritesCollection.where('post_id', isEqualTo: postId).get();
      for (final doc in favoritesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (error) {
      Logger.e('Cleanup post data failed: $error', 'HomeRepository');
    }
  }

  // ========================================
  // 💬 留言相關
  // ========================================

  @override
  Future<Comment> addComment(AddCommentParams params) async {
    if (params.userId == null) {
      throw Exception('user_not_authenticated');
    }

    // 檢查用戶是否被標記為不當使用者
    final canComment =
        await AdminPermissionService.canUserComment(params.userId!);
    if (!canComment) {
      throw Exception('user_flagged_cannot_comment');
    }

    try {
      final batch = _firestore.batch();

      // 創建留言文檔
      final commentRef = _commentsCollection.doc();
      final commentId = commentRef.id;

      // 處理匿名模式、假帳號和用戶名稱
      String authorName;
      String authorId;
      String? authorAvatarUrl;

      if (params.isAnonymous) {
        authorName = tr('common.anonymous');
        authorId = _generateAnonymousHash(params.userId!);
        authorAvatarUrl = null; // 匿名用戶沒有頭像
      } else if (params.fakeAccountId != null) {
        // 使用假帳號資訊
        authorName = params.fakeAccountName ?? 'Fake User';
        authorId = params.fakeAccountId!;
        authorAvatarUrl = params.fakeAccountAvatarUrl; // 使用假帳號頭像
        Logger.i('🎭 留言使用假帳號: $authorName ($authorId)', 'HomeRepository');
      } else {
        // 獲取真實用戶的最新資訊
        try {
          final userDoc =
              await _firestore.collection('users').doc(params.userId!).get();
          if (userDoc.exists) {
            final userData = userDoc.data()!;
            authorName = userData['displayName'] as String? ?? 'User';
            authorAvatarUrl = userData['avatarUrl'] as String?;
            Logger.i('👤 從Firestore獲取最新用戶資訊: $authorName', 'HomeRepository');
          } else {
            // 降級到Firebase Auth
            final currentUser = _auth.currentUser;
            authorName = currentUser?.displayName ?? 'User';
            authorAvatarUrl = currentUser?.photoURL;
          }
        } catch (e) {
          Logger.w('獲取用戶資訊失敗，使用Firebase Auth: $e', 'HomeRepository');
          final currentUser = _auth.currentUser;
          authorName = currentUser?.displayName ?? 'User';
          authorAvatarUrl = currentUser?.photoURL;
        }
        authorId = params.userId!;
      }

      // 獲取留言者IP地址
      String? commenterIpAddress;
      try {
        commenterIpAddress = await IpAddressService.getCurrentIpAddress();
        Logger.i('🌐 留言者IP地址: $commenterIpAddress', 'HomeRepository');
      } catch (error) {
        Logger.w('⚠️ 獲取IP地址失敗，但不影響留言創建: $error', 'HomeRepository');
      }

      // 🔗 解析連結預覽
      List<Map<String, dynamic>> linkPreviewsData = [];
      try {
        Logger.i('🔗 開始解析留言連結預覽...', 'HomeRepository');
        final linkPreviewResult =
            await LinkPreviewService.parseLinksFromText(params.content);
        linkPreviewsData = linkPreviewResult.previews
            .map(
                (preview) => LinkPreviewModel.fromDomain(preview).toFirestore())
            .toList();
        Logger.i(
            '✅ 留言連結預覽解析完成: ${linkPreviewsData.length} 個', 'HomeRepository');
      } catch (error) {
        Logger.w('⚠️ 留言連結預覽解析失敗，但不影響留言創建: $error', 'HomeRepository');
      }

      final commentData = {
        'id': commentId,
        'post_id': params.postId,
        'content': params.content,
        'author_id': params.userId, // 真實用戶ID（用於權限驗證）
        'author_identifier': authorId, // 顯示用的識別符
        'author_name': authorName, // 顯示用的名稱
        'author_avatar_url': authorAvatarUrl, // 作者大頭照URL
        'is_anonymous': params.isAnonymous, // 是否匿名
        'is_deleted': false, // 明確設置為未刪除
        'created_at': FieldValue.serverTimestamp(),
        'parent_id': params.parentId,
        'like_count': 0,
        'reply_count': 0,
        'is_edited': false,
        'image_url': params.imageUrl, // 留言圖片 URL
        'link_previews': linkPreviewsData, // 連結預覽
        if (commenterIpAddress != null)
          'commenter_ip_address': commenterIpAddress,
      };

      Logger.d('💾 準備保存留言數據: $commentData', 'HomeRepository');

      batch.set(commentRef, commentData);

      // 更新貼文統計
      final post = await getPostById(params.postId);
      if (post != null) {
        final postRef = post.isBroadcast
            ? _broadcastsCollection.doc(params.postId)
            : _postsCollection.doc(params.postId);

        batch.update(postRef, {
          'stats.commentCount': FieldValue.increment(1),
          'comment_stats.total': FieldValue.increment(1),
        });
      }

      await batch.commit();

      // 記錄互動
      await recordPostInteraction(
        postId: params.postId,
        interactionType: 'comment',
        userId: params.userId,
        metadata: {'comment_id': commentId},
      );

      // 發送留言通知（如果不是回覆）
      if (params.parentId == null) {
        NotificationTriggerService.sendCommentNotification(
          postId: params.postId,
          commenterId: params.userId!,
          content: params.content,
          isAnonymous: params.isAnonymous,
        );
      } else {
        // 如果是回覆，發送回覆通知
        NotificationTriggerService.sendReplyNotification(
          postId: params.postId,
          parentCommentId: params.parentId!,
          replyId: commentId,
          replierId: params.userId!,
          content: params.content,
        );
      }

      // 發送關注者留言通知（只有非匿名用戶才發送）
      if (!params.isAnonymous) {
        NotificationTriggerService.sendFollowedUserCommentNotification(
          postId: params.postId,
          commentId: commentId,
          commenterId: params.userId!,
          content: params.content,
          isAnonymous: params.isAnonymous,
        );
      }

      // 記錄留言活動
      if (post != null) {
        _recordCommentActivity(
          postId: params.postId,
          commentId: commentId,
          targetUserId: post.author.userId,
          triggerUserId: params.userId!,
          isAnonymous: params.isAnonymous,
          commentPreview: params.content.length > 50
              ? '${params.content.substring(0, 50)}...'
              : params.content,
        );
      }

      // 從 Firestore 重新讀取剛創建的留言以確保資料一致性
      try {
        await Future.delayed(
            const Duration(milliseconds: 100)); // 短暫延遲確保 Firestore 寫入完成
        final commentDoc = await _commentsCollection.doc(commentId).get();
        if (commentDoc.exists) {
          final data = commentDoc.data() as Map<String, dynamic>;
          return Comment(
            id: data['id'] as String,
            postId: data['post_id'] as String,
            content: data['content'] as String,
            authorId: data['author_identifier'] as String? ??
                data['author_id'] as String,
            authorName: data['author_name'] as String,
            createdAt: (data['created_at'] as Timestamp).toDate(),
            parentId: data['parent_id'] as String?,
            likeCount: (data['like_count'] as num?)?.toInt() ?? 0,
            replyCount: (data['reply_count'] as num?)?.toInt() ?? 0,
            isEdited: data['is_edited'] as bool? ?? false,
            editedAt: data['edited_at'] != null
                ? (data['edited_at'] as Timestamp).toDate()
                : null,
            imageUrl: data['image_url'] as String?,
            linkPreviews: _parseCommentLinkPreviews(data['link_previews']),
          );
        }
      } catch (e) {
        Logger.w('無法重新讀取留言，使用備用資料: $e', 'HomeRepository');
      }

      // 備用返回（如果重新讀取失敗）
      return Comment(
        id: commentId,
        postId: params.postId,
        content: params.content,
        authorId: authorId, // 使用處理過的 authorId
        authorName: authorName, // 使用處理過的 authorName
        authorAvatarUrl: authorAvatarUrl, // 使用處理過的 authorAvatarUrl
        createdAt: DateTime.now(),
        parentId: params.parentId,
        imageUrl: params.imageUrl,
      );
    } catch (error) {
      throw Exception('add_comment_failed: $error');
    }
  }

  @override
  Future<void> deleteComment(String commentId, String userId) async {
    try {
      final batch = _firestore.batch();

      // 獲取留言資料
      final commentDoc = await _commentsCollection.doc(commentId).get();
      if (!commentDoc.exists) {
        throw Exception('comment_not_found');
      }

      final commentData = commentDoc.data() as Map<String, dynamic>;
      final postId = commentData['post_id'] as String;
      final authorId = commentData['author_id'] as String;

      // 檢查權限
      if (authorId != userId) {
        throw Exception('permission_denied');
      }

      // 刪除留言
      batch.delete(_commentsCollection.doc(commentId));

      // 更新貼文統計
      final post = await getPostById(postId);
      if (post != null) {
        final postRef = post.isBroadcast
            ? _broadcastsCollection.doc(postId)
            : _postsCollection.doc(postId);

        batch.update(postRef, {
          'stats.commentCount': FieldValue.increment(-1),
          'comment_stats.total': FieldValue.increment(-1),
        });
      }

      await batch.commit();
    } catch (error) {
      throw Exception('delete_comment_failed: $error');
    }
  }

  @override
  Future<List<Comment>> getPostComments(String postId,
      {int limit = 20, int offset = 0}) async {
    try {
      Logger.d('Getting comments for post $postId (flat structure)',
          'HomeRepository');

      // 增加短暫延遲確保 Firestore 索引更新完成
      await Future.delayed(const Duration(milliseconds: 50));

      // 🎯 扁平式結構：取得所有留言（父留言 + 回覆留言）並按時間排序
      Query query = _commentsCollection
          .where('post_id', isEqualTo: postId)
          .orderBy('created_at', descending: false);

      Logger.d('查詢留言條件: post_id = $postId', 'HomeRepository');

      // 使用 startAfter 實現高效分頁（不再使用 offset）
      // offset 參數已廢棄，請使用適當的 lastDocumentId 進行分頁

      query = query.limit(limit);
      final querySnapshot = await query.get();
      print('📊 Repository: Found ${querySnapshot.docs.length} total comments');

      final comments = <Comment>[];

      for (final doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // 基本調試信息
        Logger.d(
            '📝 處理留言: ${doc.id} - ${data['author_name']}', 'HomeRepository');

        try {
          // 建立留言物件（不論是父留言或回覆留言）
          final comment = Comment(
            id: data['id'] as String,
            postId: data['post_id'] as String,
            content: data['content'] as String,
            authorId: data['author_identifier'] as String? ??
                data['author_id'] as String,
            authorName: data['author_name'] as String,
            authorAvatarUrl: data['author_avatar_url'] as String?, // 添加大頭照URL
            createdAt: (data['created_at'] as Timestamp).toDate(),
            parentId: data['parent_id'] as String?, // 回覆留言會有 parentId
            likeCount: (data['like_count'] as num?)?.toInt() ?? 0,
            replyCount: (data['reply_count'] as num?)?.toInt() ?? 0,
            isEdited: data['is_edited'] as bool? ?? false,
            editedAt: data['edited_at'] != null
                ? (data['edited_at'] as Timestamp).toDate()
                : null,
            imageUrl: data['image_url'] as String?,
            linkPreviews: _parseCommentLinkPreviews(data['link_previews']),
            replies: [], // 扁平結構不需要巢狀回覆
          );

          comments.add(comment);
          Logger.d('  ✅ 留言處理成功', 'HomeRepository');
        } catch (e) {
          Logger.e('  ❌ 留言處理失敗: $e', 'HomeRepository');
          Logger.e('  數據: $data', 'HomeRepository');
        }
      }

      print(
          '✅ Repository: Processed ${comments.length} comments in flat structure');
      return comments;
    } catch (error) {
      print('❌ Repository: Get comments failed: $error');
      throw Exception('get_post_comments_failed: $error');
    }
  }

  @override
  Future<List<Comment>> getCommentReplies(String commentId,
      {int limit = 10, int offset = 0}) async {
    try {
      Logger.d('🔍 Repository: Getting replies for comment $commentId',
          'HomeRepository');
      final query = _commentsCollection
          .where('parent_id', isEqualTo: commentId)
          .orderBy('created_at', descending: false)
          .limit(limit);

      final snapshot = await query.get();

      final comments = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        print('📝 Processing comment: ${doc.id} - ${data['content']}');

        return Comment(
          id: data['id'] as String,
          postId: data['post_id'] as String,
          content: data['content'] as String,
          authorId: data['author_identifier'] as String? ??
              data['author_id'] as String, // 優先使用 author_identifier
          authorName: data['author_name'] as String,
          createdAt: (data['created_at'] as Timestamp).toDate(),
          parentId: data['parent_id'] as String?,
          likeCount: (data['like_count'] as num?)?.toInt() ?? 0,
          replyCount: (data['reply_count'] as num?)?.toInt() ?? 0,
          isEdited: data['is_edited'] as bool? ?? false,
          editedAt: data['edited_at'] != null
              ? (data['edited_at'] as Timestamp).toDate()
              : null,
          imageUrl: data['image_url'] as String?,
          linkPreviews: _parseCommentLinkPreviews(data['link_previews']),
        );
      }).toList();

      print('✅ Repository: Processed ${comments.length} comments with replies');
      return comments;
    } catch (error) {
      print('❌ Repository: Get comments failed: $error');
      throw Exception('get_post_comments_failed: $error');
    }
  }

  @override
  Future<bool> canUserDeleteComment(String commentId, String userId) async {
    try {
      final commentDoc = await _commentsCollection.doc(commentId).get();
      if (!commentDoc.exists) return false;

      final commentData = commentDoc.data() as Map<String, dynamic>;
      final authorId = commentData['author_id'] as String;

      return authorId == userId;
    } catch (error) {
      Logger.e(
          'Check user can delete comment failed: $error', 'HomeRepository');
      return false;
    }
  }

  @override
  Future<Comment> toggleCommentLike(String commentId, String? userId) async {
    if (userId == null) {
      throw Exception('user_not_authenticated');
    }

    try {
      final batch = _firestore.batch();

      // 檢查是否已按讚
      final likeDoc = await _likesCollection
          .where('comment_id', isEqualTo: commentId)
          .where('user_id', isEqualTo: userId)
          .get();

      final commentRef = _commentsCollection.doc(commentId);
      final commentDoc = await commentRef.get();

      if (!commentDoc.exists) {
        throw Exception('comment_not_found');
      }

      final commentData = commentDoc.data() as Map<String, dynamic>;
      final currentLikeCount =
          (commentData['like_count'] as num?)?.toInt() ?? 0;

      if (likeDoc.docs.isNotEmpty) {
        // 取消按讚
        batch.delete(likeDoc.docs.first.reference);
        batch.update(commentRef, {
          'like_count': currentLikeCount - 1,
        });

        await batch.commit();

        // 記錄互動
        await recordPostInteraction(
          postId: commentData['post_id'] as String,
          interactionType: 'comment_unlike',
          userId: userId,
          metadata: {'comment_id': commentId},
        );

        return Comment(
          id: commentId,
          postId: commentData['post_id'] as String,
          content: commentData['content'] as String,
          authorId: commentData['author_id'] as String,
          authorName: commentData['author_name'] as String,
          createdAt: (commentData['created_at'] as Timestamp).toDate(),
          parentId: commentData['parent_id'] as String?,
          likeCount: currentLikeCount - 1,
          replyCount: (commentData['reply_count'] as num?)?.toInt() ?? 0,
          isEdited: commentData['is_edited'] as bool? ?? false,
          editedAt: commentData['edited_at'] != null
              ? (commentData['edited_at'] as Timestamp).toDate()
              : null,
          imageUrl: commentData['image_url'] as String?,
        );
      } else {
        // 按讚
        final likeRef = _likesCollection.doc();
        batch.set(likeRef, {
          'comment_id': commentId,
          'user_id': userId,
          'created_at': FieldValue.serverTimestamp(),
        });

        batch.update(commentRef, {
          'like_count': currentLikeCount + 1,
        });

        await batch.commit();

        // 記錄互動
        await recordPostInteraction(
          postId: commentData['post_id'] as String,
          interactionType: 'comment_like',
          userId: userId,
          metadata: {'comment_id': commentId},
        );

        return Comment(
          id: commentId,
          postId: commentData['post_id'] as String,
          content: commentData['content'] as String,
          authorId: commentData['author_id'] as String,
          authorName: commentData['author_name'] as String,
          createdAt: (commentData['created_at'] as Timestamp).toDate(),
          parentId: commentData['parent_id'] as String?,
          likeCount: currentLikeCount + 1,
          replyCount: (commentData['reply_count'] as num?)?.toInt() ?? 0,
          isEdited: commentData['is_edited'] as bool? ?? false,
          editedAt: commentData['edited_at'] != null
              ? (commentData['edited_at'] as Timestamp).toDate()
              : null,
          imageUrl: commentData['image_url'] as String?,
        );
      }
    } catch (error) {
      throw Exception('toggle_comment_like_failed: $error');
    }
  }

  @override
  Future<bool> hasUserLikedComment(String commentId, String? userId) async {
    if (userId == null) return false;

    try {
      final likeDoc = await _likesCollection
          .where('comment_id', isEqualTo: commentId)
          .where('user_id', isEqualTo: userId)
          .get();

      return likeDoc.docs.isNotEmpty;
    } catch (error) {
      Logger.e('Check user liked comment failed: $error', 'HomeRepository');
      return false;
    }
  }

  @override
  Future<Comment> addCommentReply(
      String commentId, AddCommentParams params) async {
    if (params.userId == null) {
      throw Exception('user_not_authenticated');
    }

    // 檢查用戶是否被標記為不當使用者
    final canComment =
        await AdminPermissionService.canUserComment(params.userId!);
    if (!canComment) {
      throw Exception('user_flagged_cannot_comment');
    }

    try {
      final batch = _firestore.batch();

      // 檢查父留言是否存在
      final parentCommentDoc = await _commentsCollection.doc(commentId).get();
      if (!parentCommentDoc.exists) {
        throw Exception('parent_comment_not_found');
      }

      // 創建回覆留言
      final replyRef = _commentsCollection.doc();
      final replyId = replyRef.id;

      // 處理假帳號回覆
      String authorName;
      String authorIdentifier;
      String? authorAvatarUrl;

      if (params.isAnonymous) {
        authorName = tr('common.anonymous');
        authorIdentifier = _generateAnonymousHash(params.userId!);
        authorAvatarUrl = null; // 匿名用戶沒有頭像
      } else if (params.fakeAccountId != null) {
        // 使用假帳號資訊
        authorName = params.fakeAccountName ?? 'Fake User';
        authorIdentifier = params.fakeAccountId!;
        authorAvatarUrl = params.fakeAccountAvatarUrl; // 使用假帳號頭像
        Logger.i(
            '🎭 回覆使用假帳號: $authorName ($authorIdentifier)', 'HomeRepository');
      } else {
        // 獲取真實用戶的最新資訊
        try {
          final userDoc =
              await _firestore.collection('users').doc(params.userId!).get();
          if (userDoc.exists) {
            final userData = userDoc.data()!;
            authorName = userData['displayName'] as String? ?? 'User';
            authorAvatarUrl = userData['avatarUrl'] as String?;
            Logger.i('👤 從Firestore獲取最新用戶資訊: $authorName', 'HomeRepository');
          } else {
            // 降級到Firebase Auth
            final currentUser = _auth.currentUser;
            authorName = currentUser?.displayName ?? 'User';
            authorAvatarUrl = currentUser?.photoURL;
          }
        } catch (e) {
          Logger.w('獲取用戶資訊失敗，使用Firebase Auth: $e', 'HomeRepository');
          final currentUser = _auth.currentUser;
          authorName = currentUser?.displayName ?? 'User';
          authorAvatarUrl = currentUser?.photoURL;
        }
        authorIdentifier = params.userId!;
      }

      final replyData = {
        'id': replyId,
        'post_id': params.postId,
        'content': params.content,
        'author_id': params.userId, // 真實用戶ID（用於權限驗證）
        'author_identifier': authorIdentifier, // 顯示用的識別符
        'author_name': authorName, // 顯示用的名稱
        'author_avatar_url': authorAvatarUrl, // 作者大頭照URL
        'is_anonymous': params.isAnonymous, // 是否匿名
        'created_at': FieldValue.serverTimestamp(),
        'parent_id': commentId,
        'like_count': 0,
        'reply_count': 0,
        'is_edited': false,
        'image_url': params.imageUrl,
      };

      batch.set(replyRef, replyData);

      // 更新父留言的回覆數量
      batch.update(_commentsCollection.doc(commentId), {
        'reply_count': FieldValue.increment(1),
      });

      // 更新貼文統計
      final post = await getPostById(params.postId);
      if (post != null) {
        final postRef = post.isBroadcast
            ? _broadcastsCollection.doc(params.postId)
            : _postsCollection.doc(params.postId);

        batch.update(postRef, {
          'stats.commentCount': FieldValue.increment(1),
          'comment_stats.total': FieldValue.increment(1),
        });
      }

      await batch.commit();

      // 記錄互動
      await recordPostInteraction(
        postId: params.postId,
        interactionType: 'comment_reply',
        userId: params.userId,
        metadata: {
          'comment_id': commentId,
          'reply_id': replyId,
        },
      );

      // 從 Firestore 重新讀取剛創建的回覆以確保資料一致性
      try {
        await Future.delayed(
            const Duration(milliseconds: 100)); // 短暫延遲確保 Firestore 寫入完成
        final replyDoc = await _commentsCollection.doc(replyId).get();
        if (replyDoc.exists) {
          final data = replyDoc.data() as Map<String, dynamic>;
          return Comment(
            id: data['id'] as String,
            postId: data['post_id'] as String,
            content: data['content'] as String,
            authorId: data['author_identifier'] as String? ??
                data['author_id'] as String,
            authorName: data['author_name'] as String,
            createdAt: (data['created_at'] as Timestamp).toDate(),
            parentId: data['parent_id'] as String?,
            likeCount: (data['like_count'] as num?)?.toInt() ?? 0,
            replyCount: (data['reply_count'] as num?)?.toInt() ?? 0,
            isEdited: data['is_edited'] as bool? ?? false,
            editedAt: data['edited_at'] != null
                ? (data['edited_at'] as Timestamp).toDate()
                : null,
            imageUrl: data['image_url'] as String?,
          );
        }
      } catch (e) {
        print('⚠️ 無法重新讀取回覆，使用備用資料: $e');
      }

      // 備用返回（如果重新讀取失敗）
      return Comment(
        id: replyId,
        postId: params.postId,
        content: params.content,
        authorId: authorIdentifier, // 使用處理過的 authorIdentifier
        authorName: authorName, // 使用處理過的 authorName
        authorAvatarUrl: authorAvatarUrl, // 使用處理過的 authorAvatarUrl
        createdAt: DateTime.now(),
        parentId: commentId,
        imageUrl: params.imageUrl,
      );
    } catch (error) {
      throw Exception('add_comment_reply_failed: $error');
    }
  }

  @override
  Future<CommentStats> getCommentStats(String postId) async {
    try {
      final commentsSnapshot =
          await _commentsCollection.where('post_id', isEqualTo: postId).get();

      int totalComments = 0;
      int totalReplies = 0;
      int totalLikes = 0;

      for (final doc in commentsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final isReply = data['parent_id'] != null;

        if (isReply) {
          totalReplies++;
        } else {
          totalComments++;
        }

        totalLikes += (data['like_count'] as num?)?.toInt() ?? 0;
      }

      return CommentStats(
        totalComments: totalComments,
        totalReplies: totalReplies,
        totalLikes: totalLikes,
      );
    } catch (error) {
      Logger.e('Get comment stats failed: $error', 'HomeRepository');
      return const CommentStats();
    }
  }

  @override
  Future<List<Comment>> getUserComments(String userId,
      {int limit = 20, int offset = 0}) async {
    try {
      // 獲取用戶的匿名hash，用於排除匿名留言
      String? userAnonymousHash;
      try {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        if (userDoc.exists) {
          userAnonymousHash = userDoc.data()?['anonymousHash'] as String?;
          Logger.d('用戶匿名hash: $userAnonymousHash', 'HomeRepository');
        }
      } catch (e) {
        Logger.w('獲取用戶匿名hash失敗: $e', 'HomeRepository');
      }

      // 查詢該用戶的所有留言（包括匿名和非匿名）
      Query query = _commentsCollection
          .where('author_id', isEqualTo: userId)
          .orderBy('created_at', descending: true)
          .limit(limit);

      // 使用 startAfter 實現高效分頁（不再使用 offset）
      // offset 參數已廢棄，請使用適當的 lastDocumentId 進行分頁

      final snapshot = await query.get();
      final comments = <Comment>[];

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // 檢查是否為匿名留言
        final authorIdentifier =
            data['author_identifier'] as String? ?? data['author_id'] as String;

        // 過濾匿名留言：如果 author_identifier 等於用戶的匿名hash，則為匿名留言
        if (userAnonymousHash != null &&
            authorIdentifier == userAnonymousHash) {
          continue; // 跳過匿名留言
        }

        final comment = Comment(
          id: data['id'] as String,
          postId: data['post_id'] as String,
          content: data['content'] as String,
          authorId: authorIdentifier,
          authorName: data['author_name'] as String,
          createdAt: (data['created_at'] as Timestamp).toDate(),
          parentId: data['parent_id'] as String?,
          likeCount: (data['like_count'] as num?)?.toInt() ?? 0,
          replyCount: (data['reply_count'] as num?)?.toInt() ?? 0,
          isEdited: data['is_edited'] as bool? ?? false,
          editedAt: data['edited_at'] != null
              ? (data['edited_at'] as Timestamp).toDate()
              : null,
          imageUrl: data['image_url'] as String?,
        );

        comments.add(comment);
      }

      return comments;
    } catch (error) {
      Logger.e('Get user comments failed: $error', 'HomeRepository');
      return [];
    }
  }

  // ========================================
  // 📱 本地快取同步
  // ========================================

  /// 同步用戶互動狀態到本地快取（登入時調用）
  Future<void> syncUserInteractions(String userId) async {
    try {
      print('🔄 Repository: Starting user interactions sync for user: $userId');
      final cacheService = InteractionCacheService.instance;

      // 並行獲取所有互動資料
      final futures = await Future.wait([
        _getUserLikedPosts(userId),
        _getUserFavoritePosts(userId),
        _getUserLikedComments(userId),
      ]);

      final likedPosts = futures[0] as Set<String>;
      final favoritePosts = futures[1] as Set<String>;
      final likedComments = futures[2] as Set<String>;

      // 更新快取
      await Future.wait([
        cacheService.setLikedPosts(likedPosts),
        cacheService.setFavoritePosts(favoritePosts),
        cacheService.setLikedComments(likedComments),
        cacheService.setLastSyncTime(DateTime.now()),
      ]);

      print('✅ Repository: User interactions sync completed');
      print('   - Liked Posts: ${likedPosts.length}');
      print('   - Favorite Posts: ${favoritePosts.length}');
      print('   - Liked Comments: ${likedComments.length}');
    } catch (error) {
      Logger.e('Sync user interactions failed: $error', 'HomeRepository');
      print('❌ Repository: Failed to sync user interactions: $error');
    }
  }

  /// 獲取用戶按讚的貼文
  Future<Set<String>> _getUserLikedPosts(String userId) async {
    try {
      final likesSnapshot = await _likesCollection
          .where('user_id', isEqualTo: userId)
          .limit(1000) // 限制數量避免過大
          .get();

      final likedPosts = <String>{};
      for (final doc in likesSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final postId = data['post_id'] as String?;
        if (postId != null) {
          likedPosts.add(postId);
        }
      }

      return likedPosts;
    } catch (error) {
      Logger.e('Get user liked posts failed: $error', 'HomeRepository');
      return {};
    }
  }

  /// 獲取用戶收藏的貼文
  Future<Set<String>> _getUserFavoritePosts(String userId) async {
    try {
      final favoritesSnapshot = await _favoritesCollection
          .where('user_id', isEqualTo: userId)
          .limit(1000) // 限制數量避免過大
          .get();

      final favoritePosts = <String>{};
      for (final doc in favoritesSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final postId = data['post_id'] as String?;
        if (postId != null) {
          favoritePosts.add(postId);
        }
      }

      return favoritePosts;
    } catch (error) {
      Logger.e('Get user favorite posts failed: $error', 'HomeRepository');
      return {};
    }
  }

  /// 獲取用戶按讚的留言
  Future<Set<String>> _getUserLikedComments(String userId) async {
    try {
      // 這裡需要一個 comment_likes 集合來追蹤留言按讚
      // 類似於 likes 集合的結構
      final commentLikesSnapshot = await _firestore
          .collection('comment_likes')
          .where('user_id', isEqualTo: userId)
          .limit(1000)
          .get();

      final likedComments = <String>{};
      for (final doc in commentLikesSnapshot.docs) {
        final data = doc.data();
        final commentId = data['comment_id'] as String?;
        if (commentId != null) {
          likedComments.add(commentId);
        }
      }

      return likedComments;
    } catch (error) {
      Logger.e('Get user liked comments failed: $error', 'HomeRepository');
      return {};
    }
  }

  // ========================================
  // 🛠️ 工具方法
  // ========================================

  /// 生成匿名用戶識別符
  String _generateAnonymousHash(String userId) {
    return 'anon_${userId.hashCode.abs().toString().padLeft(8, '0')}';
  }

  /// 🌍 取得國家顯示名稱（將代碼轉換為友好名稱）
  String? _getCountryDisplayName(String? countryCode, String? countryName) {
    if (countryCode == null) return countryName;

    // 優先使用自定義映射表
    const countryMapping = {
      'TW': 'Taiwan',
      'CN': 'China',
      'US': 'United States',
      'JP': 'Japan',
      'KR': 'South Korea',
      'SG': 'Singapore',
      'MY': 'Malaysia',
      'TH': 'Thailand',
      'VN': 'Vietnam',
      'PH': 'Philippines',
      'ID': 'Indonesia',
      'GB': 'United Kingdom',
      'AU': 'Australia',
      'CA': 'Canada',
      'DE': 'Germany',
      'FR': 'France',
      'IT': 'Italy',
      'ES': 'Spain',
      'NL': 'Netherlands',
      'BR': 'Brazil',
      'MX': 'Mexico',
      'IN': 'India',
      'RU': 'Russia',
    };

    // 如果在映射表中找到，使用映射值
    if (countryMapping.containsKey(countryCode)) {
      return countryMapping[countryCode];
    }

    // 否則使用countryName，如果沒有則使用原始countryCode
    return countryName ?? countryCode;
  }

  // ========================================
  // 🎯 活動記錄相關私有方法
  // ========================================

  /// 獲取活動服務實例（懶加載）
  ActivityService _getActivityService() {
    _activityService ??= ActivityService(ActivityRepositoryImpl());
    return _activityService!;
  }

  /// 記錄按讚活動
  void _recordLikeActivity({
    required String postId,
    required String targetUserId,
    required String triggerUserId,
    required bool isAnonymous,
  }) {
    // 異步執行，不影響主流程
    Future.microtask(() async {
      try {
        final activityService = _getActivityService();

        // 獲取觸發用戶的名稱
        String? triggerUserName;
        if (!isAnonymous) {
          final userDoc =
              await _firestore.collection('users').doc(triggerUserId).get();
          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            triggerUserName = userData['displayName'] as String?;
          }
        }

        await activityService.recordLikeActivity(
          targetUserId: targetUserId,
          postId: postId,
          triggerUserId: triggerUserId,
          triggerUserName: triggerUserName,
          isAnonymous: isAnonymous,
        );
      } catch (error) {
        Logger.w('記錄按讚活動失敗: $error', 'HomeRepository');
      }
    });
  }

  /// 記錄留言活動
  void _recordCommentActivity({
    required String postId,
    required String commentId,
    required String targetUserId,
    required String triggerUserId,
    required bool isAnonymous,
    String? commentPreview,
  }) {
    // 異步執行，不影響主流程
    Future.microtask(() async {
      try {
        final activityService = _getActivityService();

        // 獲取觸發用戶的名稱
        String? triggerUserName;
        if (!isAnonymous) {
          final userDoc =
              await _firestore.collection('users').doc(triggerUserId).get();
          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            triggerUserName = userData['displayName'] as String?;
          }
        }

        await activityService.recordCommentActivity(
          targetUserId: targetUserId,
          postId: postId,
          commentId: commentId,
          triggerUserId: triggerUserId,
          triggerUserName: triggerUserName,
          isAnonymous: isAnonymous,
          commentPreview: commentPreview,
        );
      } catch (error) {
        Logger.w('記錄留言活動失敗: $error', 'HomeRepository');
      }
    });
  }

  /// 記錄收藏活動
  void _recordBookmarkActivity({
    required String postId,
    required String targetUserId,
    required String triggerUserId,
    required bool isAnonymous,
  }) {
    // 異步執行，不影響主流程
    Future.microtask(() async {
      try {
        final activityService = _getActivityService();

        // 獲取觸發用戶的名稱
        String? triggerUserName;
        if (!isAnonymous) {
          final userDoc =
              await _firestore.collection('users').doc(triggerUserId).get();
          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            triggerUserName = userData['displayName'] as String?;
          }
        }

        await activityService.recordBookmarkActivity(
          targetUserId: targetUserId,
          postId: postId,
          triggerUserId: triggerUserId,
          triggerUserName: triggerUserName,
          isAnonymous: isAnonymous,
        );
      } catch (error) {
        Logger.w('記錄收藏活動失敗: $error', 'HomeRepository');
      }
    });
  }

  /// 解析留言連結預覽
  List<LinkPreview> _parseCommentLinkPreviews(dynamic linkPreviewsData) {
    if (linkPreviewsData == null) return [];

    if (linkPreviewsData is List) {
      return linkPreviewsData
          .map((data) =>
              LinkPreviewModel.fromFirestore(data as Map<String, dynamic>)
                  .toDomain())
          .toList();
    }

    return [];
  }
}

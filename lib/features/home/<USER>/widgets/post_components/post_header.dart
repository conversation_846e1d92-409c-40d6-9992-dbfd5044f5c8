import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';

import '../../../../../shared/widgets/app_widgets.dart';
import '../../../domain/entities/post.dart';
import '../../providers/home_provider.dart';
import '../../../../../core/utils/distance_calculator.dart';
import '../../../../explore/domain/entities/user_reputation.dart';

/// 📝 貼文頭部組件 - 包含頭像、名稱、時間戳、選項菜單
class PostHeader extends ConsumerWidget {
  final Post post;
  final VoidCallback? onMoreOptions;

  const PostHeader({
    super.key,
    required this.post,
    this.onMoreOptions,
  });

  /// 🏆 根據聲望值計算階級
  TitleType _getTitleTypeFromReputation(int? reputation) {
    if (reputation == null || reputation < 0) return TitleType.newbie;

    if (reputation >= 1000) return TitleType.mythic; // 傳說 (1000+)
    if (reputation >= 500) return TitleType.legend; // 傳奇 (500-999)
    if (reputation >= 200) return TitleType.master; // 大師 (200-499)
    if (reputation >= 50) return TitleType.expert; // 達人 (50-199)
    if (reputation >= 10) return TitleType.resident; // 居民 (10-49)
    return TitleType.newbie; // 新芽 (0-9)
  }

  /// 🎨 獲取階級圖示
  String _getTitleIcon(TitleType titleType) {
    switch (titleType) {
      case TitleType.newbie:
        return '🌱';
      case TitleType.resident:
        return '🌿';
      case TitleType.expert:
        return '⭐';
      case TitleType.master:
        return '💫';
      case TitleType.legend:
        return '🔥';
      case TitleType.mythic:
        return '👑';
    }
  }

  /// 🌈 獲取階級顏色
  Color _getTitleColor(BuildContext context, TitleType titleType) {
    final theme = Theme.of(context);
    switch (titleType) {
      case TitleType.newbie:
        return Colors.grey;
      case TitleType.resident:
        return Colors.green;
      case TitleType.expert:
        return Colors.blue;
      case TitleType.master:
        return Colors.purple;
      case TitleType.legend:
        return Colors.orange;
      case TitleType.mythic:
        return Colors.amber;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final author = post.author;

    return Padding(
      padding: const EdgeInsets.fromLTRB(8, 16, 8, 8),
      child: Row(
        children: [
          // 用戶大頭照（可點擊）
          GestureDetector(
            onTap: () => _handleAvatarTap(context, author),
            child: CircleAvatar(
              radius: 20,
              backgroundImage: author.photoURL != null && !author.isAnonymous
                  ? NetworkImage(author.photoURL!)
                  : null,
              backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.2),
              child: author.photoURL == null || author.isAnonymous
                  ? Icon(
                      Icons.person,
                      color: theme.colorScheme.primary,
                    )
                  : null,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // 用戶名稱（可點擊）
                    GestureDetector(
                      onTap: () => _handleUserNameTap(context, author),
                      child: Text(
                        author.displayName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: author.isAnonymous
                              ? theme.colorScheme.secondary
                              : theme.colorScheme.primary,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // 階級圖示（非匿名用戶）
                    if (!author.isAnonymous && author.reputation != null)
                      _buildReputationBadge(context, author.reputation!),
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    AppText(
                      _formatTimestamp(post.createdAt),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    // 距離顯示
                    _buildDistanceDisplay(context, ref, theme),
                    // 移除重複的階級顯示（階級圖示已經在名稱旁邊顯示了）
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: onMoreOptions,
            icon: Icon(
              Icons.more_vert,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  /// 處理大頭照點擊
  void _handleAvatarTap(BuildContext context, PostAuthor author) {
    // 只有非匿名用戶且有大頭照時才顯示
    if (!author.isAnonymous && author.photoURL != null) {
      showDialog(
        context: context,
        builder: (context) => Dialog(
          backgroundColor: Colors.transparent,
          child: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  author.photoURL!,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.error,
                        color: Theme.of(context).colorScheme.error,
                        size: 48,
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      );
    }
  }

  /// 處理用戶名稱點擊
  void _handleUserNameTap(BuildContext context, PostAuthor author) {
    debugPrint('🔍 PostHeader: 點擊用戶名稱');
    debugPrint('🔍 PostHeader: author.isAnonymous = ${author.isAnonymous}');
    debugPrint('🔍 PostHeader: author.userHash = "${author.userHash}"');
    debugPrint('🔍 PostHeader: author.displayName = "${author.displayName}"');
    debugPrint('🔍 PostHeader: author.userId = "${author.userId}"');

    if (author.isAnonymous) {
      // 匿名用戶：跳轉到匿名用戶文章列表
      final route =
          '/anonymous-posts/${author.userHash}?displayName=${Uri.encodeQueryComponent(author.displayName)}';
      debugPrint('🔍 PostHeader: 準備跳轉到匿名用戶頁面: $route');

      try {
        context.push(route);
        debugPrint('✅ PostHeader: 匿名用戶頁面跳轉成功');
      } catch (e) {
        debugPrint('❌ PostHeader: 匿名用戶頁面跳轉失敗: $e');
      }
    } else {
      // 非匿名用戶：跳轉到用戶資訊頁面
      final route = '/user/${author.userId}';
      debugPrint('🔍 PostHeader: 準備跳轉到用戶資訊頁面: $route');

      try {
        context.push(route);
        debugPrint('✅ PostHeader: 用戶資訊頁面跳轉成功');
      } catch (e) {
        debugPrint('❌ PostHeader: 用戶資訊頁面跳轉失敗: $e');
      }
    }
  }

  /// 格式化時間戳
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now'.tr();
    }
  }

  /// 建立距離顯示組件
  /// 🏆 建立聲望階級圖示組件
  Widget _buildReputationBadge(BuildContext context, int reputation) {
    final titleType = _getTitleTypeFromReputation(reputation);
    final icon = _getTitleIcon(titleType);
    final color = _getTitleColor(context, titleType);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        icon,
        style: TextStyle(
          fontSize: 12,
          height: 1.0,
        ),
      ),
    );
  }

  Widget _buildDistanceDisplay(
      BuildContext context, WidgetRef ref, ThemeData theme) {
    // 獲取用戶當前位置
    final locationState = ref.watch(locationProvider);

    // 如果沒有用戶位置資訊，不顯示距離
    if (!locationState.hasLocation) {
      return const SizedBox.shrink();
    }

    final userLat = locationState.latitude!;
    final userLon = locationState.longitude!;
    final postLat = post.location.latitude;
    final postLon = post.location.longitude;

    // 計算距離
    final distance = DistanceCalculator.calculateAndFormatDistance(
      userLat,
      userLon,
      postLat,
      postLon,
    );

    return Row(
      children: [
        const SizedBox(width: 8),
        Icon(
          Icons.location_on,
          size: 12,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        const SizedBox(width: 2),
        Text(
          distance,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_picker/image_picker.dart';
import 'package:go_router/go_router.dart';
import 'dart:io';

import '../../../../../core/utils/haptic_feedback_utils.dart';
import '../../../../../shared/animations/animation_manager_mixin.dart';
import '../../../data/models/post_model.dart';
import '../../../data/models/comment_model.dart';
import '../../../data/services/comment_service.dart';
import '../../../data/services/typing_indicator_service.dart';
import '../../../data/strategies/comment_embedding_strategy.dart';
import '../../../../notifications/data/services/notification_trigger_service.dart';
import 'comment_item.dart';

/// 🔄 實時留言區域組件
class RealTimeCommentsSection extends StatefulWidget {
  final PostModel post;
  final Function(String, File?, bool) onCommentAdded; // 添加 isAnonymous 參數

  const RealTimeCommentsSection({
    Key? key,
    required this.post,
    required this.onCommentAdded,
  }) : super(key: key);

  @override
  State<RealTimeCommentsSection> createState() =>
      _RealTimeCommentsSectionState();
}

class _RealTimeCommentsSectionState extends State<RealTimeCommentsSection>
    with
        TickerProviderStateMixin,
        AnimationLifecycleMixin,
        AutomaticKeepAliveClientMixin {
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final CommentService _commentService = CommentService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final ImagePicker _imagePicker = ImagePicker();

  // 快取當前用戶的 hash
  String? _currentUserPublicHash;
  String? _currentUserAnonymousHash;

  bool _isSubmitting = false;
  List<CommentModel> _displayComments = [];
  Map<String, int> _commentFloorMap = {}; // 留言ID到樓層的映射
  CommentSortType _currentSortType = CommentSortType.newest;
  String? _replyToCommentId;
  String? _replyToCommentAuthor;
  String? _highlightedCommentId;
  File? _selectedImage;
  bool _isAnonymousComment = false; // 匿名留言開關

  // 輸入指示器動畫控制器
  late AnimationController _typingDotsController;
  late Animation<double> _dot1Animation;
  late Animation<double> _dot2Animation;
  late Animation<double> _dot3Animation;

  @override
  void initState() {
    super.initState();
    _updateDisplayComments();
    _loadCurrentUserHashes(); // 載入當前用戶的 hash

    // 設置動畫
    setupEntranceAnimations();
    registerAnimation(
      name: 'newComment',
      duration: const Duration(milliseconds: 300),
      type: AnimationType.oneShot,
    );

    // 初始化輸入指示器動畫
    _typingDotsController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _dot1Animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingDotsController,
      curve: const Interval(0.0, 0.3, curve: Curves.easeInOut),
    ));

    _dot2Animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingDotsController,
      curve: const Interval(0.2, 0.5, curve: Curves.easeInOut),
    ));

    _dot3Animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingDotsController,
      curve: const Interval(0.4, 0.7, curve: Curves.easeInOut),
    ));

    // 監聽輸入框焦點變化
    _commentController.addListener(_handleTypingStatusChange);
  }

  @override
  void didUpdateWidget(RealTimeCommentsSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 檢測新留言並滾動到底部
    if (oldWidget.post.comments.length < widget.post.comments.length) {
      _updateDisplayComments();
      _scrollToBottom();

      // 新留言動畫提示
      _showNewCommentAnimation();
    } else {
      _updateDisplayComments();
    }
  }

  @override
  void dispose() {
    _commentController.removeListener(_handleTypingStatusChange);
    _commentController.dispose();
    _scrollController.dispose();
    _typingDotsController.dispose();
    // 確保停止輸入狀態
    typingIndicatorService.stopTyping();
    super.dispose();
  }

  void _updateDisplayComments() {
    _displayComments = _commentService.getVisibleComments(widget.post,
        sortType: _currentSortType);
    _updateFloorMapping();
  }

  /// 載入當前用戶的 hash
  Future<void> _loadCurrentUserHashes() async {
    final currentUser = _auth.currentUser;
    if (currentUser != null) {
      try {
        final userDoc =
            await _firestore.collection('users').doc(currentUser.uid).get();

        if (userDoc.exists && mounted) {
          final userData = userDoc.data() as Map<String, dynamic>;
          setState(() {
            _currentUserPublicHash = userData['publicHash'];
            _currentUserAnonymousHash = userData['anonymousHash'];
          });
        }
      } catch (e) {
        debugPrint('Failed to load user hashes: $e');
      }
    }
  }

  /// 獲取當前用戶對應留言的正確ID（用於權限檢查）
  String _getCurrentUserIdForComment(CommentModel comment) {
    // 如果沒有當前用戶的hash，返回空字串
    if (_currentUserPublicHash == null || _currentUserAnonymousHash == null) {
      return '';
    }

    // 檢查留言的 authorId 是否匹配當前用戶的任一 hash
    if (comment.authorId == _currentUserPublicHash) {
      return _currentUserPublicHash!;
    } else if (comment.authorId == _currentUserAnonymousHash) {
      return _currentUserAnonymousHash!;
    }

    // 如果都不匹配，返回空字串（表示不是當前用戶的留言）
    return '';
  }

  /// 從 hash 反查 Firebase uid
  Future<String?> _getFirebaseUidFromHash(String hash) async {
    try {
      // 查詢 users 集合，找到 publicHash 匹配的用戶
      final querySnapshot = await _firestore
          .collection('users')
          .where('publicHash', isEqualTo: hash)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs.first.id; // 返回文檔 ID（即 Firebase uid）
      }

      return null;
    } catch (e) {
      debugPrint('從 hash 反查 Firebase uid 失敗: $e');
      return null;
    }
  }

  void _updateFloorMapping() {
    // 根據當前排序建立樓層映射
    _commentFloorMap.clear();
    for (int i = 0; i < _displayComments.length; i++) {
      _commentFloorMap[_displayComments[i].id] = i + 1; // 樓層從1開始
    }
  }

  void _changeSortType(CommentSortType newSortType) {
    if (_currentSortType != newSortType) {
      setState(() {
        _currentSortType = newSortType;
        _updateDisplayComments();
      });
      HapticFeedbackUtils.lightImpact();
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showNewCommentAnimation() {
    // 觸覺反饋
    HapticFeedbackUtils.light();

    // 播放動畫
    if (hasAnimation('newComment')) {
      resetAnimation('newComment');
      startAnimation('newComment');
    }
  }

  void _handleTypingStatusChange() {
    final text = _commentController.text.trim();

    if (text.isNotEmpty) {
      // 開始輸入
      typingIndicatorService.startTyping(widget.post.postId);
    } else {
      // 停止輸入
      typingIndicatorService.stopTyping();
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final commentStatus = _commentService.getCommentStatus(widget.post);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 留言統計和狀態
        _buildCommentHeader(commentStatus),

        // 留言列表
        if (_displayComments.isEmpty)
          _buildEmptyState()
        else
          ..._displayComments.map((comment) {
            final commentFloor = _commentFloorMap[comment.id] ?? 0; // 使用動態樓層映射
            final isReply = comment.parentId != null;

            // 找到回覆的目標留言樓層和作者名稱
            String? replyToAuthorName;
            int? replyToFloor;
            if (isReply && comment.parentId != null) {
              replyToFloor = _commentFloorMap[comment.parentId];
              // 找到被回覆的留言
              final parentComment = widget.post.comments.firstWhere(
                (c) => c.id == comment.parentId,
                orElse: () => comment,
              );
              replyToAuthorName = parentComment.authorName;
            }

            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              decoration: BoxDecoration(
                color: _highlightedCommentId == comment.id
                    ? Theme.of(context)
                        .colorScheme
                        .primaryContainer
                        .withOpacity(0.3)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              margin: EdgeInsets.only(
                left: isReply ? 32.0 : 0, // 回覆留言縮排
                bottom: 4.0,
              ),
              child: CommentItem(
                key: ValueKey(comment.id),
                comment: comment,
                // 修復：檢查當前用戶是否為留言作者
                currentUserId: _getCurrentUserIdForComment(comment),
                isReply: isReply,
                floor: commentFloor, // 每個留言都有樓層
                replyToCommentIndex: replyToFloor,
                replyToAuthorName: replyToAuthorName,
                onReply: isReply ? (_) {} : _handleReply, // 回覆不能再回覆
                onEdit: _handleEdit,
                onDelete: _handleDelete,
                onLike: _handleLike,
                onReplyToFloorTap: _handleJumpToComment,
                onAuthorTap: _handleCommentAuthorTap, // 添加作者點擊處理
              ),
            );
          }).toList(),

        // 輸入指示器
        if (widget.post.stats.typingUsers.isNotEmpty &&
            !widget.post.stats.typingUsers
                .contains(_auth.currentUser?.uid)) ...[
          _buildTypingIndicator(),
        ],

        // 大小警告 - 移到輸入區之前以避免被鍵盤遮擋
        if (commentStatus['shouldWarn'] == true) ...[
          _buildSizeWarning(commentStatus),
        ],

        // 留言輸入區
        if (commentStatus['canComment'] == true) ...[
          _buildCommentInput(),
        ] else ...[
          _buildCommentsClosedMessage(),
        ],
      ],
    );
  }

  Widget _buildCommentHeader(Map<String, dynamic> status) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                tr('post.comments_count')
                    .replaceAll('{0}', status['totalComments'].toString()),
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const Spacer(),
              if (status['hasOverflow'] == true) ...[
                const SizedBox(width: 8),
                Tooltip(
                  message: tr('post.comments_overflow_tooltip'),
                  child: Icon(
                    Icons.warning_amber,
                    size: 16,
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),
          // 排序選擇器
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildSortChip(
                label: tr('comments.sort.newest'),
                sortType: CommentSortType.newest,
                icon: Icons.access_time,
              ),
              const SizedBox(width: 8),
              _buildSortChip(
                label: tr('comments.sort.oldest'),
                sortType: CommentSortType.oldest,
                icon: Icons.history,
              ),
              const SizedBox(width: 8),
              _buildSortChip(
                label: tr('comments.sort.popular'),
                sortType: CommentSortType.popular,
                icon: Icons.local_fire_department,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortChip({
    required String label,
    required CommentSortType sortType,
    required IconData icon,
  }) {
    final isSelected = _currentSortType == sortType;
    return GestureDetector(
      onTap: () {
        debugPrint('Sort chip tapped: $sortType');
        _changeSortType(sortType);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primaryContainer
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor,
            width: isSelected ? 1.5 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            tr('post.no_comments'),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
          ),
          const SizedBox(height: 8),
          Text(
            tr('post.be_first_comment'),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_replyToCommentAuthor != null) ...[
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.reply,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      tr('comments.actions.replying_to',
                          args: [_replyToCommentAuthor!]),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: _cancelReply,
                      child: Icon(
                        Icons.close,
                        size: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // 顯示選中的圖片
            if (_selectedImage != null) ...[
              const SizedBox(height: 8),
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _selectedImage!,
                      height: 100,
                      width: 100,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _selectedImage = null;
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.all(4),
                        child: const Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            // 匿名開關行
            Row(
              children: [
                Icon(
                  _isAnonymousComment ? Icons.visibility_off : Icons.visibility,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  _isAnonymousComment
                      ? tr('comments.anonymous_on')
                      : tr('comments.anonymous_off'),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const Spacer(),
                Switch(
                  value: _isAnonymousComment,
                  onChanged: (value) {
                    setState(() {
                      _isAnonymousComment = value;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                // 圖片選擇按鈕
                IconButton(
                  onPressed: _isSubmitting ? null : _pickImage,
                  icon: Icon(
                    Icons.image,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  tooltip: tr('comments.actions.add_image'),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    maxLength: CommentEmbeddingStrategy.maxCommentLength,
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _handleSubmitComment(),
                    decoration: InputDecoration(
                      hintText: _replyToCommentAuthor != null
                          ? tr('comments.actions.reply_hint')
                          : tr('post.add_comment_hint'),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      counterText: '', // 隱藏字數統計
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                FloatingActionButton.small(
                  onPressed: _isSubmitting ? null : _handleSubmitComment,
                  child: _isSubmitting
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.send),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsClosedMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Icon(
              Icons.lock_outline,
              size: 20,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                tr('post.comments_closed'),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSizeWarning(Map<String, dynamic> status) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tr('post.size_warning'),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onErrorContainer,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  tr('post.remaining_comments',
                      args: [status['remainingSlots'].toString()]),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onErrorContainer,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSubmitComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty && _selectedImage == null) return;

    setState(() => _isSubmitting = true);

    // 保存暫時的圖片引用（在 try 外定義以便在 catch 中使用）
    final tempImage = _selectedImage;

    try {
      // 清空輸入框和圖片 (樂觀更新)
      _commentController.clear();
      setState(() {
        _selectedImage = null;
      });

      // 獲取當前用戶信息
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // 提交留言（包含圖片和匿名標誌）
      // 注意：通知已經在 comment_service.dart 的 addComment 方法中發送
      await widget.onCommentAdded(content, tempImage, _isAnonymousComment);

      // 回覆通知需要單獨處理（因為 comment_service 只處理一般留言通知）
      if (_replyToCommentId != null) {
        // 回覆通知
        await NotificationTriggerService.sendReplyNotification(
          postId: widget.post.postId,
          parentCommentId: _replyToCommentId!,
          replyId: '', // 將由服務生成
          replierId: currentUser.uid,
          content: content,
        );
      }

      // 清除回覆狀態
      _cancelReply();

      // 觸覺反饋
      HapticFeedbackUtils.success();
    } catch (e) {
      // 錯誤時恢復輸入內容和圖片
      _commentController.text = content;
      setState(() {
        _selectedImage = tempImage;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(tr('post.add_comment_error')),
            action: SnackBarAction(
              label: tr('common.retry'),
              onPressed: _handleSubmitComment,
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _handleReply(CommentModel comment) {
    setState(() {
      _replyToCommentId = comment.id;
      _replyToCommentAuthor = comment.authorName;
    });

    // 聚焦輸入框
    FocusScope.of(context).requestFocus(FocusNode());
  }

  void _cancelReply() {
    setState(() {
      _replyToCommentId = null;
      _replyToCommentAuthor = null;
    });
  }

  Future<void> _pickImage() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        // 不設定 imageQuality 以保留原始品質，支援 GIF 動畫
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImage = File(pickedFile.path);
        });
        HapticFeedbackUtils.lightImpact();
      }
    } catch (e) {
      debugPrint('選擇圖片失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(tr('comments.actions.pick_image_error'))),
        );
      }
    }
  }

  Future<void> _handleEdit(CommentModel comment, String newContent) async {
    try {
      // 修復：傳遞正確的 hash 而不是 Firebase uid
      // 需要根據留言的 authorId 來判斷應該傳遞哪個 hash
      String userIdForEdit = '';

      if (comment.authorId == _currentUserPublicHash) {
        userIdForEdit = _currentUserPublicHash!;
      } else if (comment.authorId == _currentUserAnonymousHash) {
        userIdForEdit = _currentUserAnonymousHash!;
      } else {
        // 如果都不匹配，說明不是當前用戶的留言，不應該能編輯
        throw Exception('Permission denied: Not your comment');
      }

      await _commentService.editComment(
        postId: widget.post.postId,
        commentId: comment.id,
        newContent: newContent,
        userId: userIdForEdit,
      );

      HapticFeedbackUtils.success();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(tr('comment.edit_error'))),
        );
      }
    }
  }

  Future<void> _handleDelete(CommentModel comment) async {
    try {
      // 修復：傳遞正確的 hash 而不是 Firebase uid
      // 需要根據留言的 authorId 來判斷應該傳遞哪個 hash
      String userIdForDelete = '';

      if (comment.authorId == _currentUserPublicHash) {
        userIdForDelete = _currentUserPublicHash!;
      } else if (comment.authorId == _currentUserAnonymousHash) {
        userIdForDelete = _currentUserAnonymousHash!;
      } else {
        // 如果都不匹配，說明不是當前用戶的留言，不應該能刪除
        throw Exception('Permission denied: Not your comment');
      }

      await _commentService.deleteComment(
        postId: widget.post.postId,
        commentId: comment.id,
        userId: userIdForDelete,
      );

      HapticFeedbackUtils.success();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(tr('comment.delete_error'))),
        );
      }
    }
  }

  Future<void> _handleLike(CommentModel comment, bool isLiked) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // 使用從 CommentItem 傳遞過來的點讚狀態
      await _commentService.likeComment(
        postId: widget.post.postId,
        commentId: comment.id,
        userId: currentUser.uid,
        isLiked: isLiked,
      );

      // 發送點讚通知
      await NotificationTriggerService.sendCommentLikeNotification(
        commentId: comment.id,
        likerId: currentUser.uid,
        postId: widget.post.postId,
      );

      HapticFeedbackUtils.lightImpact();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(tr('comment.like_error'))),
        );
      }
    }
  }

  void _handleJumpToComment(int floor) {
    // 從樓層映射中找到對應的留言ID
    String? targetCommentId;
    int targetIndex = -1;

    for (var entry in _commentFloorMap.entries) {
      if (entry.value == floor) {
        targetCommentId = entry.key;
        // 找到在當前排序中的索引
        targetIndex =
            _displayComments.indexWhere((c) => c.id == targetCommentId);
        break;
      }
    }

    if (targetCommentId != null && targetIndex >= 0) {
      // 設置高亮 - 使用留言ID
      setState(() {
        _highlightedCommentId = targetCommentId;
      });

      // 觸覺反饋
      HapticFeedbackUtils.lightImpact();

      // 使用 PostDetailPage 的 CustomScrollView 來滾動
      WidgetsBinding.instance.addPostFrameCallback((_) {
        try {
          // 獲取最近的 Scrollable
          final scrollableState = Scrollable.of(context);
          final position = scrollableState.position;

          // 計算目標留言的大概位置
          // 考慮到 PostCard 和其他元素的高度
          final postCardHeight = 300.0; // 預估 PostCard 高度
          final headerHeight = 120.0; // 留言頭部高度（含排序選擇器）
          final commentHeight = 150.0; // 預估每個留言高度
          final targetOffset =
              postCardHeight + headerHeight + (targetIndex * commentHeight);

          // 滾動到目標位置
          position.animateTo(
            targetOffset,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        } catch (e) {
          // 如果滾動失敗，至少高亮效果還在
          print('Failed to scroll to comment: $e');
        }
      });

      // 3秒後取消高亮
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _highlightedCommentId = null;
          });
        }
      });
    }
  }

  /// 處理點擊留言作者（頭像或名稱）
  void _handleCommentAuthorTap(String authorId, String authorName) async {
    // 使用前綴 "ANON_" 來判斷是否匿名
    final isAnonymous = authorId.startsWith('ANON_');

    if (isAnonymous) {
      // 匿名留言者：跳轉到該匿名用戶的所有文章列表
      if (mounted) {
        context.push(
            '/anonymous-posts/$authorId/${Uri.encodeComponent(authorName)}');
      }
    } else {
      // 非匿名留言者：需要從 hash 反查 Firebase uid
      try {
        final firebaseUid = await _getFirebaseUidFromHash(authorId);
        if (mounted) {
          if (firebaseUid != null) {
            // 跳轉到用戶資料頁面
            context.push('/user/$firebaseUid');
          } else {
            // 如果找不到對應的 Firebase uid，顯示錯誤訊息
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('找不到該用戶資料')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('載入用戶資料失敗')),
          );
        }
      }
    }
  }

  Widget _buildTypingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          SizedBox(
            width: 40,
            height: 24,
            child: AnimatedBuilder(
              animation: _typingDotsController,
              builder: (context, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildDot(_dot1Animation),
                    const SizedBox(width: 4),
                    _buildDot(_dot2Animation),
                    const SizedBox(width: 4),
                    _buildDot(_dot3Animation),
                  ],
                );
              },
            ),
          ),
          const SizedBox(width: 8),
          Text(
            tr('post.someone_typing'),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  fontStyle: FontStyle.italic,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildDot(Animation<double> animation) {
    return Transform.translate(
      offset: Offset(0, -4 * animation.value),
      child: Container(
        width: 6,
        height: 6,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.6),
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}

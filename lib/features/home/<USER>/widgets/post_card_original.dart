import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'link_preview_card.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../../shared/widgets/app_widgets.dart';
import '../../../../core/auth/auth_guard.dart';
import '../../../../features/auth/presentation/providers/auth_provider.dart';
import '../../../admin/domain/services/admin_permission_service.dart';
import '../../domain/entities/post.dart';
import '../../domain/entities/report.dart';
import '../providers/home_provider.dart';
import '../providers/post_interaction_provider.dart';
import '../providers/crud_provider.dart';
import '../screens/create_post_screen.dart';
import 'delete_confirmation_dialog.dart';
import 'video_thumbnail.dart';
import 'enhanced_video_player.dart';
import 'video_player_widget.dart';
import 'report_dialog.dart';
import 'media_viewer.dart';
import '../../../../core/utils/distance_calculator.dart';

/// 📝 貼文卡片組件
class PostCard extends ConsumerStatefulWidget {
  final Post post;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool showActions;
  final VoidCallback? onCommentTap; // 新增：自定義留言按鈕回調

  const PostCard({
    super.key,
    required this.post,
    this.onTap,
    this.onLongPress,
    this.showActions = true,
    this.onCommentTap, // 新增：自定義留言按鈕回調
  });

  @override
  ConsumerState<PostCard> createState() => _PostCardState();
}

class _PostCardState extends ConsumerState<PostCard>
    with TickerProviderStateMixin {
  late AnimationController _likeAnimationController;
  late Animation<double> _likeScaleAnimation;
  late AnimationController _bookmarkAnimationController;
  late Animation<double> _bookmarkScaleAnimation;

  bool _isLiked = false;
  bool _isExpanded = false;
  bool _isPlaying = false;
  bool _isBookmarking = false; // 收藏處理中狀態
  int? _localLikeCount; // 本地點讚計數
  bool _isShared = false; // 本地分享狀態
  int? _localShareCount; // 本地分享計數

  @override
  void initState() {
    super.initState();

    // 初始化按讚動畫
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _likeScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _likeAnimationController,
      curve: Curves.elasticOut,
    ));

    // 初始化收藏動畫
    _bookmarkAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _bookmarkScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _bookmarkAnimationController,
      curve: Curves.elasticOut,
    ));

    // 檢查初始按讚狀態
    _checkInitialLikeState();

    // 檢查初始分享狀態
    _checkInitialShareState();

    // 記錄貼文查看
    _recordPostView();
  }

  @override
  void dispose() {
    _likeAnimationController.dispose();
    _bookmarkAnimationController.dispose();
    super.dispose();
  }

  /// 檢查初始按讚狀態
  void _checkInitialLikeState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final interactionState = ref.read(postInteractionProvider);
        final isLiked =
            interactionState.likedPosts.contains(widget.post.postId);
        if (_isLiked != isLiked) {
          setState(() {
            _isLiked = isLiked;
          });
        }
      }
    });
  }

  /// 檢查初始分享狀態
  void _checkInitialShareState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final interactionState = ref.read(postInteractionProvider);
        final isShared =
            interactionState.sharedPosts.contains(widget.post.postId);
        if (_isShared != isShared) {
          setState(() {
            _isShared = isShared;
          });
        }
      }
    });
  }

  /// 記錄貼文查看
  void _recordPostView() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final provider = ref.read(postInteractionProvider.notifier);
        provider.viewPost(widget.post.postId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // 監聽點讚狀態變化並同步本地狀態
    final isLikedFromProvider = ref.watch(postInteractionProvider
        .select((state) => state.likedPosts.contains(widget.post.postId)));

    if (_isLiked != isLikedFromProvider) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isLiked = isLikedFromProvider;
          });
        }
      });
    }

    // 監聽分享狀態變化並同步本地狀態
    final isSharedFromProvider = ref.watch(postInteractionProvider
        .select((state) => state.sharedPosts.contains(widget.post.postId)));

    if (_isShared != isSharedFromProvider) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isShared = isSharedFromProvider;
          });
        }
      });
    }

    return GestureDetector(
      onTap: _handleTap,
      onLongPress: _handleLongPress,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            Flexible(child: _buildContent()),
            if (widget.showActions) _buildActions(),
          ],
        ),
      ),
    );
  }

  /// 處理用戶名稱點擊
  void _handleUserNameTap() {
    final author = widget.post.author;

    print('🔍 PostCard: 點擊用戶名稱');
    print('🔍 PostCard: author.isAnonymous = ${author.isAnonymous}');
    print('🔍 PostCard: author.userHash = "${author.userHash}"');
    print('🔍 PostCard: author.displayName = "${author.displayName}"');
    print('🔍 PostCard: author.userId = "${author.userId}"');

    if (author.isAnonymous) {
      // 匿名用戶：跳轉到匿名用戶文章列表
      final route =
          '/anonymous-posts/${author.userHash}?displayName=${Uri.encodeQueryComponent(author.displayName)}';
      print('🔍 PostCard: 準備跳轉到匿名用戶頁面: $route');

      try {
        context.push(route);
        print('✅ PostCard: 匿名用戶頁面跳轉成功');
      } catch (e) {
        print('❌ PostCard: 匿名用戶頁面跳轉失敗: $e');
      }
    } else {
      // 非匿名用戶：跳轉到用戶資訊頁面
      final route = '/user/${author.userId}';
      print('🔍 PostCard: 準備跳轉到用戶資訊頁面: $route');

      try {
        context.push(route);
        print('✅ PostCard: 用戶資訊頁面跳轉成功');
      } catch (e) {
        print('❌ PostCard: 用戶資訊頁面跳轉失敗: $e');
      }
    }
  }

  /// 處理大頭照點擊
  void _handleAvatarTap() {
    final author = widget.post.author;

    // 只有非匿名用戶且有大頭照時才顯示
    if (!author.isAnonymous && author.photoURL != null) {
      _showAvatarDialog(author.photoURL!);
    }
  }

  /// 顯示大頭照對話框
  void _showAvatarDialog(String imageUrl) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Stack(
            children: [
              // 背景點擊關閉
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.transparent,
                ),
              ),
              // 大頭照顯示
              Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.8,
                    maxHeight: MediaQuery.of(context).size.height * 0.8,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.contain,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          width: 200,
                          height: 200,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 200,
                          height: 200,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Center(
                            child: Icon(Icons.error),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              // 關閉按鈕
              Positioned(
                top: 40,
                right: 20,
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    final theme = Theme.of(context);
    final author = widget.post.author;

    return Padding(
      padding: const EdgeInsets.fromLTRB(8, 16, 8, 8),
      child: Row(
        children: [
          // 用戶大頭照（可點擊）
          GestureDetector(
            onTap: () => _handleAvatarTap(),
            child: CircleAvatar(
              radius: 20,
              backgroundImage: author.photoURL != null && !author.isAnonymous
                  ? NetworkImage(author.photoURL!)
                  : null,
              backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
              child: author.photoURL == null || author.isAnonymous
                  ? Icon(
                      Icons.person,
                      color: theme.colorScheme.primary,
                    )
                  : null,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // 用戶名稱（可點擊）
                    GestureDetector(
                      onTap: () => _handleUserNameTap(),
                      child: Text(
                        author.displayName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // 匿名標籤
                    if (author.isAnonymous) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.secondary.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '匿名',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.secondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    // 聲望值
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${author.reputation ?? 0}',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    AppText(
                      _formatTimestamp(widget.post.createdAt),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    // 距離顯示
                    _buildDistanceDisplay(theme),
                    // 用戶階級（非匿名用戶）
                    if (!author.isAnonymous && author.title != null) ...[
                      const SizedBox(width: 8),
                      Text(
                        '• ${author.titleIcon} ${author.title}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary.withOpacity(0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _showMoreOptions,
            icon: Icon(
              Icons.more_vert,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 文字內容
            if (widget.post.content.isNotEmpty) _buildTextContent(theme),

            // 媒體內容
            if (widget.post.hasMedia) ...[
              if (widget.post.content.isNotEmpty) const SizedBox(height: 12),
              _buildMediaContent(theme),
            ],

            // 🔗 連結預覽 - 確保在文字和媒體內容之後顯示
            if (widget.post.hasLinkPreviews) ...[
              if (widget.post.content.isNotEmpty || widget.post.hasMedia)
                const SizedBox(height: 12),
              LinkPreviewList(
                linkPreviews: widget.post.validLinkPreviews,
                isCompact: true,
                maxPreviews: 2,
              ),
            ],
            if (widget.post.hashtags.isNotEmpty) ...[
              const SizedBox(height: 12),
              // Hashtag 提示標籤
              Padding(
                padding: const EdgeInsets.only(bottom: 6),
                child: Row(
                  children: [
                    Icon(
                      Icons.tag,
                      size: 14,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    AppText(
                      'Hashtags',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 32,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 0),
                  itemCount: widget.post.hashtags.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(width: 8),
                  itemBuilder: (context, index) {
                    final hashtag = widget.post.hashtags[index];
                    return GestureDetector(
                      onTap: () {
                        debugPrint('🔍 PostCard: hashtag Chip 被點擊 - $hashtag');
                        _navigateToHashtagPosts(hashtag);
                      },
                      child: Chip(
                        label: Text(
                          '#$hashtag',
                          style: TextStyle(
                            color: theme.colorScheme.onSecondaryContainer,
                            fontSize: 12,
                          ),
                        ),
                        backgroundColor: theme.colorScheme.secondaryContainer,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: VisualDensity.compact,
                      ),
                    );
                  },
                ),
              ),
            ],

            // 地理標籤部分
            if (widget.post.location.geoTags.isNotEmpty) ...[
              const SizedBox(height: 12),
              // 地理標籤提示標籤
              Padding(
                padding: const EdgeInsets.only(bottom: 6),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    AppText(
                      '地理標籤',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 32,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 0),
                  itemCount: widget.post.location.geoTags.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(width: 8),
                  itemBuilder: (context, index) {
                    final tag = widget.post.location.geoTags[index];
                    return GestureDetector(
                      onTap: () {
                        debugPrint('🔍 PostCard: 地理標籤 Chip 被點擊 - $tag');
                        _navigateToGeoTagPosts(tag);
                      },
                      child: Chip(
                        label: Text(
                          tag,
                          style: TextStyle(
                            color: theme.colorScheme.onPrimaryContainer,
                            fontSize: 12,
                          ),
                        ),
                        backgroundColor: theme.colorScheme.primaryContainer,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: VisualDensity.compact,
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 建立文字內容（支援展開/收起）
  Widget _buildTextContent(ThemeData theme) {
    const maxLength = 60;
    final content = widget.post.content;

    // 如果有連結預覽，從文字中移除連結URL以避免重複顯示
    String displayContent = content;
    if (widget.post.hasLinkPreviews) {
      displayContent = _removeUrlsFromText(content);
    }

    final needsTruncation = displayContent.length > maxLength;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 使用 AnimatedContainer 來平滑過渡高度變化
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: Text(
            _isExpanded || !needsTruncation
                ? displayContent
                : '${displayContent.substring(0, maxLength)}...',
            style: theme.textTheme.bodyMedium,
            // 當展開時不限制行數，收起時限制為合理行數
            maxLines: _isExpanded ? null : 3,
            overflow:
                _isExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
          ),
        ),
        if (needsTruncation) ...[
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _isExpanded ? '顯示較少' : '顯示更多',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 從文字中移除URL（因為會單獨顯示連結預覽）
  String _removeUrlsFromText(String text) {
    final urlRegex = RegExp(
      r'https?://[^\s<>"{}|\\^`\[\]]+',
      caseSensitive: false,
    );

    // 移除URL並清理多餘的空白
    String result = text.replaceAll(urlRegex, '').trim();

    // 清理多個連續的空白字符
    result = result.replaceAll(RegExp(r'\s+'), ' ');

    return result;
  }

  Widget _buildMediaContent(ThemeData theme) {
    final allMedia = [...widget.post.imageUrls, ...widget.post.videoUrls];

    if (allMedia.isEmpty) return const SizedBox.shrink();

    // 單一媒體
    if (allMedia.length == 1) {
      final mediaUrl = allMedia.first;
      final isVideo = widget.post.videoUrls.contains(mediaUrl);

      if (isVideo) {
        // 使用增強版影片播放器
        return Container(
          height: 300, // 稍微增加高度以適應控制器
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: theme.colorScheme.surfaceContainerHighest,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: EnhancedVideoPlayer(
              videoUrl: mediaUrl,
              autoPlay: false,
              aspectRatio: 16 / 9,
              enableFullscreen: true,
              onFullscreen: () => _openFullScreenVideo(mediaUrl),
            ),
          ),
        );
      } else {
        return GestureDetector(
          onTap: () => _openMediaViewer(mediaUrl),
          child: Container(
            constraints: const BoxConstraints(
              maxHeight: 400, // 設定最大高度，避免圖片過高
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: theme.colorScheme.surfaceContainerHighest,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildSingleImageDisplay(mediaUrl, theme),
            ),
          ),
        );
      }
    }

    // 多個媒體 - 網格佈局
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: allMedia.length == 2 ? 2 : 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 1,
      ),
      itemCount: allMedia.length > 4 ? 4 : allMedia.length,
      itemBuilder: (context, index) {
        final mediaUrl = allMedia[index];
        final isVideo = widget.post.videoUrls.contains(mediaUrl);

        return GestureDetector(
          onTap: () => _openMediaViewer(mediaUrl),
          child: Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: theme.colorScheme.surfaceContainerHighest,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: isVideo
                      ? _buildVideoThumbnailForGrid(mediaUrl, theme)
                      : _buildImageThumbnailForGrid(mediaUrl, theme),
                ),
              ),
              // 影片播放按鈕
              if (isVideo)
                Positioned.fill(
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              // 更多媒體指示器
              if (index == 3 && allMedia.length > 4)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.black.withOpacity(0.7),
                    ),
                    child: Center(
                      child: Text(
                        '+${allMedia.length - 4}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  /// 智能圖片顯示（支援 GIF 動畫）
  Widget _buildSmartImage(String imageUrl, {BoxFit fit = BoxFit.cover}) {
    final theme = Theme.of(context);

    // 檢查是否為 GIF 檔案
    final isGif = imageUrl.toLowerCase().endsWith('.gif') ||
        imageUrl.toLowerCase().contains('.gif?') ||
        imageUrl.toLowerCase().contains('.gif&');

    if (isGif) {
      // GIF 使用原生 Image.network 以支援動畫
      return Image.network(
        imageUrl,
        fit: fit,
        loadingBuilder: (context, child, progress) {
          if (progress == null) return child;
          return Container(
            color: theme.colorScheme.surfaceContainerHighest,
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                value: progress.expectedTotalBytes != null
                    ? progress.cumulativeBytesLoaded /
                        progress.expectedTotalBytes!
                    : null,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) => Container(
          color: theme.colorScheme.surfaceContainerHighest,
          child: Center(
            child: Icon(
              Icons.broken_image,
              color: theme.colorScheme.error,
              size: 32,
            ),
          ),
        ),
      );
    } else {
      // 靜態圖片使用 CachedNetworkImage
      return CachedNetworkImage(
        imageUrl: imageUrl,
        fit: fit,
        placeholder: (context, url) => Container(
          color: theme.colorScheme.surfaceContainerHighest,
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: theme.colorScheme.surfaceContainerHighest,
          child: Center(
            child: Icon(
              Icons.broken_image,
              color: theme.colorScheme.error,
              size: 32,
            ),
          ),
        ),
      );
    }
  }

  /// 為單張圖片顯示構建完整比例的圖片
  Widget _buildSingleImageDisplay(String imageUrl, ThemeData theme) {
    return _buildSmartImage(imageUrl, fit: BoxFit.contain);
  }

  /// 為網格佈局構建圖片縮圖（不包裝額外的 GestureDetector）
  Widget _buildImageThumbnailForGrid(String imageUrl, ThemeData theme) {
    return _buildSmartImage(imageUrl, fit: BoxFit.cover);
  }

  /// 為網格佈局構建影片縮圖（不包裝額外的 GestureDetector）
  Widget _buildVideoThumbnailForGrid(String videoUrl, ThemeData theme) {
    return VideoThumbnail(
      videoUrl: videoUrl,
      borderRadius: BorderRadius.circular(8),
      onTap: null, // 在網格中不使用 VideoThumbnail 的內建點擊事件
    );
  }

  /// 打開多媒體瀏覽器
  void _openMediaViewer(String initialMediaUrl) {
    final allMedia = [...widget.post.imageUrls, ...widget.post.videoUrls];
    final initialIndex = allMedia.indexOf(initialMediaUrl);

    MediaViewerUtils.show(
      context: context,
      imageUrls: widget.post.imageUrls,
      videoUrls: widget.post.videoUrls,
      initialIndex: initialIndex >= 0 ? initialIndex : 0,
      heroTag: '${widget.post.postId}_$initialMediaUrl',
    );
  }

  /// 打開全螢幕影片播放器
  void _openFullScreenVideo(String videoUrl) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FullScreenVideoPlayer(videoUrl: videoUrl),
        fullscreenDialog: true,
      ),
    );
  }

  Widget _buildActions() {
    final theme = Theme.of(context);
    final isBookmarked = ref.watch(postInteractionProvider
        .select((state) => state.bookmarkedPosts.contains(widget.post.postId)));

    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 16),
      child: Column(
        children: [
          // 主要互動按鈕
          Row(
            children: [
              Expanded(
                child: _buildLikeButton(theme),
              ),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.comment_outlined,
                  label: '${widget.post.stats.commentCount}',
                  onTap: _showCommentDialog,
                ),
              ),
              Expanded(
                child: _buildShareButton(),
              ),
              Expanded(
                child: _buildBookmarkButton(isBookmarked),
              ),
            ],
          ),
          // 查看次數顯示
          if (widget.post.stats.viewCount > 0) ...[
            const SizedBox(height: 8),
            _buildViewCount(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildLikeButton(ThemeData theme) {
    return GestureDetector(
      onTap: _isPlaying ? null : _handleLike,
      behavior: HitTestBehavior.opaque,
      child: Container(
        // 增大點擊區域
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        // 添加最小尺寸約束
        constraints: const BoxConstraints(
          minWidth: 48,
          minHeight: 48,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedBuilder(
              animation: _likeAnimationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _likeScaleAnimation.value,
                  child: _isPlaying
                      ? SizedBox(
                          width: 22, // 調整載入指示器大小
                          height: 22,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.primary,
                            ),
                          ),
                        )
                      : Icon(
                          _isLiked ? Icons.favorite : Icons.favorite_border,
                          size: 22, // 調整圖標尺寸
                          color: _isLiked
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                );
              },
            ),
            const SizedBox(height: 4), // 垂直間距
            AppText(
              '${_localLikeCount ?? widget.post.stats.likeCount}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: _isLiked
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withOpacity(0.6),
                fontSize: 14, // 調整數字字體大小
                fontWeight: FontWeight.w600, // 增加字重
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    String? label,
    bool isActive = false,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () {
        print('🔍 PostCard: Action button tapped - ${icon.codePoint}');
        onTap();
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        // 增大點擊區域
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        // 添加最小尺寸約束
        constraints: const BoxConstraints(
          minWidth: 48,
          minHeight: 48,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 22, // 稍微縮小圖標尺寸
              color: isActive
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            if (label != null) ...[
              const SizedBox(height: 4), // 垂直間距
              AppText(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isActive
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withOpacity(0.6),
                  fontSize: 14, // 調整數字字體大小
                  fontWeight: FontWeight.w600, // 增加字重
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 建立帶動畫的收藏按鈕
  Widget _buildBookmarkButton(bool isBookmarked) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => _handleBookmark(isBookmarked),
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        constraints: const BoxConstraints(
          minWidth: 48,
          minHeight: 48,
        ),
        child: AnimatedBuilder(
          animation: _bookmarkAnimationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _bookmarkScaleAnimation.value,
              child: _isBookmarking
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 1.5,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primary,
                        ),
                      ),
                    )
                  : Icon(
                      isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                      size: 24,
                      color: isBookmarked
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildShareButton() {
    final displayCount = _localShareCount ?? widget.post.stats.shareCount;

    return _buildActionButton(
      icon: _isShared ? Icons.share : Icons.share_outlined,
      label: '$displayCount',
      onTap: _handleShare,
      isActive: _isShared,
    );
  }

  Widget _buildViewCount(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.visibility_outlined,
          size: 16,
          color: theme.colorScheme.onSurface.withOpacity(0.6),
        ),
        const SizedBox(width: 4),
        AppText(
          '${widget.post.stats.viewCount} 次查看',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  void _handleTap() {
    widget.onTap?.call();
  }

  void _handleLongPress() {
    widget.onLongPress?.call();
  }

  void _handleLike() async {
    if (_isPlaying) return; // 防止重複點擊

    setState(() {
      _isPlaying = true;
    });

    try {
      final provider = ref.read(postInteractionProvider.notifier);

      if (_isLiked) {
        // 先更新本地狀態和計數
        setState(() {
          _isLiked = false;
          _localLikeCount =
              (_localLikeCount ?? widget.post.stats.likeCount) - 1;
          if (_localLikeCount! < 0) _localLikeCount = 0;
        });

        await provider.unlikePost(widget.post.postId);

        // 顯示取消按讚成功訊息
        if (mounted) {
          AppToast.showTop(
            context,
            'post.interactions.unlike_success'.tr(),
            backgroundColor: Theme.of(context).colorScheme.primary,
            textColor: Theme.of(context).colorScheme.onPrimary,
            icon: Icons.favorite_border,
            duration: const Duration(seconds: 1),
          );
        }
      } else {
        // 先更新本地狀態和計數
        setState(() {
          _isLiked = true;
          _localLikeCount =
              (_localLikeCount ?? widget.post.stats.likeCount) + 1;
        });

        final success = await provider.likePost(widget.post.postId, context);
        if (!success) {
          // 如果失敗，恢復狀態
          setState(() {
            _isLiked = false;
            _localLikeCount =
                (_localLikeCount ?? widget.post.stats.likeCount) - 1;
          });
          return;
        }

        // 播放按讚動畫
        _likeAnimationController.forward().then((_) {
          _likeAnimationController.reverse();
        });

        // 顯示按讚成功訊息
        if (mounted) {
          AppToast.showTop(
            context,
            'post.interactions.like_success'.tr(),
            backgroundColor: Theme.of(context).colorScheme.primary,
            textColor: Theme.of(context).colorScheme.onPrimary,
            icon: Icons.favorite,
            duration: const Duration(seconds: 1),
          );
        }
      }
    } catch (error) {
      // 顯示錯誤訊息
      if (mounted) {
        AppToast.showTop(
          context,
          'post.interactions.like_failed'.tr(),
          backgroundColor: Theme.of(context).colorScheme.error,
          textColor: Theme.of(context).colorScheme.onError,
          icon: Icons.error,
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isPlaying = false;
      });
    }
  }

  /// 顯示留言對話框
  void _showCommentDialog() async {
    // 先檢查認證
    final success = await AuthGuard.quickActionLogin(
      context: context,
      ref: ref,
      actionName: '留言',
      onLoginSuccess: () {
        _performCommentAction();
      },
    );

    // 如果用戶已經登入，直接執行留言操作
    if (success && mounted) {
      _performCommentAction();
    }
  }

  /// 執行留言操作
  void _performCommentAction() {
    // 如果有自定義留言回調，使用自定義回調
    if (widget.onCommentTap != null) {
      widget.onCommentTap!();
    } else {
      // 否則使用預設的留言對話框
      ref
          .read(postInteractionProvider.notifier)
          .showCommentDialog(context, widget.post.postId);
    }
  }

  void _handleShare() async {
    print('🔍 PostCard: _handleShare called for post ${widget.post.postId}');

    // 先檢查認證
    final success = await AuthGuard.quickActionLogin(
      context: context,
      ref: ref,
      actionName: '分享',
      onLoginSuccess: () {
        _performShareAction();
      },
    );

    // 如果用戶已經登入，直接執行分享操作
    if (success && mounted) {
      _performShareAction();
    }
  }

  /// 執行分享操作
  void _performShareAction() {
    print(
        '🔍 PostCard: _performShareAction called for post ${widget.post.postId}');
    try {
      final provider = ref.read(postInteractionProvider.notifier);
      print('✅ PostCard: Provider obtained, calling showShareOptions');
      provider.showShareOptions(context, widget.post.postId);
      print('✅ PostCard: showShareOptions called successfully');
    } catch (e) {
      print('❌ PostCard: _performShareAction failed: $e');
      if (mounted) {
        AppToast.show(
          context,
          '分享功能暫時無法使用',
          backgroundColor: Theme.of(context).colorScheme.error,
          textColor: Theme.of(context).colorScheme.onError,
          icon: Icons.error,
        );
      }
    }
  }

  void _handleBookmark(bool isBookmarked) async {
    // 先檢查認證
    final success = await AuthGuard.quickActionLogin(
      context: context,
      ref: ref,
      actionName: '收藏',
      onLoginSuccess: () {
        _performBookmarkAction(isBookmarked);
      },
    );

    // 如果用戶已經登入，直接執行收藏操作
    if (success && mounted) {
      _performBookmarkAction(isBookmarked);
    }
  }

  /// 執行收藏操作
  void _performBookmarkAction(bool isBookmarked) async {
    if (_isBookmarking) return; // 防止重複點擊

    setState(() {
      _isBookmarking = true;
    });

    // 觸發動畫
    _bookmarkAnimationController.forward().then((_) {
      _bookmarkAnimationController.reverse();
    });

    try {
      final provider = ref.read(postInteractionProvider.notifier);
      bool success;

      if (isBookmarked) {
        success = await provider.unbookmarkPost(widget.post.postId);
      } else {
        success = await provider.bookmarkPost(widget.post.postId);
      }

      // 只有伺服器確認成功後才顯示成功訊息
      if (mounted && success) {
        AppToast.show(
          context,
          isBookmarked ? '已取消收藏' : '已加入收藏',
          icon: isBookmarked ? Icons.bookmark_border : Icons.bookmark,
        );
      } else if (mounted && !success) {
        // 伺服器回報失敗時顯示錯誤訊息
        AppToast.show(
          context,
          isBookmarked ? '取消收藏失敗' : '收藏失敗',
          backgroundColor: Theme.of(context).colorScheme.error,
          textColor: Theme.of(context).colorScheme.onError,
          icon: Icons.error,
        );
      }
    } catch (e) {
      if (mounted) {
        AppToast.show(
          context,
          '收藏功能暫時無法使用',
          backgroundColor: Theme.of(context).colorScheme.error,
          textColor: Theme.of(context).colorScheme.onError,
          icon: Icons.error,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isBookmarking = false;
        });
      }
    }
  }

  /// 顯示更多選項
  void _showMoreOptions() async {
    final theme = Theme.of(context);
    final currentUser = ref.read(currentUserProvider);
    final isAuthor =
        currentUser != null && widget.post.author.userId == currentUser.uid;

    // 檢查管理員權限
    final isAdmin = await AdminPermissionService.isCurrentUserAdmin();

    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽指示器
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),

            // 選項列表
            if (isAuthor) ...[
              ListTile(
                leading: Icon(Icons.edit, color: theme.colorScheme.primary),
                title: AppText.body('post.interactions.edit'.tr()),
                onTap: () {
                  Navigator.pop(context);
                  _editPost();
                },
              ),
            ],

            ListTile(
              leading: Icon(Icons.share, color: theme.colorScheme.primary),
              title: AppText.body('post.interactions.share'.tr()),
              onTap: () {
                Navigator.pop(context);
                _sharePost();
              },
            ),

            ListTile(
              leading:
                  Icon(Icons.copy, color: theme.colorScheme.onSurfaceVariant),
              title: AppText.body('post.interactions.copy_link'.tr()),
              onTap: () {
                Navigator.pop(context);
                _copyLink();
              },
            ),

            // 管理員選項：禁言用戶
            if (isAdmin && !isAuthor && !widget.post.author.isAnonymous) ...[
              FutureBuilder<bool>(
                future: AdminPermissionService.isUserFlagged(
                    widget.post.author.userId),
                builder: (context, snapshot) {
                  final isFlagged = snapshot.data ?? false;
                  return ListTile(
                    leading: Icon(
                      isFlagged ? Icons.person : Icons.block,
                      color: isFlagged ? Colors.green : Colors.orange,
                    ),
                    title: AppText.body(
                      isFlagged ? '解除禁言' : '禁言用戶',
                      style: TextStyle(
                        color: isFlagged ? Colors.green : Colors.orange,
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      if (isFlagged) {
                        _unflagUser();
                      } else {
                        _flagUser();
                      }
                    },
                  );
                },
              ),
            ],

            // 刪除選項：作者或管理員可以刪除
            if (isAuthor || isAdmin) ...[
              ListTile(
                leading: Icon(Icons.delete, color: theme.colorScheme.error),
                title: AppText.body('post.interactions.delete'.tr(),
                    style: TextStyle(color: theme.colorScheme.error)),
                onTap: () {
                  Navigator.pop(context);
                  if (isAdmin && !isAuthor) {
                    _deletePostAsAdmin();
                  } else {
                    _deletePost();
                  }
                },
              ),
            ],

            // 非作者選項
            if (!isAuthor) ...[
              // 只有非匿名用戶才能被追蹤
              if (!widget.post.author.isAnonymous)
                Consumer(
                  builder: (context, ref, child) {
                    final isFollowing = ref
                        .watch(postInteractionProvider)
                        .followingUsers
                        .contains(widget.post.author.userId);

                    return ListTile(
                      leading: Icon(
                        isFollowing
                            ? Icons.person_remove_outlined
                            : Icons.person_add_outlined,
                        color: isFollowing
                            ? theme.colorScheme.error
                            : theme.colorScheme.primary,
                      ),
                      title: AppText.body(isFollowing
                          ? 'user.unfollow'.tr()
                          : 'user.follow'.tr()),
                      onTap: () async {
                        Navigator.pop(context);
                        await Future.delayed(const Duration(milliseconds: 100));
                        if (mounted) {
                          if (isFollowing) {
                            _unfollowUser();
                          } else {
                            _followUser();
                          }
                        }
                      },
                    );
                  },
                ),
              ListTile(
                leading: Icon(Icons.report,
                    color: theme.colorScheme.onSurfaceVariant),
                title: AppText.body('report.title'.tr()),
                onTap: () {
                  Navigator.pop(context);
                  _reportPost();
                },
              ),
              ListTile(
                leading: Icon(Icons.block, color: theme.colorScheme.error),
                title: AppText.body('user.block'.tr(),
                    style: TextStyle(color: theme.colorScheme.error)),
                onTap: () {
                  Navigator.pop(context);
                  _blockUser();
                },
              ),
            ],

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _editPost() {
    // 導航到編輯頁面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreatePostScreen(editingPost: widget.post),
      ),
    );
  }

  void _deletePost() {
    // 顯示刪除確認對話框
    DeleteConfirmationDialog.show(
      context,
      title: 'post.delete_confirm_title'.tr(),
      message: 'post.delete_confirm_message'.tr(),
      warning: 'post.delete_confirm_warning'.tr(),
      onConfirm: () => _performDelete(),
    );
  }

  /// 管理員刪除貼文
  void _deletePostAsAdmin() async {
    // 顯示刪除原因對話框
    final reason = await _showDeleteReasonDialog();
    if (reason == null) return;

    try {
      await AdminPermissionService.deletePost(widget.post.postId, reason);

      if (mounted) {
        AppToast.success(context, '貼文已刪除');

        // 重新載入貼文列表
        ref.read(postListProvider.notifier).refreshPosts();
      }
    } catch (e) {
      if (mounted) {
        AppToast.error(context, '刪除失敗: $e');
      }
    }
  }

  /// 管理員禁言用戶
  void _flagUser() async {
    // 顯示禁言原因對話框
    final reason = await _showFlagReasonDialog();
    if (reason == null) return;

    try {
      await AdminPermissionService.flagUser(widget.post.author.userId, reason);

      if (mounted) {
        AppToast.success(context, '用戶已被禁言');
      }
    } catch (e) {
      if (mounted) {
        AppToast.error(context, '禁言失敗: $e');
      }
    }
  }

  /// 管理員解除禁言
  void _unflagUser() async {
    try {
      await AdminPermissionService.unflagUser(widget.post.author.userId);

      if (mounted) {
        AppToast.success(context, '已解除用戶禁言');
      }
    } catch (e) {
      if (mounted) {
        AppToast.error(context, '解除禁言失敗: $e');
      }
    }
  }

  /// 顯示禁言原因對話框
  Future<String?> _showFlagReasonDialog() async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('禁言原因'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('請輸入禁言此用戶的原因：'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                hintText: '例如：發布不當內容、惡意騷擾等',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              final reason = controller.text.trim();
              if (reason.isNotEmpty) {
                Navigator.of(context).pop(reason);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('確認禁言'),
          ),
        ],
      ),
    );
  }

  /// 顯示刪除原因對話框
  Future<String?> _showDeleteReasonDialog() async {
    String? reason;

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('刪除原因'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: '請輸入刪除原因...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          onChanged: (value) => reason = value,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, reason),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  void _performDelete() async {
    final crudNotifier = ref.read(crudProvider.notifier);
    final success = await crudNotifier.deletePost(
      postId: widget.post.postId,
      softDelete: true, // 使用軟刪除
    );

    if (success && mounted) {
      // 顯示成功訊息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: AppText.body('post.delete_success'.tr()),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );

      // 重新載入貼文列表
      ref.read(postListProvider.notifier).refreshPosts();
    } else if (mounted) {
      // 顯示錯誤訊息
      final crudState = ref.read(crudProvider);
      if (crudState.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: AppText.body(crudState.error!),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _reportPost() async {
    // 先檢查認證
    final success = await AuthGuard.quickActionLogin(
      context: context,
      ref: ref,
      actionName: '檢舉',
      onLoginSuccess: () {
        _performReportAction();
      },
    );

    // 如果用戶已經登入，直接執行檢舉操作
    if (success && mounted) {
      _performReportAction();
    }
  }

  /// 執行檢舉操作
  void _performReportAction() {
    ReportDialog.show(
      context,
      targetId: widget.post.postId,
      targetType: ReportTargetType.post,
      targetUserId: widget.post.author.userId,
      targetContent: widget.post.content,
    );
  }

  /// 分享貼文
  void _sharePost() {
    ref.read(postInteractionProvider.notifier).sharePost(widget.post.postId);
  }

  /// 複製貼文連結
  void _copyLink() async {
    try {
      final postUrl = 'https://hisohiso.app/post/${widget.post.postId}';
      await Clipboard.setData(ClipboardData(text: postUrl));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: AppText.body('post.interactions.link_copied'.tr()),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: AppText.body('post.copy_failed'.tr()),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// 追蹤用戶
  void _followUser() async {
    // 先檢查認證
    final success = await AuthGuard.quickActionLogin(
      context: context,
      ref: ref,
      actionName: '追蹤',
      onLoginSuccess: () {
        _performFollowAction();
      },
    );

    // 如果用戶已經登入，直接執行追蹤操作
    if (success && mounted) {
      _performFollowAction();
    }
  }

  /// 執行追蹤操作
  void _performFollowAction() async {
    print('🔍 PostCard: 追蹤用戶 ${widget.post.author.userId}');

    // 檢查 widget 是否還存在
    if (!mounted) {
      print('❌ PostCard: Widget not mounted');
      return;
    }

    try {
      print('🔍 PostCard: 調用 postInteractionProvider.followUser');
      await ref
          .read(postInteractionProvider.notifier)
          .followUser(widget.post.author.userId);

      print('✅ PostCard: 追蹤成功');
      if (mounted) {
        AppToast.showTop(
          context,
          '已追蹤 ${widget.post.author.displayName}',
          backgroundColor: Theme.of(context).colorScheme.primary,
          textColor: Theme.of(context).colorScheme.onPrimary,
          icon: Icons.person_add,
        );
      }
    } catch (e) {
      print('❌ PostCard: 追蹤失敗: $e');
      if (mounted) {
        AppToast.showTop(
          context,
          '追蹤失敗，請稍後再試',
          backgroundColor: Theme.of(context).colorScheme.error,
          textColor: Theme.of(context).colorScheme.onError,
          icon: Icons.error,
          isError: true,
        );
      }
    }
  }

  /// 取消追蹤用戶
  void _unfollowUser() async {
    print('🔍 PostCard: 取消追蹤用戶 ${widget.post.author.userId}');

    // 檢查 widget 是否還存在
    if (!mounted) {
      print('❌ PostCard: Widget not mounted');
      return;
    }

    try {
      print('🔍 PostCard: 調用 postInteractionProvider.unfollowUser');
      await ref
          .read(postInteractionProvider.notifier)
          .unfollowUser(widget.post.author.userId);

      print('✅ PostCard: 取消追蹤成功');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: AppText.body('已取消追蹤 ${widget.post.author.displayName}'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      print('❌ PostCard: 取消追蹤失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: AppText.body('取消追蹤失敗，請稍後再試'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// 封鎖用戶
  void _blockUser() async {
    // 先檢查認證
    final success = await AuthGuard.quickActionLogin(
      context: context,
      ref: ref,
      actionName: '封鎖',
      onLoginSuccess: () {
        _showBlockConfirmation();
      },
    );

    // 如果用戶已經登入，直接顯示封鎖確認
    if (success && mounted) {
      _showBlockConfirmation();
    }
  }

  /// 顯示封鎖確認對話框
  void _showBlockConfirmation() {
    print('🔍 PostCard: 封鎖用戶 ${widget.post.author.userId}');

    // 顯示封鎖確認 BottomSheet
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽指示器
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),

            // 圖標
            Icon(
              Icons.block,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),

            // 標題
            AppText.title('封鎖用戶'),
            const SizedBox(height: 8),

            // 內容
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: AppText.body(
                '確定要封鎖 ${widget.post.author.displayName} 嗎？封鎖後將不會看到此用戶的貼文。',
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),

            // 按鈕
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                children: [
                  Expanded(
                    child: AppButton.secondary(
                      text: '取消',
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: AppButton(
                      text: '確定封鎖',
                      onPressed: () {
                        Navigator.pop(context);
                        _performBlock();
                      },
                      backgroundColor: Theme.of(context).colorScheme.error,
                      foregroundColor: Theme.of(context).colorScheme.onError,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// 執行封鎖
  void _performBlock() async {
    print('✅ PostCard: 執行封鎖 ${widget.post.author.userId}');

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // 添加到封鎖列表
      await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .collection('blocked_users')
          .doc(widget.post.author.userId)
          .set({
        'blocked_at': FieldValue.serverTimestamp(),
        'reason': 'user_blocked',
        'blocked_from_post': widget.post.postId,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: AppText.body('已封鎖 ${widget.post.author.displayName}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (error) {
      print('❌ 封鎖用戶失敗: $error');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: AppText.body('封鎖失敗，請稍後再試'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now'.tr();
    }
  }

  /// 導航到 hashtag 相關文章
  void _navigateToHashtagPosts(String hashtag) {
    debugPrint('🔍 PostCard: 點擊 hashtag - $hashtag');
    debugPrint('🔍 PostCard: 準備導航到 hashtag 篩選頁面');

    try {
      final url =
          '/posts?filterType=hashtag&filterValue=${Uri.encodeComponent(hashtag)}&title=${Uri.encodeComponent('#$hashtag')}';
      debugPrint('🔍 PostCard: 導航 URL - $url');

      context.push(url);
      debugPrint('✅ PostCard: hashtag 導航成功');
    } catch (error) {
      debugPrint('❌ PostCard: hashtag 導航失敗 - $error');
    }
  }

  /// 導航到地理標籤相關文章
  void _navigateToGeoTagPosts(String geoTag) {
    debugPrint('🔍 PostCard: 點擊地理標籤 - $geoTag');
    debugPrint('🔍 PostCard: 準備導航到地理標籤篩選頁面');

    try {
      final url =
          '/posts?filterType=geoTag&filterValue=${Uri.encodeComponent(geoTag)}&title=${Uri.encodeComponent(geoTag)}';
      debugPrint('🔍 PostCard: 導航 URL - $url');

      context.push(url);
      debugPrint('✅ PostCard: 地理標籤導航成功');
    } catch (error) {
      debugPrint('❌ PostCard: 地理標籤導航失敗 - $error');
    }
  }

  /// 建立距離顯示組件
  Widget _buildDistanceDisplay(ThemeData theme) {
    // 獲取用戶當前位置
    final locationState = ref.watch(locationProvider);

    // 如果沒有用戶位置資訊，不顯示距離
    if (!locationState.hasLocation) {
      return const SizedBox.shrink();
    }

    final userLat = locationState.latitude!;
    final userLon = locationState.longitude!;
    final postLat = widget.post.location.latitude;
    final postLon = widget.post.location.longitude;

    // 計算距離
    final distance = DistanceCalculator.calculateAndFormatDistance(
      userLat,
      userLon,
      postLat,
      postLon,
    );

    // 獲取距離等級和對應顏色
    final distanceKm = DistanceCalculator.calculateDistance(
      userLat,
      userLon,
      postLat,
      postLon,
    );
    final distanceLevel = DistanceCalculator.getDistanceLevel(distanceKm);
    final distanceColor =
        Color(DistanceCalculator.getDistanceLevelColor(distanceLevel));

    return Row(
      children: [
        const SizedBox(width: 8),
        Icon(
          Icons.location_on,
          size: 12,
          color: distanceColor,
        ),
        const SizedBox(width: 2),
        Text(
          distance,
          style: theme.textTheme.bodySmall?.copyWith(
            color: distanceColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

/// 📝 貼文卡片骨架屏
class PostCardSkeleton extends StatelessWidget {
  const PostCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 16,
                      width: 120,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 12,
                      width: 80,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            height: 16,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 16,
            width: 200,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';

/// 📊 查看記錄追蹤服務 - 管理本地快取和 Firestore 同步
class ViewTrackingService {
  static final ViewTrackingService _instance = ViewTrackingService._internal();
  factory ViewTrackingService() => _instance;
  ViewTrackingService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  late SharedPreferences _prefs;
  String? _currentUserId;

  // 本地快取 - 記錄已查看的貼文
  final Set<String> _viewedPosts = {};
  bool _isInitialized = false;

  static Future<ViewTrackingService> createWithCache() async {
    await _instance._initialize();
    return _instance;
  }

  Future<void> _initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// 初始化用戶快取（登入時調用）
  Future<void> initializeUserCache(String userId) async {
    if (_currentUserId == userId && _isInitialized) {
      Logger.i('📊 ViewTracking: 用戶 $userId 快取已初始化');
      return;
    }

    // 如果是不同用戶，才清空快取
    if (_currentUserId != userId) {
      Logger.i('📊 ViewTracking: 切換用戶 $_currentUserId -> $userId，清空快取');
      _viewedPosts.clear();
    }

    _currentUserId = userId;
    _isInitialized = false;

    // 從本地快取載入
    await _loadFromLocalCache();

    // 從 Firestore 同步（只有當本地快取為空或需要更新時）
    await _syncWithFirestore();

    _isInitialized = true;
    Logger.i(
        '📊 ViewTracking: 用戶 $userId 快取初始化完成，共 ${_viewedPosts.length} 個查看記錄');
  }

  /// 確保快取已初始化（用於每次檢查前）
  Future<void> ensureInitialized(String userId) async {
    // 如果已經初始化且是同一個用戶，直接返回
    if (_currentUserId == userId && _isInitialized) {
      return;
    }

    // 否則初始化快取
    await initializeUserCache(userId);
  }

  /// 從本地快取載入
  Future<void> _loadFromLocalCache() async {
    if (_currentUserId == null) return;

    final key = 'viewed_posts_$_currentUserId';
    final cachedViews = _prefs.getStringList(key) ?? [];

    // 清空現有快取，然後加載本地數據
    _viewedPosts.clear();
    _viewedPosts.addAll(cachedViews);

    Logger.i('📱 從本地載入 ${cachedViews.length} 個查看記錄');
  }

  /// 保存到本地快取
  Future<void> _saveToLocalCache() async {
    if (_currentUserId == null) return;

    final key = 'viewed_posts_$_currentUserId';
    await _prefs.setStringList(key, _viewedPosts.toList());
  }

  /// 與 Firestore 同步
  Future<void> _syncWithFirestore() async {
    if (_currentUserId == null) return;

    try {
      final userViewsDoc =
          await _firestore.collection('views').doc(_currentUserId).get();

      if (userViewsDoc.exists) {
        final data = userViewsDoc.data();
        final viewedPosts = data?['viewed_posts'] as List<dynamic>? ?? [];

        // 記錄同步前的數量
        final beforeCount = _viewedPosts.length;

        // 提取貼文 ID 並加入快取（使用 Set 自動去重）
        for (final view in viewedPosts) {
          if (view is Map<String, dynamic>) {
            final postId = view['post_id'] as String?;
            if (postId != null) {
              _viewedPosts.add(postId);
            }
          }
        }

        // 只有當有新數據時才保存到本地快取
        if (_viewedPosts.length > beforeCount) {
          await _saveToLocalCache();
          Logger.i(
              '🔄 從 Firestore 同步了 ${_viewedPosts.length - beforeCount} 個新的查看記錄');
        } else {
          Logger.i('🔄 Firestore 同步完成，無新記錄');
        }
      }
    } catch (e) {
      Logger.e('❌ 同步查看記錄失敗: $e');
    }
  }

  /// 檢查是否已查看（優先使用本地快取）
  bool hasViewed(String postId) {
    if (!_isInitialized || _currentUserId == null) {
      Logger.w('⚠️ ViewTracking 未初始化');
      return false;
    }

    return _viewedPosts.contains(postId);
  }

  /// 記錄新的查看（立即更新本地快取）
  Future<void> recordView(String postId) async {
    if (!_isInitialized || _currentUserId == null) {
      Logger.w('⚠️ ViewTracking 未初始化，無法記錄查看');
      return;
    }

    // 如果已經查看過，直接返回
    if (_viewedPosts.contains(postId)) {
      return;
    }

    // 立即更新本地快取
    _viewedPosts.add(postId);
    await _saveToLocalCache();

    Logger.i('✅ 記錄查看: $postId (本地快取已更新)');
  }

  /// 清除用戶快取（登出時調用）
  Future<void> clearUserCache() async {
    if (_currentUserId != null) {
      final key = 'viewed_posts_$_currentUserId';
      await _prefs.remove(key);
    }

    _viewedPosts.clear();
    _isInitialized = false;
    _currentUserId = null;

    Logger.i('🗑️ 清除查看記錄快取');
  }

  /// 獲取當前已查看的貼文列表
  List<String> getViewedPosts() {
    return _viewedPosts.toList();
  }

  /// 獲取查看記錄數量
  int getViewedCount() {
    return _viewedPosts.length;
  }
}

import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';

/// 📊 查看記錄追蹤服務 - 管理本地快取和 Firestore 同步
class ViewTrackingService {
  static final ViewTrackingService _instance = ViewTrackingService._internal();
  factory ViewTrackingService() => _instance;
  ViewTrackingService._internal();

  late SharedPreferences _prefs;
  String? _currentUserId;

  // 本地快取 - 記錄已查看的貼文
  final Set<String> _viewedPosts = {};
  bool _isInitialized = false;

  static Future<ViewTrackingService> createWithCache() async {
    await _instance._initialize();
    return _instance;
  }

  Future<void> _initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// 初始化用戶快取（登入時調用）
  Future<void> initializeUserCache(String userId) async {
    if (_currentUserId == userId && _isInitialized) {
      Logger.i('📊 ViewTracking: 用戶 $userId 快取已初始化');
      return;
    }

    // 如果是不同用戶，才清空快取
    if (_currentUserId != userId) {
      Logger.i('📊 ViewTracking: 切換用戶 $_currentUserId -> $userId，清空快取');
      _viewedPosts.clear();
    }

    _currentUserId = userId;
    _isInitialized = false;

    // 只從本地快取載入，不同步 Firestore
    await _loadFromLocalCache();

    _isInitialized = true;
    Logger.i(
        '📊 ViewTracking: 用戶 $userId 本地快取初始化完成，共 ${_viewedPosts.length} 個查看記錄');
  }

  /// 確保快取已初始化（用於每次檢查前）
  Future<void> ensureInitialized(String userId) async {
    // 如果已經初始化且是同一個用戶，直接返回
    if (_currentUserId == userId && _isInitialized) {
      return;
    }

    // 否則初始化快取
    await initializeUserCache(userId);
  }

  /// 從本地快取載入
  Future<void> _loadFromLocalCache() async {
    if (_currentUserId == null) return;

    final key = 'viewed_posts_$_currentUserId';
    final cachedViews = _prefs.getStringList(key) ?? [];

    // 清空現有快取，然後加載本地數據
    _viewedPosts.clear();
    _viewedPosts.addAll(cachedViews);

    Logger.i('📱 [DEBUG] 從本地載入 ${cachedViews.length} 個查看記錄');
    Logger.i('📱 [DEBUG] 載入的記錄: $cachedViews');
    Logger.i('📱 [DEBUG] 內存快取狀態: $_viewedPosts');
  }

  /// 保存到本地快取
  Future<void> _saveToLocalCache() async {
    if (_currentUserId == null) return;

    final key = 'viewed_posts_$_currentUserId';
    await _prefs.setStringList(key, _viewedPosts.toList());

    Logger.i('📱 [DEBUG] 保存到本地快取: ${_viewedPosts.toList()}');
    Logger.i('📱 [DEBUG] 快取鍵: $key');
  }

  /// 檢查是否已查看（優先使用本地快取）
  bool hasViewed(String postId) {
    if (!_isInitialized || _currentUserId == null) {
      Logger.w('⚠️ [DEBUG] ViewTracking 未初始化');
      return false;
    }

    final result = _viewedPosts.contains(postId);
    Logger.i('📱 [DEBUG] 檢查文章 $postId 是否已查看: $result');
    Logger.i('📱 [DEBUG] 當前快取內容: $_viewedPosts');

    return result;
  }

  /// 記錄新的查看（立即更新本地快取）
  Future<void> recordView(String postId) async {
    if (!_isInitialized || _currentUserId == null) {
      Logger.w('⚠️ [DEBUG] ViewTracking 未初始化，無法記錄查看');
      return;
    }

    // 如果已經查看過，直接返回
    if (_viewedPosts.contains(postId)) {
      Logger.i('📱 [DEBUG] 文章 $postId 已在快取中，跳過記錄');
      return;
    }

    // 立即更新本地快取
    _viewedPosts.add(postId);
    await _saveToLocalCache();

    Logger.i('✅ [DEBUG] 記錄查看: $postId (本地快取已更新)');
    Logger.i('📱 [DEBUG] 更新後的快取: $_viewedPosts');
  }

  /// 清除用戶快取（登出時調用）
  Future<void> clearUserCache() async {
    if (_currentUserId != null) {
      final key = 'viewed_posts_$_currentUserId';
      await _prefs.remove(key);
    }

    _viewedPosts.clear();
    _isInitialized = false;
    _currentUserId = null;

    Logger.i('🗑️ 清除查看記錄快取');
  }

  /// 獲取當前已查看的貼文列表
  List<String> getViewedPosts() {
    return _viewedPosts.toList();
  }

  /// 獲取查看記錄數量
  int getViewedCount() {
    return _viewedPosts.length;
  }

  /// 獲取本地查看次數（用於顯示）
  /// 由於是純本地快取，每個用戶看到的都是自己的查看記錄
  /// 返回 1 表示已查看，0 表示未查看
  int getLocalViewCount(String postId) {
    return hasViewed(postId) ? 1 : 0;
  }
}

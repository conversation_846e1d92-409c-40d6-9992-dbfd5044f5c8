// lib/features/auth/presentation/providers/auth_provider.dart

import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'dart:io' show Platform;
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:ui' as ui;
import '../../../home/<USER>/repositories/home_repository_impl.dart';
import '../../../../core/services/interaction_cache_service.dart';
import '../../../../core/services/ip_address_service.dart';
import '../../../../core/utils/facebook_debug_helper.dart';
import '../../../profile/presentation/providers/profile_provider.dart';
import '../../domain/entities/notification_settings.dart';
import '../../../home/<USER>/services/user_follow_service.dart';
import '../../../home/<USER>/services/user_like_service.dart';
import '../../../home/<USER>/services/user_favorite_service.dart';
import '../../../home/<USER>/services/view_tracking_service.dart';
import '../../../../core/services/block_service.dart';
import '../../../home/<USER>/providers/draft_provider.dart';
import '../../../home/<USER>/providers/batch_interaction_provider.dart';
import '../../../../core/services/enhanced_analytics_service.dart';

/// 🔐 認證管理 Provider
/// 負責管理用戶登入、登出和認證狀態

/// 用戶模型
class AppUser {
  final String uid;
  final String? email;
  final String? displayName;
  final String? photoURL;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final String? anonymousName;
  final bool useAnonymousMode;
  final double reputation;
  final String? country; // 國家代碼 (e.g., 'TW', 'US')
  final String? countryName; // 國家名稱 (e.g., 'Taiwan', 'United States')
  final NotificationSettings notificationSettings; // 通知設定

  const AppUser({
    required this.uid,
    this.email,
    this.displayName,
    this.photoURL,
    required this.createdAt,
    required this.lastLoginAt,
    this.anonymousName,
    this.useAnonymousMode = false,
    this.reputation = 0.0,
    this.country,
    this.countryName,
    this.notificationSettings = const NotificationSettings(),
  });

  factory AppUser.fromFirebaseUser(User user) {
    return AppUser(
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      createdAt: user.metadata.creationTime ?? DateTime.now(),
      lastLoginAt: user.metadata.lastSignInTime ?? DateTime.now(),
      anonymousName: null,
      useAnonymousMode: false,
    );
  }

  /// 從 Firestore 數據創建完整的用戶對象
  factory AppUser.fromFirestoreData(
      User firebaseUser, Map<String, dynamic>? firestoreData) {
    return AppUser(
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      photoURL: firebaseUser.photoURL,
      createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      lastLoginAt: firebaseUser.metadata.lastSignInTime ?? DateTime.now(),
      anonymousName: firestoreData?['anonymousName'] as String?,
      useAnonymousMode: firestoreData?['useAnonymousMode'] as bool? ?? false,
      reputation: (firestoreData?['reputation'] as num?)?.toDouble() ?? 0.0,
      country: firestoreData?['country'] as String?,
      countryName: firestoreData?['countryName'] as String?,
      notificationSettings: firestoreData?['notificationSettings'] != null
          ? NotificationSettings.fromFirestore(
              firestoreData!['notificationSettings'] as Map<String, dynamic>)
          : const NotificationSettings(),
    );
  }

  AppUser copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? Function()? photoURL,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    String? anonymousName,
    bool? useAnonymousMode,
    double? reputation,
    String? country,
    String? countryName,
    NotificationSettings? notificationSettings,
  }) {
    return AppUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL != null ? photoURL() : this.photoURL,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      anonymousName: anonymousName ?? this.anonymousName,
      useAnonymousMode: useAnonymousMode ?? this.useAnonymousMode,
      reputation: reputation ?? this.reputation,
      country: country ?? this.country,
      countryName: countryName ?? this.countryName,
      notificationSettings: notificationSettings ?? this.notificationSettings,
    );
  }
}

/// 認證狀態管理器
class AuthNotifier extends AsyncNotifier<AppUser?> {
  FirebaseAuth? _firebaseAuth;
  GoogleSignIn? _googleSignIn;
  StreamSubscription<User?>? _authStateSubscription;

  @override
  Future<AppUser?> build() async {
    _firebaseAuth ??= FirebaseAuth.instance;
    _googleSignIn ??= GoogleSignIn();

    // 監聽認證狀態變化
    _authStateSubscription =
        _firebaseAuth!.authStateChanges().listen((User? user) async {
      if (user != null) {
        final completeUser = await _fetchCompleteUserData(user);
        state = AsyncValue.data(completeUser);
      } else {
        state = const AsyncValue.data(null);
      }
    });

    // 當 provider 被銷毀時自動取消訂閱
    ref.onDispose(() {
      _authStateSubscription?.cancel();
    });

    // 返回當前用戶
    final currentUser = _firebaseAuth!.currentUser;
    if (currentUser != null) {
      final appUser = await _fetchCompleteUserData(currentUser);

      // 🔥 APP 重啟時初始化用戶快取（關鍵修復）
      try {
        debugPrint('🔥 [AuthProvider] APP 重啟，初始化用戶快取: ${currentUser.uid}');
        await Future.wait([
          _initializeFollowCache(currentUser.uid),
          _initializeLikeCache(currentUser.uid),
          _initializeFavoriteCache(currentUser.uid),
          _initializeViewTrackingCache(currentUser.uid),
        ]);
        debugPrint('✅ [AuthProvider] APP 重啟快取初始化完成');
      } catch (e) {
        debugPrint('❌ [AuthProvider] APP 重啟快取初始化失敗: $e');
      }

      return appUser;
    }

    return null;
  }

  /// 使用 Google 登入
  Future<void> signInWithGoogle() async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      try {
        // 觸發 Google 登入流程
        final GoogleSignInAccount? googleUser = await _googleSignIn!.signIn();

        if (googleUser == null) {
          // 用戶取消登入
          return state.value;
        }

        // 獲取認證詳細資訊
        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;

        // 建立 Firebase 認證憑證
        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        // 使用憑證登入 Firebase
        final UserCredential userCredential =
            await _firebaseAuth!.signInWithCredential(credential);
        final User? user = userCredential.user;

        if (user != null) {
          // 記錄登入成功事件
          try {
            await FirebaseAnalytics.instance.logLogin(loginMethod: 'google');

            // 使用增強的 Analytics 服務記錄更詳細的資訊
            await enhancedAnalytics.setUserId(user.uid);
            await enhancedAnalytics.logEvent('login_success', parameters: {
              'method': 'google',
              'is_new_user':
                  userCredential.additionalUserInfo?.isNewUser ?? false,
              'provider_id': userCredential.additionalUserInfo?.providerId,
            });

            // 設置用戶屬性
            await enhancedAnalytics.setUserProperty('login_method', 'google');
            await enhancedAnalytics.setUserProperty('account_created',
                user.metadata.creationTime?.toIso8601String());
          } catch (e) {
            debugPrint('Analytics 記錄失敗: $e');
          }

          // 儲存用戶資料到 Firestore
          await _saveUserToFirestore(user);

          // 同步用戶互動狀態到本地快取
          await _syncUserInteractions(user.uid);

          // 初始化快取（並行執行提升速度）
          await Future.wait([
            _initializeFollowCache(user.uid),
            _initializeLikeCache(user.uid),
            _initializeFavoriteCache(user.uid),
            _initializeViewTrackingCache(user.uid),
          ]);

          return AppUser.fromFirebaseUser(user);
        }

        throw Exception('登入失敗：無法獲取用戶資訊');
      } catch (error, stackTrace) {
        // 記錄登入失敗事件
        try {
          await FirebaseAnalytics.instance.logEvent(
            name: 'login_failed',
            parameters: {
              'method': 'google',
              'error': error.toString(),
            },
          );

          // 使用增強的 Analytics 服務記錄錯誤
          await enhancedAnalytics.logError(
            error,
            stackTrace,
            reason: 'Google sign in failed',
            additionalData: {
              'method': 'google',
              'error_type': error.runtimeType.toString(),
            },
          );
        } catch (e) {
          debugPrint('Analytics 記錄失敗: $e');
        }

        throw Exception('Google 登入失敗: $error');
      }
    });
  }

  /// 使用 Facebook 登入
  Future<void> signInWithFacebook() async {
    debugPrint('🔵 [AuthProvider] Facebook 登入開始');
    debugPrint('🔵 [AuthProvider] 設定狀態為 loading');
    state = const AsyncValue.loading();

    // 在開發模式下顯示配置指南
    if (kDebugMode) {
      FacebookDebugHelper.showConfigurationGuide();
      await FacebookDebugHelper.checkFacebookConfiguration();
    }

    debugPrint('🔵 [AuthProvider] 開始執行 AsyncValue.guard');
    state = await AsyncValue.guard(() async {
      try {
        debugPrint('🔵 [AuthProvider] 開始 Facebook 登入流程');

        // 檢查 Facebook SDK 狀態
        debugPrint('🔵 [AuthProvider] 檢查 Facebook SDK 狀態');
        try {
          final accessToken = await FacebookAuth.instance.accessToken;
          debugPrint('🔵 [AuthProvider] 當前 Facebook AccessToken: $accessToken');
        } catch (e) {
          debugPrint('🔵 [AuthProvider] 無當前 Facebook AccessToken: $e');
        }

        // 開始 Facebook 登入流程
        debugPrint('🔵 [AuthProvider] 調用 FacebookAuth.instance.login');
        final LoginResult result = await FacebookAuth.instance.login(
          permissions: ['email', 'public_profile'],
        );

        debugPrint('🔵 [AuthProvider] Facebook 登入結果: ${result.status}');
        debugPrint('🔵 [AuthProvider] Facebook 登入訊息: ${result.message}');
        debugPrint('🔵 [AuthProvider] Facebook 登入詳細資訊: $result');

        // 🔍 打印 LoginResult 的所有屬性
        print('🔍 [AuthProvider] LoginResult 詳細資訊:');
        print('🔍 [AuthProvider] - Status: ${result.status}');
        print('🔍 [AuthProvider] - Message: ${result.message}');
        print('🔍 [AuthProvider] - AccessToken: ${result.accessToken}');
        print(
            '🔍 [AuthProvider] - AccessToken 是否為 null: ${result.accessToken == null}');

        // 額外的診斷信息
        if (result.status == LoginStatus.failed) {
          debugPrint('❌ [AuthProvider] Facebook 登入失敗詳細信息:');
          debugPrint('❌ [AuthProvider] - Status: ${result.status}');
          debugPrint('❌ [AuthProvider] - Message: ${result.message}');
          debugPrint('❌ [AuthProvider] - AccessToken: ${result.accessToken}');
        }

        if (result.status == LoginStatus.success) {
          debugPrint('🔵 [AuthProvider] Facebook 登入成功，獲取存取權杖');

          // 獲取 Facebook 存取權杖
          final AccessToken accessToken = result.accessToken!;
          debugPrint(
              '🔵 [AuthProvider] Facebook AccessToken: ${accessToken.token.substring(0, 20)}...');
          debugPrint(
              '🔵 [AuthProvider] Facebook AccessToken 完整資訊: $accessToken');

          // 🔍 添加詳細的 Token 資訊調試 (使用 print 確保 release 版本也能顯示)
          print('🔍 [AuthProvider] Token 詳細資訊:');
          print('🔍 [AuthProvider] - Token 長度: ${accessToken.token.length}');
          print('🔍 [AuthProvider] - 過期時間: ${accessToken.expires}');
          print('🔍 [AuthProvider] - 權限: ${accessToken.grantedPermissions}');
          print('🔍 [AuthProvider] - 拒絕權限: ${accessToken.declinedPermissions}');
          print('🔍 [AuthProvider] - 用戶 ID: ${accessToken.userId}');
          print('🔍 [AuthProvider] - 應用 ID: ${accessToken.applicationId}');

          // 🔍 獲取 Facebook 用戶資料
          try {
            print('🔍 [AuthProvider] 開始獲取 Facebook 用戶資料...');
            final userData = await FacebookAuth.instance.getUserData();
            print('🔍 [AuthProvider] Facebook 用戶資料:');
            print('🔍 [AuthProvider] - 完整資料: $userData');

            // 逐一打印每個欄位
            userData.forEach((key, value) {
              print('🔍 [AuthProvider] - $key: $value');
            });

            // 特別檢查重要欄位
            print('🔍 [AuthProvider] 重要欄位檢查:');
            print('🔍 [AuthProvider] - ID: ${userData['id']}');
            print('🔍 [AuthProvider] - Name: ${userData['name']}');
            print('🔍 [AuthProvider] - Email: ${userData['email']}');
            print('🔍 [AuthProvider] - Picture: ${userData['picture']}');
            print('🔍 [AuthProvider] - First Name: ${userData['first_name']}');
            print('🔍 [AuthProvider] - Last Name: ${userData['last_name']}');
          } catch (userDataError) {
            print('❌ [AuthProvider] 獲取 Facebook 用戶資料失敗: $userDataError');
          }

          // 使用 Facebook 憑證登入 Firebase Auth
          print('🔥 [AuthProvider] 開始使用 Facebook 憑證登入 Firebase Auth');

          // 創建 Firebase Auth 憑證
          final OAuthCredential facebookAuthCredential =
              FacebookAuthProvider.credential(accessToken.token);

          print('🔥 [AuthProvider] 創建 Facebook Auth 憑證完成');

          // 使用憑證登入 Firebase Auth
          final UserCredential userCredential =
              await _firebaseAuth!.signInWithCredential(facebookAuthCredential);

          final User? firebaseUser = userCredential.user;

          if (firebaseUser == null) {
            throw Exception('Firebase Auth 登入失敗');
          }

          print('🔥 [AuthProvider] Firebase Auth 登入成功');
          print('🔥 [AuthProvider] Firebase UID: ${firebaseUser.uid}');
          print('🔥 [AuthProvider] Firebase Email: ${firebaseUser.email}');
          print(
              '🔥 [AuthProvider] Firebase DisplayName: ${firebaseUser.displayName}');

          // 獲取 Facebook 用戶資料以補充資訊
          final userData = await FacebookAuth.instance.getUserData();
          print('🔥 [AuthProvider] 獲取 Facebook 用戶資料完成');

          // 更新 Firebase 用戶檔案（如果需要）
          if (firebaseUser.displayName == null ||
              firebaseUser.photoURL == null) {
            await firebaseUser.updateProfile(
              displayName: userData['name'] as String?,
              photoURL: _extractPhotoUrl(userData['picture']),
            );
            await firebaseUser.reload();
            print('🔥 [AuthProvider] 更新 Firebase 用戶檔案完成');
          }

          // 儲存額外的用戶資料到 Firestore
          await _saveUserToFirestoreWithFacebookData(
              firebaseUser, userData, accessToken.token);

          // 記錄登入成功事件
          try {
            await FirebaseAnalytics.instance.logLogin(loginMethod: 'facebook');
            print('🔥 [AuthProvider] Analytics 成功事件記錄成功');
          } catch (e) {
            print('❌ [AuthProvider] Analytics 記錄失敗: $e');
          }

          // 返回 AppUser
          final appUser = AppUser.fromFirebaseUser(firebaseUser);
          print('🔥 [AuthProvider] Facebook + Firebase Auth 登入完成');
          return appUser;
        } else if (result.status == LoginStatus.cancelled) {
          debugPrint('🔵 [AuthProvider] Facebook 登入已取消');
          throw Exception('Facebook 登入已取消');
        } else {
          debugPrint('❌ [AuthProvider] Facebook 登入失敗：${result.message}');
          throw Exception('Facebook 登入失敗：${result.message}');
        }
      } catch (error) {
        debugPrint('❌ [AuthProvider] Facebook 登入過程中發生錯誤: $error');
        debugPrint('❌ [AuthProvider] 錯誤類型: ${error.runtimeType}');
        debugPrint('❌ [AuthProvider] 錯誤堆疊: ${StackTrace.current}');

        // 記錄登入失敗事件
        try {
          debugPrint('🔵 [AuthProvider] 開始記錄 Analytics 失敗事件');
          await FirebaseAnalytics.instance.logEvent(
            name: 'login_failed',
            parameters: {
              'method': 'facebook',
              'error': error.toString(),
            },
          );
          debugPrint('🔵 [AuthProvider] Analytics 失敗事件記錄成功');
        } catch (e) {
          debugPrint('❌ [AuthProvider] Analytics 記錄失敗: $e');
        }

        debugPrint('❌ [AuthProvider] 拋出 Facebook 登入失敗異常');
        throw Exception('Facebook 登入失敗: $error');
      }
    });

    debugPrint('🔵 [AuthProvider] AsyncValue.guard 執行完成');
    debugPrint('🔵 [AuthProvider] 最終狀態: $state');
  }

  /// 使用 Email 和密碼登入
  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      try {
        final UserCredential userCredential =
            await _firebaseAuth!.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        final User? user = userCredential.user;
        if (user != null) {
          // 記錄登入成功事件
          try {
            await FirebaseAnalytics.instance.logLogin(loginMethod: 'email');
          } catch (e) {
            debugPrint('Analytics 記錄失敗: $e');
          }

          // 同步用戶互動狀態到本地快取
          await _syncUserInteractions(user.uid);

          // 初始化快取（並行執行提升速度）
          await Future.wait([
            _initializeFollowCache(user.uid),
            _initializeLikeCache(user.uid),
            _initializeFavoriteCache(user.uid),
            _initializeViewTrackingCache(user.uid),
          ]);

          return AppUser.fromFirebaseUser(_firebaseAuth!.currentUser!);
        }

        throw Exception('Email 登入失敗：無法獲取用戶資訊');
      } catch (error) {
        // 記錄登入失敗事件
        try {
          await FirebaseAnalytics.instance.logEvent(
            name: 'login_failed',
            parameters: {
              'method': 'email',
              'error': error.toString(),
            },
          );
        } catch (e) {
          debugPrint('Analytics 記錄失敗: $e');
        }

        throw Exception('Email 登入失敗: $error');
      }
    });
  }

  /// 使用 Email 和密碼註冊
  Future<void> signUpWithEmail({
    required String email,
    required String password,
    String? displayName,
  }) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      try {
        final UserCredential userCredential =
            await _firebaseAuth!.createUserWithEmailAndPassword(
          email: email,
          password: password,
        );

        final User? user = userCredential.user;
        if (user != null) {
          // 設定顯示名稱
          if (displayName != null && displayName.trim().isNotEmpty) {
            await user.updateDisplayName(displayName.trim());
            await user.reload();
          }

          // 發送郵件驗證
          if (!user.emailVerified) {
            await user.sendEmailVerification();
          }

          // 記錄註冊成功事件
          try {
            await FirebaseAnalytics.instance.logSignUp(signUpMethod: 'email');
          } catch (e) {
            debugPrint('Analytics 記錄失敗: $e');
          }

          // 獲取更新後的用戶對象並儲存到 Firestore
          final updatedUser = _firebaseAuth!.currentUser!;
          await _saveUserToFirestore(updatedUser);

          // 同步用戶互動狀態到本地快取
          await _syncUserInteractions(user.uid);

          // 初始化快取（並行執行提升速度）
          await Future.wait([
            _initializeFollowCache(user.uid),
            _initializeLikeCache(user.uid),
            _initializeFavoriteCache(user.uid),
            _initializeViewTrackingCache(user.uid),
          ]);

          return AppUser.fromFirebaseUser(_firebaseAuth!.currentUser!);
        }

        throw Exception('Email 註冊失敗：無法獲取用戶資訊');
      } catch (error) {
        // 記錄註冊失敗事件
        try {
          await FirebaseAnalytics.instance.logEvent(
            name: 'signup_failed',
            parameters: {
              'method': 'email',
              'error': error.toString(),
            },
          );
        } catch (e) {
          debugPrint('Analytics 記錄失敗: $e');
        }

        throw Exception('Email 註冊失敗: $error');
      }
    });
  }

  /// 重設密碼
  Future<void> resetPassword({required String email}) async {
    try {
      await _firebaseAuth!.sendPasswordResetEmail(email: email);

      // 記錄密碼重設事件
      try {
        await FirebaseAnalytics.instance.logEvent(
          name: 'password_reset_requested',
          parameters: {'email': email},
        );
      } catch (e) {
        debugPrint('Analytics 記錄失敗: $e');
      }
    } catch (error) {
      // 記錄密碼重設失敗事件
      try {
        await FirebaseAnalytics.instance.logEvent(
          name: 'password_reset_failed',
          parameters: {
            'email': email,
            'error': error.toString(),
          },
        );
      } catch (e) {
        debugPrint('Analytics 記錄失敗: $e');
      }

      throw Exception('密碼重設失敗: $error');
    }
  }

  /// 使用 Apple 登入（iOS 專用）
  Future<void> signInWithApple() async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      try {
        // 檢查是否支援 Apple 登入
        if (!Platform.isIOS) {
          throw Exception('Apple 登入僅支援 iOS 平台');
        }

        // 檢查是否可用
        final isAvailable = await SignInWithApple.isAvailable();
        if (!isAvailable) {
          throw Exception('Apple 登入不可用');
        }

        // 開始 Apple 登入流程
        final credential = await SignInWithApple.getAppleIDCredential(
          scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ],
        );

        // 建立 Firebase 認證憑證
        final oauthCredential = OAuthProvider('apple.com').credential(
          idToken: credential.identityToken,
          accessToken: credential.authorizationCode,
        );

        // 使用憑證登入 Firebase
        final UserCredential userCredential =
            await _firebaseAuth!.signInWithCredential(oauthCredential);
        final User? user = userCredential.user;

        if (user != null) {
          // 如果用戶沒有顯示名稱，使用 Apple 提供的名稱
          if (user.displayName == null && credential.givenName != null) {
            final displayName =
                '${credential.givenName} ${credential.familyName ?? ''}'.trim();
            if (displayName.isNotEmpty) {
              await user.updateDisplayName(displayName);
              await user.reload();
            }
          }

          // 記錄登入成功事件
          try {
            await FirebaseAnalytics.instance.logLogin(loginMethod: 'apple');
          } catch (e) {
            debugPrint('Analytics 記錄失敗: $e');
          }

          await _saveUserToFirestore(user);

          // 同步用戶互動狀態到本地快取
          await _syncUserInteractions(user.uid);

          // 初始化快取（並行執行提升速度）
          await Future.wait([
            _initializeFollowCache(user.uid),
            _initializeLikeCache(user.uid),
            _initializeFavoriteCache(user.uid),
            _initializeViewTrackingCache(user.uid),
          ]);

          return AppUser.fromFirebaseUser(_firebaseAuth!.currentUser!);
        }

        throw Exception('Apple 登入失敗：無法獲取用戶資訊');
      } catch (error) {
        // 記錄登入失敗事件
        try {
          await FirebaseAnalytics.instance.logEvent(
            name: 'login_failed',
            parameters: {
              'method': 'apple',
              'error': error.toString(),
            },
          );
        } catch (e) {
          debugPrint('Analytics 記錄失敗: $e');
        }

        throw Exception('Apple 登入失敗: $error');
      }
    });
  }

  /// 登出
  Future<void> logout() async {
    debugPrint('🔵 [AuthProvider] 開始登出流程');
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      try {
        // 記錄登出事件
        try {
          await FirebaseAnalytics.instance.logEvent(name: 'logout');
          debugPrint('🔵 [AuthProvider] Analytics 登出事件記錄成功');

          // 使用增強的 Analytics 服務記錄更詳細的資訊
          final currentUser = _firebaseAuth!.currentUser;
          if (currentUser != null) {
            await enhancedAnalytics.logEvent('logout', parameters: {
              'user_id': currentUser.uid,
              'session_duration': DateTime.now()
                  .difference(
                      currentUser.metadata.lastSignInTime ?? DateTime.now())
                  .inMinutes,
            });
          }

          // 清除用戶資料
          await enhancedAnalytics.clearUserData();
        } catch (e) {
          debugPrint('❌ [AuthProvider] Analytics 記錄失敗: $e');
        }

        // 登出 Facebook
        try {
          await FacebookAuth.instance.logOut();
          debugPrint('🔵 [AuthProvider] Facebook 登出成功');
        } catch (e) {
          debugPrint('❌ [AuthProvider] Facebook 登出失敗: $e');
        }

        // 登出 Google
        if (await _googleSignIn!.isSignedIn()) {
          await _googleSignIn!.signOut();
          debugPrint('🔵 [AuthProvider] Google 登出成功');
        }

        // 登出 Firebase
        await _firebaseAuth!.signOut();
        debugPrint('🔵 [AuthProvider] Firebase 登出成功');

        // 清除本地快取
        await _clearUserCache();
        debugPrint('🔵 [AuthProvider] 本地快取清除完成');

        // 清除所有 Riverpod Provider 快取
        await _clearProviderCache();
        debugPrint('🔵 [AuthProvider] Provider 快取清除完成');

        debugPrint('✅ [AuthProvider] 登出流程完成');
        return null;
      } catch (error) {
        debugPrint('❌ [AuthProvider] 登出失敗: $error');
        throw Exception('登出失敗: $error');
      }
    });
  }

  /// 刪除帳號
  Future<void> deleteAccount() async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      try {
        final User? user = _firebaseAuth!.currentUser;
        if (user == null) {
          throw Exception('用戶未登入');
        }

        // 登出 Facebook
        try {
          await FacebookAuth.instance.logOut();
          debugPrint('🔵 [AuthProvider] Facebook 登出成功');
        } catch (e) {
          debugPrint('❌ [AuthProvider] Facebook 登出失敗: $e');
        }

        // 登出 Google
        if (await _googleSignIn!.isSignedIn()) {
          await _googleSignIn!.signOut();
          debugPrint('🔵 [AuthProvider] Google 登出成功');
        }

        // 清除本地快取（在刪除Firebase帳號前進行，因為需要用戶ID）
        await _clearUserCache();
        debugPrint('🔵 [AuthProvider] 本地快取清除完成');

        // 刪除 Firebase 帳號
        await user.delete();
        debugPrint('🔵 [AuthProvider] Firebase 帳號刪除成功');

        // 清除所有 Riverpod Provider 快取
        await _clearProviderCache();
        debugPrint('🔵 [AuthProvider] Provider 快取清除完成');

        return null;
      } catch (error) {
        throw Exception('刪除帳號失敗: $error');
      }
    });
  }

  /// 重新認證（用於敏感操作前）
  Future<void> reauthenticate() async {
    try {
      final User? user = _firebaseAuth!.currentUser;
      if (user == null) {
        throw Exception('用戶未登入');
      }

      // Google 重新認證
      final GoogleSignInAccount? googleUser = await _googleSignIn!.signIn();
      if (googleUser != null) {
        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;
        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );
        await user.reauthenticateWithCredential(credential);
      }
    } catch (error) {
      throw Exception('重新認證失敗: $error');
    }
  }

  /// 儲存用戶資料到 Firestore
  Future<void> _saveUserToFirestore(User user) async {
    try {
      debugPrint('🔵 [AuthProvider] _saveUserToFirestore 開始，用戶: ${user.uid}');
      final firestore = FirebaseFirestore.instance;

      // 生成兩個不同的 hash：一個用於一般發文，一個用於匿名發文
      debugPrint('🔵 [AuthProvider] 生成用戶 hash');
      final publicHash = _generatePublicUserHash(user.uid);
      final anonymousHash = _generateAnonymousUserHash(user.uid);

      // 獲取用戶IP地址
      debugPrint('🔵 [AuthProvider] 獲取用戶 IP 地址');
      final ipAddress = await IpAddressService.getCurrentIpAddress();
      debugPrint('🔵 [AuthProvider] 用戶 IP 地址: $ipAddress');

      // 獲取系統語系
      debugPrint('🔵 [AuthProvider] 獲取系統語系');
      final systemLocale = ui.PlatformDispatcher.instance.locale;
      final languageCode =
          '${systemLocale.languageCode}-${systemLocale.countryCode ?? systemLocale.languageCode.toUpperCase()}';
      debugPrint('🔵 [AuthProvider] 系統語系: $languageCode');

      final userData = {
        'uid': user.uid,
        'email': user.email,
        'displayName': user.displayName,
        'photoURL': user.photoURL,
        'createdAt': user.metadata.creationTime ?? FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'isActive': true,
        'loginMethod': user.providerData.isNotEmpty
            ? user.providerData.first.providerId
            : 'unknown',
        // 兩個不同的 hash，確保匿名發文與一般發文的隱私隔離
        'publicHash': publicHash,
        'anonymousHash': anonymousHash,
        // 新增IP地址和語系資訊
        if (ipAddress != null) 'ipAddress': ipAddress,
        'language': languageCode,
        // 預設通知設定
        'notificationSettings':
            NotificationSettings.defaultSettings.toFirestore(),
      };

      debugPrint('🔵 [AuthProvider] 準備儲存用戶資料: $userData');
      await firestore.collection('users').doc(user.uid).set(
            userData,
            SetOptions(merge: true),
          );

      debugPrint('✅ [AuthProvider] 用戶資料已儲存到 Firestore: ${user.uid}');
      debugPrint('🔵 [AuthProvider] 公開 Hash: $publicHash');
      debugPrint('🔵 [AuthProvider] 匿名 Hash: $anonymousHash');
      debugPrint('🔵 [AuthProvider] IP地址: $ipAddress');
      debugPrint('🔵 [AuthProvider] 語系: $languageCode');
    } catch (error) {
      debugPrint('❌ [AuthProvider] 儲存用戶資料失敗: $error');
      debugPrint('❌ [AuthProvider] 錯誤類型: ${error.runtimeType}');
      // 不拋出錯誤，避免影響登入流程
    }
  }

  /// 生成一般發文用的 hash（與用戶身份關聯）
  String _generatePublicUserHash(String userId) {
    final publicSeed = '$userId-public-v1';
    final hash = publicSeed.hashCode.toRadixString(16).toUpperCase();
    return 'PUB_$hash';
  }

  /// 生成匿名發文用的 hash（獨立於用戶身份）
  String _generateAnonymousUserHash(String userId) {
    final anonymousSeed = '$userId-anonymous-v1';
    final hash = anonymousSeed.hashCode.toRadixString(16).toUpperCase();
    return 'ANON_$hash';
  }

  /// 同步用戶互動狀態到本地快取
  Future<void> _syncUserInteractions(String userId) async {
    try {
      debugPrint(
          '🔄 [AuthProvider] Starting sync user interactions for: $userId');

      // 創建 HomeRepository 實例來同步互動狀態
      final homeRepository = HomeRepositoryImpl();
      await homeRepository.syncUserInteractions(userId);

      debugPrint('✅ [AuthProvider] User interactions sync completed');
    } catch (error) {
      debugPrint('❌ AuthProvider: Failed to sync user interactions: $error');
      // 不讓同步失敗影響登入流程
    }
  }

  /// 📥 初始化用戶追蹤快取
  Future<void> _initializeFollowCache(String userId) async {
    try {
      debugPrint('📥 [AuthProvider] Initializing follow cache for: $userId');

      // 創建帶快取的追蹤服務
      final followService = await UserFollowService.createWithCache();

      // 初始化用戶追蹤快取
      await followService.initializeUserCache(userId);

      debugPrint(
          '✅ [AuthProvider] Follow cache initialized successfully for: $userId');
    } catch (error) {
      debugPrint('❌ [AuthProvider] Failed to initialize follow cache: $error');
      // 不讓快取初始化失敗影響登入流程
    }
  }

  /// 👍 初始化用戶點讚快取
  Future<void> _initializeLikeCache(String userId) async {
    try {
      debugPrint('👍 [AuthProvider] Initializing like cache for: $userId');

      // 創建帶快取的點讚服務
      final likeService = await UserLikeService.createWithCache();

      // 初始化用戶點讚快取
      await likeService.initializeUserCache(userId);

      debugPrint(
          '✅ [AuthProvider] Like cache initialized successfully for: $userId');
    } catch (error) {
      debugPrint('❌ [AuthProvider] Failed to initialize like cache: $error');
      // 不讓快取初始化失敗影響登入流程
    }
  }

  /// ⭐ 初始化用戶收藏快取
  Future<void> _initializeFavoriteCache(String userId) async {
    try {
      debugPrint('⭐ [AuthProvider] Initializing favorite cache for: $userId');

      // 創建帶快取的收藏服務
      final favoriteService = await UserFavoriteService.createWithCache();

      // 初始化用戶收藏快取
      await favoriteService.initializeUserCache(userId);

      debugPrint(
          '✅ [AuthProvider] Favorite cache initialized successfully for: $userId');
    } catch (error) {
      debugPrint(
          '❌ [AuthProvider] Failed to initialize favorite cache: $error');
      // 不讓快取初始化失敗影響登入流程
    }
  }

  /// 📊 初始化用戶查看記錄快取
  Future<void> _initializeViewTrackingCache(String userId) async {
    try {
      debugPrint(
          '📊 [AuthProvider] Initializing view tracking cache for: $userId');

      // 創建帶快取的查看記錄服務
      final viewTrackingService = await ViewTrackingService.createWithCache();

      // 初始化用戶查看記錄快取
      await viewTrackingService.initializeUserCache(userId);

      debugPrint(
          '✅ [AuthProvider] View tracking cache initialized successfully for: $userId');
    } catch (error) {
      debugPrint(
          '❌ [AuthProvider] Failed to initialize view tracking cache: $error');
      // 不讓快取初始化失敗影響登入流程
    }
  }

  /// 清除用戶快取
  Future<void> _clearUserCache() async {
    try {
      debugPrint('🗑️ [AuthProvider] Clearing user cache');

      // 獲取當前用戶ID
      final currentUser = _firebaseAuth?.currentUser;
      final userId = currentUser?.uid;

      // 清理 InteractionCacheService 快取
      final cacheService = InteractionCacheService.instance;
      await cacheService.clearAllCache();

      // 清理用戶特定的服務快取（如果有用戶ID）
      if (userId != null) {
        debugPrint(
            '🗑️ [AuthProvider] Clearing user-specific service caches for: $userId');

        try {
          // 清理用戶點讚快取
          final likeService = await UserLikeService.createWithCache();
          await likeService.clearUserCache(userId);
          debugPrint('✅ [AuthProvider] Like cache cleared for user: $userId');
        } catch (error) {
          debugPrint('❌ [AuthProvider] Failed to clear like cache: $error');
        }

        try {
          // 清理用戶收藏快取
          final favoriteService = await UserFavoriteService.createWithCache();
          await favoriteService.clearUserCache(userId);
          debugPrint(
              '✅ [AuthProvider] Favorite cache cleared for user: $userId');
        } catch (error) {
          debugPrint('❌ [AuthProvider] Failed to clear favorite cache: $error');
        }

        try {
          // 清理用戶追蹤快取
          final followService = await UserFollowService.createWithCache();
          await followService.clearUserCache(userId);
          debugPrint('✅ [AuthProvider] Follow cache cleared for user: $userId');
        } catch (error) {
          debugPrint('❌ [AuthProvider] Failed to clear follow cache: $error');
        }

        try {
          // 清理用戶查看記錄快取
          final viewTrackingService =
              await ViewTrackingService.createWithCache();
          await viewTrackingService.clearUserCache();
          debugPrint(
              '✅ [AuthProvider] View tracking cache cleared for user: $userId');
        } catch (error) {
          debugPrint(
              '❌ [AuthProvider] Failed to clear view tracking cache: $error');
        }
      }

      debugPrint('✅ [AuthProvider] All user caches cleared');
    } catch (error) {
      debugPrint('❌ [AuthProvider] Failed to clear user cache: $error');
      // 不讓清除快取失敗影響登出流程
    }
  }

  /// 清除 Provider 快取
  Future<void> _clearProviderCache() async {
    try {
      debugPrint('🗑️ [AuthProvider] Clearing provider cache');

      // 清除所有 ProfileNotifier 的快取
      // 這會強制重新載入用戶資料
      ref.invalidate(profileNotifierProvider);

      // 清除當前用戶 Provider 快取
      ref.invalidate(currentUserProvider);

      // 清除封鎖用戶的狀態快取
      try {
        // 先清空狀態，再 invalidate
        final blockedNotifier = ref.read(blockedUsersProvider.notifier);
        blockedNotifier.state = [];
        ref.invalidate(blockedUsersProvider);
        debugPrint('✅ [AuthProvider] Blocked users provider cleared');
      } catch (error) {
        debugPrint(
            '❌ [AuthProvider] Failed to clear blocked users provider: $error');
      }

      // 清除草稿 Provider 狀態
      try {
        ref.invalidate(draftProvider);
        debugPrint('✅ [AuthProvider] Draft provider cleared');
      } catch (error) {
        debugPrint('❌ [AuthProvider] Failed to clear draft provider: $error');
      }

      // 🎯 清除批量交互狀態快取（關鍵修復）
      try {
        // 先清空狀態，再 invalidate provider
        final batchNotifier = ref.read(batchInteractionProvider.notifier);
        batchNotifier.clearCache();
        ref.invalidate(batchInteractionProvider);
        debugPrint('✅ [AuthProvider] Batch interaction provider cleared');
      } catch (error) {
        debugPrint(
            '❌ [AuthProvider] Failed to clear batch interaction provider: $error');
      }

      debugPrint('✅ [AuthProvider] Provider cache cleared');
    } catch (error) {
      debugPrint('❌ [AuthProvider] Failed to clear provider cache: $error');
      // 不讓清除快取失敗影響登出流程
    }
  }

  /// 檢查並更新用戶IP地址（app冷啟動時調用）
  Future<void> checkAndUpdateIpAddress() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      debugPrint(
          '🌐 AuthProvider: Checking IP address for user: ${currentUser.uid}');

      // 檢查IP是否有變更
      final hasChanged = await IpAddressService.hasIpAddressChanged();
      if (hasChanged) {
        final newIp = await IpAddressService.getCurrentIpAddress();
        if (newIp != null) {
          // 更新Firestore中的IP地址
          await FirebaseFirestore.instance
              .collection('users')
              .doc(currentUser.uid)
              .update({
            'ipAddress': newIp,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          debugPrint('✅ AuthProvider: IP address updated to: $newIp');
        }
      } else {
        debugPrint('✅ AuthProvider: IP address unchanged');
      }
    } catch (error) {
      debugPrint('❌ AuthProvider: Failed to check/update IP address: $error');
      // 不讓IP更新失敗影響app啟動
    }
  }

  /// 儲存用戶資料到 Firestore (Facebook 登入版本)
  Future<void> _saveUserToFirestoreWithFacebookData(User firebaseUser,
      Map<String, dynamic>? facebookData, String? accessToken) async {
    try {
      print('🔥 [AuthProvider] 開始儲存用戶資料到 Firestore');

      final userDoc =
          FirebaseFirestore.instance.collection('users').doc(firebaseUser.uid);

      // 檢查用戶是否已存在
      final docSnapshot = await userDoc.get();
      final now = DateTime.now();

      final userDataToSave = {
        'uid': firebaseUser.uid,
        'email': firebaseUser.email,
        'displayName': firebaseUser.displayName,
        'photoURL': firebaseUser.photoURL,
        'lastLoginAt': now,
        'updatedAt': now,
      };

      // 如果是 Facebook 登入，添加 Facebook 相關資訊
      if (facebookData != null) {
        userDataToSave['facebookId'] = facebookData['id'];
        userDataToSave['loginMethod'] = 'facebook';
        if (accessToken != null) {
          userDataToSave['facebookAccessToken'] = accessToken;
        }
      }

      if (docSnapshot.exists) {
        // 更新現有用戶
        await userDoc.update(userDataToSave);
        print('🔥 [AuthProvider] 更新現有用戶資料完成');
      } else {
        // 創建新用戶
        userDataToSave['createdAt'] = now;
        await userDoc.set(userDataToSave);
        print('🔥 [AuthProvider] 創建新用戶資料完成');
      }
    } catch (error) {
      print('❌ [AuthProvider] 儲存用戶資料失敗: $error');
      rethrow;
    }
  }

  /// 從 Facebook 圖片資料中提取 URL
  String? _extractPhotoUrl(dynamic pictureData) {
    try {
      if (pictureData is Map<String, dynamic>) {
        final data = pictureData['data'] as Map<String, dynamic>?;
        return data?['url'] as String?;
      }
      return null;
    } catch (e) {
      print('❌ [AuthProvider] 提取圖片 URL 失敗: $e');
      return null;
    }
  }

  /// 📋 獲取完整的用戶數據（包含通知設定）
  Future<AppUser?> _fetchCompleteUserData(User firebaseUser) async {
    try {
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(firebaseUser.uid)
          .get();

      final firestoreData = userDoc.exists ? userDoc.data() : null;
      return AppUser.fromFirestoreData(firebaseUser, firestoreData);
    } catch (error) {
      print('❌ [AuthProvider] 獲取完整用戶數據失敗: $error');
      // 回退到基本用戶數據
      return AppUser.fromFirebaseUser(firebaseUser);
    }
  }

  /// 📋 更新用戶通知設定
  Future<void> updateNotificationSettings(NotificationSettings settings) async {
    final currentUser = _firebaseAuth?.currentUser;
    if (currentUser == null) return;

    try {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .update({
        'notificationSettings': settings.toFirestore(),
      });

      // 更新本地狀態
      final currentAppUser = state.value;
      if (currentAppUser != null) {
        state = AsyncValue.data(
          currentAppUser.copyWith(notificationSettings: settings),
        );
      }
    } catch (error) {
      print('❌ [AuthProvider] 更新通知設定失敗: $error');
      throw Exception('更新通知設定失敗: $error');
    }
  }
}

/// 認證 Provider
final authProvider = AsyncNotifierProvider<AuthNotifier, AppUser?>(() {
  return AuthNotifier();
});

/// 便利性 Provider

/// 當前用戶 Provider
final currentUserProvider = Provider<AppUser?>((ref) {
  final authState = ref.watch(authProvider);
  debugPrint('🔍 [currentUserProvider] authState: $authState');

  return authState.when(
    data: (user) {
      debugPrint('🔍 [currentUserProvider] data: $user');
      return user;
    },
    loading: () {
      debugPrint('🔍 [currentUserProvider] loading state');
      // 在載入狀態時，嘗試從 FirebaseAuth 獲取當前用戶
      final currentUser = FirebaseAuth.instance.currentUser;
      final appUser =
          currentUser != null ? AppUser.fromFirebaseUser(currentUser) : null;
      debugPrint('🔍 [currentUserProvider] loading fallback: $appUser');
      return appUser;
    },
    error: (error, stack) {
      debugPrint('❌ [currentUserProvider] error: $error');
      // 錯誤狀態時也嘗試從 FirebaseAuth 獲取當前用戶
      final currentUser = FirebaseAuth.instance.currentUser;
      final appUser =
          currentUser != null ? AppUser.fromFirebaseUser(currentUser) : null;
      debugPrint('🔍 [currentUserProvider] error fallback: $appUser');
      return appUser;
    },
  );
});

/// 是否已登入 Provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).maybeWhen(
        data: (user) => user != null,
        orElse: () => false,
      );
});

/// 認證狀態 Provider
final authStateProvider = Provider<AsyncValue<AppUser?>>((ref) {
  return ref.watch(authProvider);
});

/// 認證錯誤 Provider
final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).maybeWhen(
        error: (error, stack) => error.toString(),
        orElse: () => null,
      );
});

/// 認證載入狀態 Provider
final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).maybeWhen(
        loading: () => true,
        orElse: () => false,
      );
});

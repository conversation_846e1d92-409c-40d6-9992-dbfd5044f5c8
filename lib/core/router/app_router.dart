// lib/core/router/app_router.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/explore/presentation/screens/explore_screen.dart';
import '../../features/favorite/presentation/screens/favorite_screen.dart';
import '../../features/activity/presentation/screens/activity_screen.dart';
import '../../features/home/<USER>/screens/home_screen.dart';
import '../../features/home/<USER>/screens/create_post_screen.dart';
import '../../features/settings/presentation/screens/setting_screen.dart';
import '../../features/home/<USER>/screens/filtered_posts_screen.dart';
import '../../features/home/<USER>/screens/filtered_posts_screen_with_sliver.dart';
import '../../features/profile/presentation/screens/profile_screen.dart';
import '../../features/profile/presentation/screens/user_profile_screen.dart';
import '../../features/profile/presentation/screens/user_posts_screen.dart';
// 已移除舊的 user_likes_screen.dart 和 user_comments_screen.dart
import '../../features/profile/presentation/screens/anonymous_user_posts_screen.dart';
import '../navigation/main_navigation.dart';
import '../../features/splash/presentation/screens/splash_screen.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/notifications/presentation/screens/notifications_screen.dart';
import '../../features/home/<USER>/screens/search_screen.dart';
// 已移除舊的 post_detail_screen.dart
import '../../features/home/<USER>/pages/post_detail_page.dart';
import '../../features/notifications/presentation/pages/notification_settings_page.dart';
import '../../features/notifications/debug/notification_test_screen.dart';
import '../../features/settings/presentation/screens/blocked_users_screen.dart';
import '../../features/admin/presentation/pages/admin_dashboard_page.dart';
import '../../features/admin/presentation/pages/fake_accounts_management_page.dart';
import '../../features/admin/presentation/pages/users_management_page.dart';
import '../../features/legal/presentation/screens/privacy_policy_screen.dart';
import '../../features/legal/presentation/screens/terms_of_service_screen.dart';
import '../../features/admin/presentation/pages/content_management_page.dart';
import '../../features/auth/presentation/providers/auth_provider.dart';
import '../../features/explore/presentation/screens/country_ranking_screen.dart';
import '../../features/explore/presentation/screens/country_posts_screen.dart';
import '../../features/explore/presentation/screens/reputation_leaderboard_screen.dart';
import '../../features/app_update/presentation/screens/app_status_wrapper.dart';
// import '../../features/onboarding/presentation/screens/welcome_screen.dart'; // 未使用
import '../../features/onboarding/presentation/screens/simple_welcome_screen.dart';
import '../../features/explore/presentation/pages/hashtag_posts_page.dart';
import '../observers/analytics_route_observer.dart';
import '../services/enhanced_analytics_service.dart';

/// 🧭 APP 路由管理系統
/// 使用 GoRouter 管理所有頁面導航和路由邏輯

/// 全局 GoRouter 實例（用於通知導航）
late final GoRouter globalRouter;

/// 路由觀察者，用於調試路由變化
class _RouterObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint(
        '🧭 [RouterObserver] didPush: ${route.settings.name} (from: ${previousRoute?.settings.name})');
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint(
        '🧭 [RouterObserver] didPop: ${route.settings.name} (to: ${previousRoute?.settings.name})');
    super.didPop(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    debugPrint(
        '🧭 [RouterObserver] didReplace: ${oldRoute?.settings.name} -> ${newRoute?.settings.name}');
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint(
        '🧭 [RouterObserver] didRemove: ${route.settings.name} (previous: ${previousRoute?.settings.name})');
    super.didRemove(route, previousRoute);
  }
}

/// Analytics 路由觀察者實例
final analyticsObserver = AnalyticsRouteObserver();
final goRouterAnalyticsObserver = GoRouterAnalyticsObserver();

/// 路由提供者
final routerProvider = Provider<GoRouter>((ref) {
  final router = GoRouter(
    debugLogDiagnostics: true,
    initialLocation: '/splash',
    observers: [
      _RouterObserver(),
      analyticsObserver, // 添加 Analytics 觀察者
    ],
    redirect: (context, state) {
      // 處理 deep link scheme
      final location = state.uri.toString();
      debugPrint('🔗 [Router] 處理路由: $location');

      // 處理 hisohiso:// scheme
      if (location.startsWith('hisohiso://')) {
        final uri = Uri.parse(location);
        debugPrint(
            '🔗 [Router] 解析 deep link: host=${uri.host}, path=${uri.path}');

        // hisohiso://post/postId
        if (uri.host == 'post' && uri.pathSegments.isNotEmpty) {
          final postId = uri.pathSegments.first;
          debugPrint('🔗 [Router] 重定向到貼文: /post/$postId');
          return '/post/$postId';
        }

        // hisohiso:///post/postId (三個斜線)
        if (uri.host.isEmpty && uri.pathSegments.isNotEmpty) {
          if (uri.pathSegments.first == 'post' && uri.pathSegments.length > 1) {
            final postId = uri.pathSegments[1];
            debugPrint('🔗 [Router] 重定向到貼文（三斜線）: /post/$postId');
            return '/post/$postId';
          }
        }

        // 其他 deep link 格式
        if (uri.host == 'home') return '/home';
        if (uri.host == 'explore') return '/explore';
        if (uri.host == 'favorite') return '/favorite';

        // 預設返回首頁
        debugPrint('🔗 [Router] 未識別的 deep link，返回首頁');
        return '/home';
      }

      // ✅ 移除登入檢查邏輯，讓所有用戶都能進入主頁面瀏覽
      // 只有在特定操作時才要求登入
      return null;
    },
    routes: [
      // 根路由重定向到主頁面
      GoRoute(
        path: '/',
        redirect: (context, state) => '/home',
      ),

      // 啟動畫面
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // 歡迎/入門流程
      GoRoute(
        path: '/welcome',
        name: 'welcome',
        builder: (context, state) => const SimpleWelcomeScreen(),
      ),

      // 認證流程
      GoRoute(
        path: '/auth',
        name: 'auth',
        builder: (context, state) => _buildPlaceholderScreen(
          'Auth',
          'auth.title',
        ),
        routes: [
          GoRoute(
            path: 'login',
            name: 'login',
            builder: (context, state) => LoginScreen(
              onLoginSuccess: () {
                // 登入成功後返回上一頁或主頁面
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go('/home');
                }
              },
              onBack: context.canPop() ? () => context.pop() : null,
            ),
          ),
          GoRoute(
            path: 'register',
            name: 'register',
            builder: (context, state) => _buildPlaceholderScreen(
              'Register',
              'auth.register',
            ),
          ),
          GoRoute(
            path: 'forgot-password',
            name: 'forgot-password',
            builder: (context, state) => _buildPlaceholderScreen(
              'Forgot Password',
              'auth.forgot_password',
            ),
          ),
        ],
      ),

      // 主要應用流程
      ShellRoute(
        builder: (context, state, child) {
          // 解析當前路由對應的底部導航索引
          final index = _getNavigationIndex(state.uri.path);
          debugPrint(
              '🧭 [ShellRoute] 构建 MainNavigation，索引: $index，路径: ${state.uri.path}');
          debugPrint('🧭 [ShellRoute] child 类型: ${child.runtimeType}');

          // 使用 AppStatusWrapper 包裝 MainNavigation 以支援公告和更新檢查
          return AppStatusWrapper(
            child: MainNavigation(initialIndex: index),
          );
        },
        routes: [
          // 主頁面（地圖/列表）
          GoRoute(
            path: '/main',
            name: 'main',
            redirect: (context, state) => '/home',
          ),

          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomeScreen(), // 更新為新的 HomeScreen
          ),

          // 活動動態
          GoRoute(
            path: '/history',
            name: 'history',
            builder: (context, state) =>
                const ActivityScreen(), // 更新為新的 ActivityScreen
          ),

          GoRoute(
            path: '/explore',
            name: 'explore',
            builder: (context, state) => const ExploreScreen(),
          ),

          // 收藏
          GoRoute(
            path: '/favorite',
            name: 'favorite',
            builder: (context, state) =>
                const FavoriteScreen(), // 更新為新的 FavoriteScreen
          ),

          // 設定 (保留在 ShellRoute 中以維持底部導航)
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) =>
                const SettingsScreen(), // 更新為新的 SettingsScreen
          ),
        ],
      ),

      // 搜尋頁面 - 獨立頁面
      GoRoute(
        path: '/search',
        name: 'search',
        builder: (context, state) {
          return SearchScreen(
            initialFilterType: state.uri.queryParameters['filterType'],
            initialFilterValue: state.uri.queryParameters['filterValue'],
            initialTitle: state.uri.queryParameters['title'],
          );
        },
      ),
      // 通知測試頁面 - 調試工具
      GoRoute(
        path: '/notification-test',
        name: 'notification-test',
        builder: (context, state) {
          return const NotificationTestScreen();
        },
      ),

      // 篩選文章頁面 - 獨立頁面
      GoRoute(
        path: '/posts',
        name: 'posts',
        builder: (context, state) {
          final filterType = state.uri.queryParameters['filterType'];
          final filterValue = state.uri.queryParameters['filterValue'];
          final title = state.uri.queryParameters['title'];

          debugPrint('🔍 Router: 導航到 /posts 路由');
          debugPrint(
              '🔍 Router: filterType=$filterType, filterValue=$filterValue, title=$title');

          // 驗證必要參數
          if (filterType == null || filterValue == null || title == null) {
            debugPrint('❌ Router: 缺少必要參數，返回首頁');
            return const Scaffold(
              body: Center(
                child: Text('參數錯誤'),
              ),
            );
          }

          // 使用新的帶有可收縮 Header 的頁面
          return FilteredPostsScreenWithSliver(
            filterType: filterType,
            filterValue: filterValue,
            title: title,
          );
        },
      ),

      // 通知設定頁面
      GoRoute(
        path: '/notification-settings',
        name: 'notification-settings',
        builder: (context, state) => const NotificationSettingsPage(),
      ),

      // 封鎖管理頁面
      GoRoute(
        path: '/settings/blocked-users',
        name: 'blocked-users',
        builder: (context, state) => const BlockedUsersScreen(),
      ),

      // 其他獨立頁面
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) {
          final userId = state.uri.queryParameters['userId'];
          return ProfileScreen(userId: userId);
        },
      ),

      // 帳號資訊頁面 (實際上是個人資料頁面的帳號模式)
      GoRoute(
        path: '/account',
        name: 'account',
        builder: (context, state) => const ProfileScreen(),
      ),

      // 用戶資料頁面 (查看其他用戶的資料)
      GoRoute(
        path: '/user/:userId',
        name: 'user-profile',
        builder: (context, state) {
          final userId = state.pathParameters['userId'];
          if (userId == null) {
            return _buildErrorScreen(Exception('Invalid user ID'));
          }
          return UserProfileScreen(userId: userId);
        },
      ),

      // 用戶統計列表路由
      GoRoute(
        path: '/user-posts/:userId',
        name: 'user-posts',
        builder: (context, state) {
          final userId = state.pathParameters['userId'];
          if (userId == null) {
            return _buildErrorScreen(Exception('Invalid user ID'));
          }
          return UserPostsScreen(userId: userId);
        },
      ),

      GoRoute(
        path: '/user-likes/:userId',
        name: 'user-likes',
        builder: (context, state) {
          final userId = state.pathParameters['userId'];
          if (userId == null) {
            return _buildErrorScreen(Exception('Invalid user ID'));
          }
          return Scaffold(
            appBar: AppBar(title: const Text('用戶讚過的貼文')),
            body: const Center(child: Text('此功能暫時不可用')),
          );
        },
      ),

      GoRoute(
        path: '/user-comments/:userId',
        name: 'user-comments',
        builder: (context, state) {
          final userId = state.pathParameters['userId'];
          if (userId == null) {
            return _buildErrorScreen(Exception('Invalid user ID'));
          }
          return Scaffold(
            appBar: AppBar(title: const Text('用戶留言')),
            body: const Center(child: Text('此功能暫時不可用')),
          );
        },
      ),

      // 匿名用戶文章列表頁面
      GoRoute(
        path: '/anonymous-posts/:userHash',
        name: 'anonymous-posts',
        builder: (context, state) {
          final userHash = state.pathParameters['userHash'];
          final displayName = state.uri.queryParameters['displayName'];

          if (userHash == null) {
            return _buildErrorScreen(Exception('Invalid user hash'));
          }

          // displayName 可以為空，使用默認值
          final decodedDisplayName = displayName != null
              ? Uri.decodeQueryComponent(displayName)
              : '匿名用戶';

          return AnonymousUserPostsScreen(
            userHash: userHash,
            displayName: decodedDisplayName,
          );
        },
      ),

      // 創建貼文頁面
      GoRoute(
        path: '/create-post',
        name: 'create-post',
        redirect: (context, state) {
          // 檢查用戶是否已登入
          final container = ProviderScope.containerOf(context);
          final authState = container.read(authProvider);

          if (!authState.hasValue || authState.value == null) {
            // 用戶未登入，重定向到首頁
            debugPrint('🚫 [Router] 用戶未登入，無法訪問創建貼文頁面');
            return '/home';
          }

          return null; // 已登入，繼續導航
        },
        builder: (context, state) {
          final postId = state.uri.queryParameters['postId'];
          final location = state.uri.queryParameters['location'];
          final geoTag = state.uri.queryParameters['geoTag'];
          final geoTags = state.uri.queryParameters['geoTags']; // 支持多個地理標籤
          final sharedText = state.uri.queryParameters['sharedText']; // 分享的文字內容

          // 如果有 postId，表示是編輯模式
          if (postId != null) {
            // TODO: 從資料庫獲取貼文資料
            return const CreatePostScreen();
          }

          // 如果有 location 參數，表示是從地圖長按創建
          if (location != null) {
            return CreatePostScreen(
              initialLocation: location,
              initialGeoTag: geoTag, // 單個地理標籤（向後兼容）
              initialGeoTags: geoTags, // 多個地理標籤（新功能）
              sharedText: sharedText, // 分享的文字內容
            );
          }

          // 一般創建模式（包括分享模式）
          return CreatePostScreen(
            sharedText: sharedText, // 分享的文字內容
          );
        },
      ),

      GoRoute(
        path: '/post/:postId',
        name: 'post-detail',
        builder: (context, state) {
          final postId = state.pathParameters['postId'];
          if (postId == null) {
            return _buildErrorScreen(Exception('Invalid post ID'));
          }
          final showComments =
              state.uri.queryParameters['showComments'] == 'true';
          return PostDetailPage(
            postId: postId,
          );
        },
      ),

      GoRoute(
        path: '/notifications',
        name: 'notifications',
        builder: (context, state) => const NotificationsScreen(),
      ),

      // 法律文件路由
      GoRoute(
        path: '/legal/privacy',
        name: 'privacy-policy',
        builder: (context, state) => const PrivacyPolicyScreen(),
      ),

      GoRoute(
        path: '/legal/terms',
        name: 'terms-of-service',
        builder: (context, state) => const TermsOfServiceScreen(),
      ),

      // 探索功能相關路由
      GoRoute(
        path: '/country-ranking',
        name: 'country-ranking',
        builder: (context, state) => const CountryRankingScreen(),
      ),

      GoRoute(
        path: '/country-posts/:countryName',
        name: 'country-posts',
        builder: (context, state) {
          final countryName = state.pathParameters['countryName']!;
          final countryCode = state.uri.queryParameters['countryCode'];
          return CountryPostsScreen(
            countryName: countryName,
            countryCode: countryCode,
          );
        },
      ),

      GoRoute(
        path: '/reputation-leaderboard',
        name: 'reputation-leaderboard',
        builder: (context, state) => const ReputationLeaderboardScreen(),
      ),

      // Hashtag 文章列表頁面
      GoRoute(
        path: '/hashtag/:hashtag',
        name: 'hashtag-posts',
        builder: (context, state) {
          final hashtag = state.pathParameters['hashtag']!;
          return HashtagPostsPage(hashtag: hashtag);
        },
      ),

      GoRoute(
        path: '/help',
        name: 'help',
        builder: (context, state) => _buildPlaceholderScreen(
          'Help',
          'help.title',
        ),
      ),

      GoRoute(
        path: '/about',
        name: 'about',
        builder: (context, state) => _buildPlaceholderScreen(
          'About',
          'about.title',
        ),
      ),

      // 管理者路由
      GoRoute(
        path: '/admin',
        name: 'admin',
        builder: (context, state) => const AdminDashboardPage(),
        routes: [
          GoRoute(
            path: 'reports',
            name: 'admin-reports',
            builder: (context, state) => _buildPlaceholderScreen(
              'Reports Management',
              'admin.reports',
            ),
          ),
          GoRoute(
            path: 'fake-accounts',
            name: 'admin-fake-accounts',
            builder: (context, state) => const FakeAccountsManagementPage(),
          ),
          GoRoute(
            path: 'geo-tags',
            name: 'admin-geo-tags',
            builder: (context, state) => _buildPlaceholderScreen(
              'Geo Tags Management',
              'admin.geo_tags',
            ),
          ),
          GoRoute(
            path: 'users',
            name: 'admin-users',
            builder: (context, state) => const UsersManagementPage(),
          ),
          GoRoute(
            path: 'content',
            name: 'admin-content',
            builder: (context, state) => const ContentManagementPage(),
          ),
          GoRoute(
            path: 'settings',
            name: 'admin-settings',
            builder: (context, state) => _buildPlaceholderScreen(
              'Admin Settings',
              'admin.settings',
            ),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) {
      return _buildErrorScreen(state.error);
    },
  );

  // 初始化全局實例
  globalRouter = router;

  // 添加路由變化監聽器用於 Analytics
  router.routerDelegate.addListener(() {
    try {
      final RouteMatchList matchList =
          router.routerDelegate.currentConfiguration;
      final String location = matchList.uri.toString();

      // 直接使用增強的 Analytics 服務記錄路由變化
      enhancedAnalytics.logScreenView(
        screenName: location.replaceAll('/', '_').isEmpty
            ? 'home'
            : location.replaceAll('/', '_'),
        screenClass: location,
        parameters: {
          'path': location,
          'path_parameters': matchList.pathParameters.toString(),
          'query_parameters': matchList.uri.queryParameters.toString(),
        },
      );
    } catch (e) {
      debugPrint('❌ Failed to track route change: $e');
    }
  });

  return router;
});

/// 根據路由路徑獲取底部導航索引
int _getNavigationIndex(String path) {
  debugPrint('🧭 [_getNavigationIndex] 解析路由路径: $path');

  if (path.startsWith('/home')) {
    debugPrint('🧭 [_getNavigationIndex] 返回首页索引: 0');
    return 0;
  }
  if (path.startsWith('/history')) {
    debugPrint('🧭 [_getNavigationIndex] 返回历史索引: 1');
    return 1;
  }
  if (path.startsWith('/explore')) {
    debugPrint('🧭 [_getNavigationIndex] 返回探索索引: 2');
    return 2;
  }
  if (path.startsWith('/favorite')) {
    debugPrint('🧭 [_getNavigationIndex] 返回收藏索引: 3');
    return 3;
  }
  if (path.startsWith('/settings')) {
    debugPrint('🧭 [_getNavigationIndex] 返回设置索引: 4');
    return 4;
  }
  if (path.startsWith('/search')) {
    debugPrint('🧭 [_getNavigationIndex] 返回搜索索引: -1 (特殊处理)');
    return -1; // 搜索页面使用特殊索引，不对应底部导航
  }

  debugPrint('🧭 [_getNavigationIndex] 未匹配路径，返回默认首页索引: 0');
  return 0; // 預設返回首頁
}

/// 建立佔位畫面（開發期間使用）
Widget _buildPlaceholderScreen(
  String title,
  String translationKey, {
  String? subtitle,
}) {
  return Scaffold(
    appBar: AppBar(
      title: Text(title),
      centerTitle: true,
    ),
    body: Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 16),
            const Text(
              '此頁面正在開發中...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                // 可以添加返回邏輯或其他操作
              },
              child: const Text('敬請期待'),
            ),
          ],
        ),
      ),
    ),
  );
}

/// 建立錯誤畫面
Widget _buildErrorScreen(Exception? error) {
  return Scaffold(
    appBar: AppBar(
      title: const Text('頁面錯誤'),
    ),
    body: Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red,
            ),
            const SizedBox(height: 24),
            const Text(
              '頁面載入失敗',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (error != null) ...[
              Text(
                error.toString(),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
            ],
            Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  // 使用 GoRouter 返回首頁
                  context.go('/home');
                },
                child: const Text('返回首頁'),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

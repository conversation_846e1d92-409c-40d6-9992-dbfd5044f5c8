// lib/main.dart

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'app.dart';
import 'firebase_options.dart';
import 'core/database/local_database_manager.dart';
import 'core/utils/debug_logger.dart';
import 'core/services/sharing_service.dart';
import 'core/services/firestore_query_counter.dart';
import 'features/onboarding/presentation/services/onboarding_service.dart';
import 'dart:io';
import 'core/services/ios_share_receive_service.dart';
import 'core/services/enhanced_analytics_service.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'features/notifications/data/services/ios_notification_handler.dart';
import 'features/home/<USER>/services/view_tracking_service.dart';

/// 🚀 自言自語 APP 主入口
/// 負責初始化所有核心服務和依賴
void main() async {
  // 確保 Flutter 綁定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 應用啟動 log
  DebugLogger.lifecycle('🚀 應用開始啟動...');
  DebugLogger.info('Flutter 綁定初始化完成');

  // 🔍 初始化 Firestore 查詢計數器
  FirestoreQueryCounter.initSession();
  DebugLogger.info('🔍 Firestore 查詢計數器已初始化');

  // 設定系統UI樣式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 設定偏好的螢幕方向（僅豎屏）
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  try {
    // 標記應用重啟（用於地圖狀態管理）
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('app_restart_flag', true);
    debugPrint('🗺️ [Main] 標記應用重啟，地圖將使用用戶位置');

    // 初始化多語系支援
    await EasyLocalization.ensureInitialized();

    // 初始化 Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // 初始化 Facebook SDK
    debugPrint('🔵 [Main] 初始化 Facebook SDK');
    await FacebookAuth.instance.webAndDesktopInitialize(
      appId: "950731773813243", // 使用當前配置的 Facebook App ID
      cookie: true,
      xfbml: true,
      version: "v18.0",
    );
    debugPrint('🔵 [Main] Facebook SDK 初始化完成');

    // 🔧 初始化 Debug App Check 來避免 Firebase Auth 錯誤
    try {
      await FirebaseAppCheck.instance.activate(
        androidProvider: AndroidProvider.debug,
        appleProvider: AppleProvider.debug,
        webProvider: ReCaptchaV3Provider('debug'),
      );
      debugPrint('🔧 [Main] App Check Debug 模式已啟用');
    } catch (e) {
      debugPrint('🔧 [Main] App Check 初始化失敗，但繼續執行: $e');
    }

    // 📊 初始化 Analytics 和 Crashlytics
    try {
      // 初始化增強的 Analytics 服務
      await enhancedAnalytics.initialize();
      debugPrint('📊 [Main] Enhanced Analytics 服務初始化完成');

      // 設置 Crashlytics 錯誤處理
      FlutterError.onError =
          FirebaseCrashlytics.instance.recordFlutterFatalError;

      // 設置非同步錯誤處理
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };

      debugPrint('🔥 [Main] Firebase Crashlytics 已設置');
    } catch (e) {
      debugPrint('📊 [Main] Analytics/Crashlytics 初始化失敗，但繼續執行: $e');
    }

    // 初始化本地資料庫
    await LocalDatabaseManager().initialize();

    // 🔥 預初始化 ViewTrackingService（避免首次使用時的延遲）
    try {
      debugPrint('📊 [Main] 預初始化 ViewTrackingService');
      await ViewTrackingService.createWithCache();
      debugPrint('✅ [Main] ViewTrackingService 預初始化完成');
    } catch (e) {
      debugPrint('❌ [Main] ViewTrackingService 預初始化失敗: $e');
    }

    // 初始化分享接收服務
    if (Platform.isIOS) {
      // iOS 使用新的 IOSShareReceiveService
      debugPrint('🍎 [Main] 初始化 iOS 分享接收服務');
      IOSShareReceiveService.instance.initialize();
      debugPrint('🍎 [Main] iOS 分享接收服務初始化完成');

      // 初始化 iOS 通知處理器
      debugPrint('🍎 [Main] 初始化 iOS 通知處理器');
      IOSNotificationHandler.initialize();
      debugPrint('🍎 [Main] iOS 通知處理器初始化完成');
    } else {
      // Android 還是使用原本的 SharingService
      await SharingService.instance.initialize();
      debugPrint('📤 [Main] Android 分享接收服務初始化完成');
    }

    // 啟動 APP
    runApp(
      EasyLocalization(
        supportedLocales: const [
          Locale('zh', 'TW'), // Traditional Chinese
          Locale('zh', 'CN'), // Simplified Chinese
          Locale('en', 'US'), // English
          Locale('es', 'ES'), // Spanish
          Locale('ja', 'JP'), // Japanese
          Locale('de', 'DE'), // German
          Locale('fr', 'FR'), // French
          Locale('ko', 'KR'), // Korean
          Locale('pt', 'BR'), // Portuguese
          Locale('ru', 'RU'), // Russian
        ],
        path: 'assets/translations',
        fallbackLocale: const Locale('zh', 'TW'),
        useFallbackTranslations: true,
        // 讓 EasyLocalization 自動檢測系統語言
        useOnlyLangCode: false,
        child: const ProviderScope(
          child: HisoHisoApp(),
        ),
      ),
    );
  } catch (error, stackTrace) {
    // 初始化失敗處理
    debugPrint('❌ APP 初始化失敗: $error');
    debugPrint('Stack trace: $stackTrace');

    // 顯示錯誤畫面
    runApp(
      MaterialApp(
        home: InitializationErrorScreen(
          error: error.toString(),
          onRetry: () {
            // 重新啟動 APP
            main();
          },
        ),
      ),
    );
  }
}

/// 初始化錯誤畫面
class InitializationErrorScreen extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const InitializationErrorScreen({
    super.key,
    required this.error,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 80,
                color: Colors.red,
              ),
              const SizedBox(height: 24),
              const Text(
                '應用程式初始化失敗',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                error,
                style: const TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('重試'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
